.ds ]W %
.ds ]L
.nh
.TH bjobs 1 "Volclava Version 1.0 - June 2025"
.br
.SH NAME
\fBbjobs\fR - displays information about Lava jobs
.SH SYNOPSIS
.BR
.PP
.PP
\fBbjobs\fR [\fB-a\fR] [\fB-A\fR] [\fB-w\fR | \fB-l\fR] [\fB-J\fR \fIjob_name\fR] 
[\fB-m\fR \fIhost_name\fR | \fB-m\fR \fIhost_group\fR] 
[\fB-N\fR \fIhost_name | \fR\fB-N\fR \fIhost_model | \fR\fB-N \fR\fICPU_factor\fR] [\fB-P\fR\fI project_name\fR] 
[\fB-q\fR \fIqueue_name\fR] [\fB-u \fR\fIuser_name\fR | \fB-u\fR \fIuser_group\fR | \fB-u all\fR]\fB \fR\fIjob_ID ...
.BR
.PP
\fBbjobs\fR [\fB-d\fR] [\fB-p\fR] [\fB-r\fR] [\fB-s\fR] [\fB-A\fR] [\fB-w\fR | \fB-l\fR] \fB \fR\fIjob_ID ...
.BR
.PP
\fBbjobs \fR[\fB-o\fR \fIcustomized_fields\fR] [\fB-json\fR] \fB \fR\fIjob_ID ...
.BR
.PP
\fBbjobs \fR[\fB-h\fR | \fB-V\fR]
.SH DESCRIPTION
.BR
.PP
.PP
\fB\fRDisplays information about jobs.
.PP
By default, displays information about your own pending, running and 
suspended jobs.
.PP
To display older historical information, use bhist.
.SH OPTIONS
.BR
.PP
.TP 
\fB-a
\fR
.IP
Displays information about jobs in all states, including finished jobs 
that finished recently, within an interval specified by CLEAN_PERIOD 
in lsb.params (the default period is 1 hour).


.TP 
\fB-A
\fR
.IP
Displays summarized information about job arrays. If you specify job 
arrays with the job array ID, and also specify -A, do not include the 
index list with the job array ID.

.IP
You can use -w to show the full array specification, if necessary.


.TP 
\fB-d
\fR
.IP
Displays information about jobs that finished recently, within an 
interval specified by CLEAN_PERIOD in lsb.params (the default 
period is 1 hour).

.TP 
\fB-p
\fR
.IP
Displays pending jobs, together with the pending reasons that caused 
each job not to be dispatched during the last dispatch turn. The 
pending reason shows the number of hosts for that reason, or names 
the hosts if -l is also specified. 

.IP
Each pending reason is associated with one or more hosts and it states 
the cause why these hosts are not allocated to run the job. In situations 
where the job requests specific hosts (using bsub -m), users may see 
reasons for unrelated hosts also being displayed, together with the 
reasons associated with the requested hosts. The life cycle of a pending 
reason ends after a new dispatch turn starts. The reason may not reflect 
the current load situation because it could last as long as the interval 
specified by MBD_SLEEP_TIME in lsb.params.

.IP
When the job slot limit is reached for a job array 
(bsub -J "jobArray[indexList]%job_slot_limit") the 
following message is displayed: 

.IP
The job array has reached its job slot limit.


.TP 
\fB-r
\fR
.IP
Displays running jobs.


.TP 
\fB-s
\fR
.IP
Displays suspended jobs, together with the suspending reason that 
caused each job to become suspended. 

.IP
The suspending reason may not remain the same while the job stays 
suspended. For example, a job may have been suspended due to the 
paging rate, but after the paging rate dropped another load index could 
prevent the job from being resumed. The suspending reason will be 
updated according to the load index. The reasons could be as old as 
the time interval specified by SBD_SLEEP_TIME in lsb.params. So the 
reasons shown may not reflect the current load situation. 


.TP 
\fB-w
\fR
.IP
Wide format. Displays job information without truncating fields.


.TP 
\fB-l
\fR
.IP
Long format. Displays detailed information for each job in a multi-line 
format. 

.IP
The -l option displays the following additional information: project 
name, job command, current working directory on the submission 
host, pending and suspending reasons, job status, resource usage, 
resource limits information.

.IP
Use bjobs -A -l to display detailed information for job arrays 
including job array job limit (%\fIjob_limit\fR) if set.


.TP 
\fIjob_ID
\fR
.IP
Displays information about the specified jobs or job arrays.

.IP
If you use -A, specify job array IDs without the index list.


.TP 
\fB-J\fR \fIjob_name
\fR
.IP
Displays information about the specified jobs or job arrays.


.TP 
\fB-m\fR \fIhost_name\fR | \fB-m\fR \fIhost_group\fR  

.IP
Only displays jobs dispatched to the specified hosts.

.IP
To determine the available hosts and host groups, use bhosts and 
bmgroup. 


.TP 
\fB-N\fR \fIhost_name \fR| \fB-N \fR\fIhost_model \fR| \fB-N \fR\fICPU_factor\fR 

.IP
Displays the normalized CPU time consumed by the job. Normalizes 
using the CPU factor specified, or the CPU factor of the host or host 
model specified.


.TP 
\fB-P \fR\fIproject_name 
\fR
.IP
Only displays jobs that belong to the specified project.


.TP 
\fB-q\fR \fIqueue_name 
\fR
.IP
Only displays jobs in the specified queue. 

.IP
The command bqueues returns a list of queues configured in the 
system, and information about the configurations of these queues.


.TP 
\fB-u \fR\fIuser_name\fR | \fB-u\fR \fIuser_group\fR | \fB-u all\fR 

.IP
Only displays jobs that have been submitted by the specified users. The 
keyword all specifies all users.


.TP
\fB-o customized_fields
\fR
.IP
Sets the customized output fields with default delimiter of space character, such as: bjobs -o "jobid USER STAT".


.TP
\fB-json
\fR
.IP
Displays the customized output in JSON format, and -json need be used with -o option, such as: bjobs -o "jobid" -json.


.TP 
\fB-h
\fR
.IP
Prints command usage to stderr and exits. 


.TP 
\fB-V
\fR
.IP
Prints Lava release version to stderr and exits. 


.SH OUTPUT
.BR
.PP
.PP
Pending jobs are displayed in the order in which they will be 
considered for dispatch. Jobs in higher priority queues are displayed 
before those in lower priority queues. Pending jobs in the same priority 
queues are displayed in the order in which they were submitted but 
this order can be changed by using the commands btop or bbot. If 
more than one job is dispatched to a host, the jobs on that host are 
listed in the order in which they will be considered for scheduling on 
this host by their queue priorities and dispatch times. Finished jobs are 
displayed in the order in which they were completed.
.SS Default Display
.BR
.PP
.PP
A listing of jobs is displayed with the following fields: 

.IP
JOBID 
.BR
.RS
.IP
The job ID that Lava assigned to the job. 

.RE

.IP
USER
.BR
.RS
.IP
The user who submitted the job. 

.RE

.IP
STAT
.BR
.RS
.IP
The current status of the job (see JOB STATUS below). 

.RE

.IP
QUEUE
.BR
.RS
.IP
The name of the job queue to which the job belongs. If the queue 
to which the job belongs has been removed from the configuration, 
the queue name will be displayed as lost_and_found. Use bhist 
to get the original queue name. The job in the lost_and_found 
queue will remain pending until it is switched with the bswitch 
command into another queue. 

.RE

.IP
FROM_HOST
.BR
.RS
.IP
The name of the host from which the job was submitted. 

.RE

.IP
EXEC_HOST
.BR
.RS
.IP
The name of one or more hosts on which the job is executing (this 
field is empty if the job has not been dispatched). If the host on 
which the job is running has been removed from the configuration, 
the host name will be displayed as lost_and_found. Use bhist 
to get the original host name. 

.RE

.IP
JOB_NAME 
.BR
.RS
.IP
The job name assigned by the user, or the \fIcommand\fR string 
assigned by default (see bsub (1)). If the job name is too long to 
fit in this field, then only the latter part of the job name is displayed. 

.RE

.IP
SUBMIT_TIME 
.BR
.RS
.IP
The submission time of the job. 

.RE
.SS -l output
.BR
.PP
.PP
If the -l option is specified, the resulting long format listing includes 
the following additional fields: 

.IP
Project
.BR
.RS
.IP
The project the job was submitted from. 

.RE

.IP
Command 
.BR
.RS
.IP
The job command. 

.RE

.IP
CWD 
.BR
.RS
.IP
The current working directory on the submission host. 

.RE

.IP
PENDING REASONS 
.BR
.RS
.IP
The reason the job is in the PEND or PSUSP state. The names of 
the hosts associated with each reason will be displayed when both 
-p and -l options are specified. 

.RE

.IP
SUSPENDING REASONS 
.BR
.RS
.IP
The reason the job is in the USUSP or SSUSP state. 


.IP
loadSched 
.BR
.RS
.IP
The load scheduling thresholds for the job. 

.RE

.IP
loadStop 
.BR
.RS
.IP
The load suspending thresholds for the job. 

.RE
.RE
.RE

.IP
JOB STATUS
.BR
.RS
.IP
Possible values for the status of a job include: 


.IP
PEND 
.BR
.RS
.IP
The job is pending, that is, it has not yet been started. 

.RE

.IP
PSUSP
.BR
.RS
.IP
The job has been suspended, either by its owner or the Lava 
administrator, while pending. 

.RE

.IP
RUN 
.BR
.RS
.IP
the job is currently running. 

.RE

.IP
USUSP 
.BR
.RS
.IP
The job has been suspended, either by its owner or the Lava 
administrator, while running. 

.RE

.IP
SSUSP
.BR
.RS
.IP
The job has been suspended by Lava. The job has been 
suspended by Lava due to either of the following two causes: 

.IP
1) The load conditions on the execution host or hosts have 
exceeded a threshold according to the loadStop vector 
defined for the host or queue.

.IP
2) the run window of the job's queue is closed. See 
bqueues(1), bhosts(1), and lsb.queues(5).

.RE

.IP
DONE
.BR
.RS
.IP
The job has terminated with status of 0. 

.RE

.IP
EXIT 
.BR
.RS
.IP
The job has terminated with a non-zero status - it may have 
been aborted due to an error in its execution, or killed by its 
owner or the Lava administrator. 

.RE

.IP
UNKWN
.BR
.RS
.IP
MBD has lost contact with the SBD on the host on which the 
job runs.

.RE

.IP
ZOMBI
.BR
.RS
.IP
A job will become ZOMBI if:

.IP
- A non-rerunnable job is killed by bkill while the SBD on the 
execution host is unreachable and the job is shown as UNKWN. 

.IP
- The host on which a rerunnable job is running is unavailable 
and the job has been requeued by Lava with a new job ID, as if 
the job were submitted as a new job.

.IP
After the execution host becomes available, Lava will try to kill 
the ZOMBI job. Upon successful termination of the ZOMBI job, 
the job's status will be changed to EXIT. 

.RE
.RE
.RE

.IP
RESOURCE USAGE
.BR
.RS
.IP
The values for the current usage of a job include: 


.IP
CPU time 
.BR
.RS
.IP
Cumulative total CPU time in seconds of all processes in a job. 

.RE

.IP
MEM 
.BR
.RS
.IP
Total resident memory usage of all processes in a job, in MB. 

.RE

.IP
SWAP
.BR
.RS
.IP
Total virtual memory usage of all processes in a job, in MB. 

.RE

.IP
PGID
.BR
.RS
.IP
Currently active process group ID in a job. 

.RE

.IP
PIDs 
.BR
.RS
.IP
Currently active processes in a job. 

.RE
.RE
.RE

.IP
RESOURCE LIMITS
.BR
.RS
.IP
The hard resource limits that are imposed on the jobs in the queue 
(see getrlimit(2) and lsb.queues(5)). These limits are imposed 
on a per-job and a per-process basis. 

.IP
The possible per-job limits are: 

.IP
CPULIMIT

.IP
PROCLIMIT

.IP
MEMLIMIT

.IP
SWAPLIMIT

.IP
PROCESSLIMIT 

.IP
The possible UNIX per-process resource limits are:

.IP
RUNLIMIT

.IP
FILELIMIT

.IP
DATALIMIT

.IP
STACKLIMIT

.IP
CORELIMIT

.IP
If a job submitted to the queue has any of these limits specified (see 
bsub(1)), then the lower of the corresponding job limits and queue 
limits are used for the job. 

.IP
If no resource limit is specified, the resource is assumed to be 
unlimited. 

.RE
.SS Job Array Summary Information
.BR
.PP
.PP
If you use -A, displays summary information about job arrays. The 
following fields are displayed: 

.IP
JOBID 
.BR
.RS
.IP
Job ID of the job array.

.RE

.IP
ARRAY_SPEC 
.BR
.RS
.IP
Array specification in the format of \fIname\fR[\fIindex\fR]. The array 
specification may be truncated, use -w option together with -A to 
show the full array specification. 

.RE

.IP
OWNER 
.BR
.RS
.IP
Owner of the job array.

.RE

.IP
NJOBS 
.BR
.RS
.IP
Number of jobs in the job array. 

.RE

.IP
PEND 
.BR
.RS
.IP
Number of pending jobs of the job array. 

.RE

.IP
RUN 
.BR
.RS
.IP
Number of running jobs of the job array. 

.RE

.IP
DONE 
.BR
.RS
.IP
Number of successfully completed jobs of the job array. 

.RE

.IP
EXIT 
.BR
.RS
.IP
Number of unsuccessfully completed jobs of the job array. 

.RE

.IP
SSUSP 
.BR
.RS
.IP
Number of Lava system suspended jobs of the job array. 

.RE

.IP
USUSP 
.BR
.RS
.IP
Number of user suspended jobs of the job array. 

.RE

.IP
PSUSP 
.BR
.RS
.IP
Number of held jobs of the job array. 

.RE
.SH EXAMPLES
.BR
.PP
.PP
% \fBbjobs -pl
\fR.PP
Displays detailed information about all pending jobs of the invoker. 
.PP
% \fBbjobs -ps
\fR.PP
Display only pending and suspended jobs.
.PP
% \fBbjobs -u all -a
\fR.PP
Displays all jobs of all users.
.PP
% \fBbjobs -d -q short -m apple -u john
\fR.PP
Displays all the recently finished jobs submitted by john to the queue 
short, and executed on the host apple. 
.PP
% \fBbjobs 101 102 203 509
\fR.PP
Display jobs with job_ID 101, 102, 203, and 509. 
.SH SEE ALSO
.BR
.PP
.PP
bsub(1), bkill(1), bhosts(1), bmgroup(1), bqueues(1) 
bhist(1), bresume(1), bstop(1), lsb.params(5), 
mbatchd(8)
