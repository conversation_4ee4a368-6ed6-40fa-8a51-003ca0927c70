# Makefile.in generated by automake 1.16.1 from Makefile.am.
# @configure_input@

# Copyright (C) 1994-2018 Free Software Foundation, Inc.

# This Makefile.in is free software; the Free Software Foundation
# gives unlimited permission to copy and/or distribute it,
# with or without modifications, as long as this notice is preserved.

# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY, to the extent permitted by law; without
# even the implied warranty of MERCHANTABILITY or FITNESS FOR A
# PARTICULAR PURPOSE.

@SET_MAKE@

VPATH = @srcdir@
am__is_gnu_make = { \
  if test -z '$(MAKELEVEL)'; then \
    false; \
  elif test -n '$(MAKE_HOST)'; then \
    true; \
  elif test -n '$(MAKE_VERSION)' && test -n '$(CURDIR)'; then \
    true; \
  else \
    false; \
  fi; \
}
am__make_running_with_option = \
  case $${target_option-} in \
      ?) ;; \
      *) echo "am__make_running_with_option: internal error: invalid" \
              "target option '$${target_option-}' specified" >&2; \
         exit 1;; \
  esac; \
  has_opt=no; \
  sane_makeflags=$$MAKEFLAGS; \
  if $(am__is_gnu_make); then \
    sane_makeflags=$$MFLAGS; \
  else \
    case $$MAKEFLAGS in \
      *\\[\ \	]*) \
        bs=\\; \
        sane_makeflags=`printf '%s\n' "$$MAKEFLAGS" \
          | sed "s/$$bs$$bs[$$bs $$bs	]*//g"`;; \
    esac; \
  fi; \
  skip_next=no; \
  strip_trailopt () \
  { \
    flg=`printf '%s\n' "$$flg" | sed "s/$$1.*$$//"`; \
  }; \
  for flg in $$sane_makeflags; do \
    test $$skip_next = yes && { skip_next=no; continue; }; \
    case $$flg in \
      *=*|--*) continue;; \
        -*I) strip_trailopt 'I'; skip_next=yes;; \
      -*I?*) strip_trailopt 'I';; \
        -*O) strip_trailopt 'O'; skip_next=yes;; \
      -*O?*) strip_trailopt 'O';; \
        -*l) strip_trailopt 'l'; skip_next=yes;; \
      -*l?*) strip_trailopt 'l';; \
      -[dEDm]) skip_next=yes;; \
      -[JT]) skip_next=yes;; \
    esac; \
    case $$flg in \
      *$$target_option*) has_opt=yes; break;; \
    esac; \
  done; \
  test $$has_opt = yes
am__make_dryrun = (target_option=n; $(am__make_running_with_option))
am__make_keepgoing = (target_option=k; $(am__make_running_with_option))
pkgdatadir = $(datadir)/@PACKAGE@
pkgincludedir = $(includedir)/@PACKAGE@
pkglibdir = $(libdir)/@PACKAGE@
pkglibexecdir = $(libexecdir)/@PACKAGE@
am__cd = CDPATH="$${ZSH_VERSION+.}$(PATH_SEPARATOR)" && cd
install_sh_DATA = $(install_sh) -c -m 644
install_sh_PROGRAM = $(install_sh) -c
install_sh_SCRIPT = $(install_sh) -c
INSTALL_HEADER = $(INSTALL_DATA)
transform = $(program_transform_name)
NORMAL_INSTALL = :
PRE_INSTALL = :
POST_INSTALL = :
NORMAL_UNINSTALL = :
PRE_UNINSTALL = :
POST_UNINSTALL = :
build_triplet = @build@
host_triplet = @host@
target_triplet = @target@
bin_PROGRAMS = badmin$(EXEEXT) bkill$(EXEEXT) bparams$(EXEEXT) \
	brestart$(EXEEXT) btop$(EXEEXT) bbot$(EXEEXT) bmgroup$(EXEEXT) \
	bpeek$(EXEEXT) brun$(EXEEXT) busers$(EXEEXT) bhosts$(EXEEXT) \
	bmig$(EXEEXT) bqueues$(EXEEXT) bsub$(EXEEXT) bjobs$(EXEEXT) \
	bmod$(EXEEXT) brequeue$(EXEEXT) bswitch$(EXEEXT)
@CYGWIN_FALSE@am__append_1 = -lnsl
@CYGWIN_FALSE@am__append_2 = -lnsl
@CYGWIN_FALSE@am__append_3 = -lnsl
@CYGWIN_FALSE@am__append_4 = -lnsl
@CYGWIN_FALSE@am__append_5 = -lnsl
@CYGWIN_FALSE@am__append_6 = -lnsl
@CYGWIN_FALSE@am__append_7 = -lnsl
@CYGWIN_FALSE@am__append_8 = -lnsl
@CYGWIN_FALSE@am__append_9 = -lnsl
@CYGWIN_FALSE@am__append_10 = -lnsl
@CYGWIN_FALSE@am__append_11 = -lnsl
@CYGWIN_FALSE@am__append_12 = -lnsl
@CYGWIN_FALSE@am__append_13 = -lnsl
@CYGWIN_FALSE@am__append_14 = -lnsl
@CYGWIN_FALSE@am__append_15 = -lnsl
@CYGWIN_FALSE@am__append_16 = -lnsl
@CYGWIN_FALSE@am__append_17 = -lnsl
@CYGWIN_FALSE@am__append_18 = -lnsl
subdir = lsbatch/cmd
ACLOCAL_M4 = $(top_srcdir)/aclocal.m4
am__aclocal_m4_deps = $(top_srcdir)/configure.ac
am__configure_deps = $(am__aclocal_m4_deps) $(CONFIGURE_DEPENDENCIES) \
	$(ACLOCAL_M4)
DIST_COMMON = $(srcdir)/Makefile.am $(am__DIST_COMMON)
mkinstalldirs = $(install_sh) -d
CONFIG_HEADER = $(top_builddir)/config.h
CONFIG_CLEAN_FILES =
CONFIG_CLEAN_VPATH_FILES =
am__installdirs = "$(DESTDIR)$(bindir)"
PROGRAMS = $(bin_PROGRAMS)
am_badmin_OBJECTS = badmin.$(OBJEXT) cmd.bqc.$(OBJEXT) \
	cmd.hist.$(OBJEXT) cmd.bhc.$(OBJEXT) cmd.misc.$(OBJEXT) \
	cmd.job.$(OBJEXT) cmd.prt.$(OBJEXT)
badmin_OBJECTS = $(am_badmin_OBJECTS)
am__DEPENDENCIES_1 =
badmin_DEPENDENCIES = ../../lsf/lsadm/startup.o ../lib/liblsbatch.a \
	../../lsf/lib/liblsf.a ../../lsf/intlib/liblsfint.a \
	$(am__DEPENDENCIES_1)
am_bbot_OBJECTS = bbot.$(OBJEXT) cmd.move.$(OBJEXT) \
	cmd.jobid.$(OBJEXT) cmd.misc.$(OBJEXT) cmd.prt.$(OBJEXT) \
	cmd.err.$(OBJEXT)
bbot_OBJECTS = $(am_bbot_OBJECTS)
bbot_DEPENDENCIES = ../lib/liblsbatch.a ../../lsf/lib/liblsf.a \
	../../lsf/intlib/liblsfint.a $(am__DEPENDENCIES_1)
am_bhosts_OBJECTS = bhosts.$(OBJEXT) cmd.prt.$(OBJEXT) \
	cmd.misc.$(OBJEXT)
bhosts_OBJECTS = $(am_bhosts_OBJECTS)
bhosts_DEPENDENCIES = ../lib/liblsbatch.a ../../lsf/lib/liblsf.a \
	../../lsf/intlib/liblsfint.a $(am__DEPENDENCIES_1)
am_bjobs_OBJECTS = bjobs.$(OBJEXT) cmd.prt.$(OBJEXT) cmd.err.$(OBJEXT) \
	cmd.job.$(OBJEXT) cmd.jobid.$(OBJEXT) cmd.misc.$(OBJEXT) \
	cJSON.$(OBJEXT)
bjobs_OBJECTS = $(am_bjobs_OBJECTS)
bjobs_DEPENDENCIES = ../lib/liblsbatch.a ../../lsf/lib/liblsf.a \
	../../lsf/intlib/liblsfint.a $(am__DEPENDENCIES_1)
am_bkill_OBJECTS = bkill.$(OBJEXT) cmd.sig.$(OBJEXT) \
	cmd.jobid.$(OBJEXT) cmd.err.$(OBJEXT)
bkill_OBJECTS = $(am_bkill_OBJECTS)
bkill_DEPENDENCIES = ../lib/liblsbatch.a ../../lsf/lib/liblsf.a \
	../../lsf/intlib/liblsfint.a $(am__DEPENDENCIES_1)
am_bmgroup_OBJECTS = bmgroup.$(OBJEXT) cmd.misc.$(OBJEXT)
bmgroup_OBJECTS = $(am_bmgroup_OBJECTS)
bmgroup_DEPENDENCIES = ../lib/liblsbatch.a ../../lsf/lib/liblsf.a \
	../../lsf/intlib/liblsfint.a $(am__DEPENDENCIES_1)
am_bmig_OBJECTS = bmig.$(OBJEXT) cmd.jobid.$(OBJEXT) cmd.err.$(OBJEXT)
bmig_OBJECTS = $(am_bmig_OBJECTS)
bmig_DEPENDENCIES = ../lib/liblsbatch.a ../../lsf/lib/liblsf.a \
	../../lsf/intlib/liblsfint.a $(am__DEPENDENCIES_1)
am_bmod_OBJECTS = bmod.$(OBJEXT) cmd.sub.$(OBJEXT) cmd.jobid.$(OBJEXT) \
	cmd.err.$(OBJEXT) batch_job_collector.$(OBJEXT) \
	batch_transmitter.$(OBJEXT) batch_error_handler.$(OBJEXT)
bmod_OBJECTS = $(am_bmod_OBJECTS)
bmod_DEPENDENCIES = ../lib/liblsbatch.a ../../lsf/lib/liblsf.a \
	../../lsf/intlib/liblsfint.a $(am__DEPENDENCIES_1)
am_bparams_OBJECTS = bparams.$(OBJEXT)
bparams_OBJECTS = $(am_bparams_OBJECTS)
bparams_DEPENDENCIES = ../lib/liblsbatch.a ../../lsf/lib/liblsf.a \
	../../lsf/intlib/liblsfint.a $(am__DEPENDENCIES_1)
am_bpeek_OBJECTS = bpeek.$(OBJEXT) cmd.err.$(OBJEXT) \
	cmd.jobid.$(OBJEXT) cmd.misc.$(OBJEXT) cmd.prt.$(OBJEXT)
bpeek_OBJECTS = $(am_bpeek_OBJECTS)
bpeek_DEPENDENCIES = ../lib/liblsbatch.a ../../lsf/lib/liblsf.a \
	../../lsf/intlib/liblsfint.a $(am__DEPENDENCIES_1)
am_bqueues_OBJECTS = bqueues.$(OBJEXT) cmd.prt.$(OBJEXT) \
	cmd.misc.$(OBJEXT)
bqueues_OBJECTS = $(am_bqueues_OBJECTS)
bqueues_DEPENDENCIES = ../lib/liblsbatch.a ../../lsf/lib/liblsf.a \
	../../lsf/intlib/liblsfint.a $(am__DEPENDENCIES_1)
am_brequeue_OBJECTS = brequeue.$(OBJEXT) cmd.jobid.$(OBJEXT) \
	cmd.err.$(OBJEXT)
brequeue_OBJECTS = $(am_brequeue_OBJECTS)
brequeue_DEPENDENCIES = ../lib/liblsbatch.a ../../lsf/lib/liblsf.a \
	../../lsf/intlib/liblsfint.a $(am__DEPENDENCIES_1)
am_brestart_OBJECTS = brestart.$(OBJEXT) cmd.sub.$(OBJEXT) \
	cmd.jobid.$(OBJEXT) cmd.err.$(OBJEXT) \
	batch_job_collector.$(OBJEXT) batch_transmitter.$(OBJEXT)
brestart_OBJECTS = $(am_brestart_OBJECTS)
brestart_DEPENDENCIES = ../lib/liblsbatch.a ../../lsf/lib/liblsf.a \
	../../lsf/intlib/liblsfint.a $(am__DEPENDENCIES_1)
am_brun_OBJECTS = brun.$(OBJEXT) cmd.jobid.$(OBJEXT) cmd.err.$(OBJEXT)
brun_OBJECTS = $(am_brun_OBJECTS)
brun_DEPENDENCIES = ../lib/liblsbatch.a ../../lsf/lib/liblsf.a \
	../../lsf/intlib/liblsfint.a $(am__DEPENDENCIES_1)
am_bsub_OBJECTS = bsub.$(OBJEXT) cmd.sub.$(OBJEXT) cmd.jobid.$(OBJEXT) \
	cmd.err.$(OBJEXT) batch_job_collector.$(OBJEXT) \
	batch_transmitter.$(OBJEXT)
bsub_OBJECTS = $(am_bsub_OBJECTS)
bsub_DEPENDENCIES = ../lib/liblsbatch.a ../../lsf/lib/liblsf.a \
	../../lsf/intlib/liblsfint.a $(am__DEPENDENCIES_1)
am_bswitch_OBJECTS = bswitch.$(OBJEXT) cmd.jobid.$(OBJEXT) \
	cmd.err.$(OBJEXT)
bswitch_OBJECTS = $(am_bswitch_OBJECTS)
bswitch_DEPENDENCIES = ../lib/liblsbatch.a ../../lsf/lib/liblsf.a \
	../../lsf/intlib/liblsfint.a $(am__DEPENDENCIES_1)
am_btop_OBJECTS = btop.$(OBJEXT) cmd.move.$(OBJEXT) \
	cmd.jobid.$(OBJEXT) cmd.misc.$(OBJEXT) cmd.prt.$(OBJEXT) \
	cmd.err.$(OBJEXT)
btop_OBJECTS = $(am_btop_OBJECTS)
btop_DEPENDENCIES = ../lib/liblsbatch.a ../../lsf/lib/liblsf.a \
	../../lsf/intlib/liblsfint.a $(am__DEPENDENCIES_1)
am_busers_OBJECTS = busers.$(OBJEXT) cmd.misc.$(OBJEXT)
busers_OBJECTS = $(am_busers_OBJECTS)
busers_DEPENDENCIES = ../lib/liblsbatch.a ../../lsf/lib/liblsf.a \
	../../lsf/intlib/liblsfint.a $(am__DEPENDENCIES_1)
AM_V_P = $(am__v_P_@AM_V@)
am__v_P_ = $(am__v_P_@AM_DEFAULT_V@)
am__v_P_0 = false
am__v_P_1 = :
AM_V_GEN = $(am__v_GEN_@AM_V@)
am__v_GEN_ = $(am__v_GEN_@AM_DEFAULT_V@)
am__v_GEN_0 = @echo "  GEN     " $@;
am__v_GEN_1 = 
AM_V_at = $(am__v_at_@AM_V@)
am__v_at_ = $(am__v_at_@AM_DEFAULT_V@)
am__v_at_0 = @
am__v_at_1 = 
DEFAULT_INCLUDES = -I.@am__isrc@ -I$(top_builddir)
depcomp = $(SHELL) $(top_srcdir)/depcomp
am__maybe_remake_depfiles = depfiles
am__depfiles_remade = ./$(DEPDIR)/badmin.Po \
	./$(DEPDIR)/batch_error_handler.Po \
	./$(DEPDIR)/batch_job_collector.Po \
	./$(DEPDIR)/batch_transmitter.Po ./$(DEPDIR)/bbot.Po \
	./$(DEPDIR)/bhosts.Po ./$(DEPDIR)/bjobs.Po \
	./$(DEPDIR)/bkill.Po ./$(DEPDIR)/bmgroup.Po \
	./$(DEPDIR)/bmig.Po ./$(DEPDIR)/bmod.Po ./$(DEPDIR)/bparams.Po \
	./$(DEPDIR)/bpeek.Po ./$(DEPDIR)/bqueues.Po \
	./$(DEPDIR)/brequeue.Po ./$(DEPDIR)/brestart.Po \
	./$(DEPDIR)/brun.Po ./$(DEPDIR)/bsub.Po ./$(DEPDIR)/bswitch.Po \
	./$(DEPDIR)/btop.Po ./$(DEPDIR)/busers.Po ./$(DEPDIR)/cJSON.Po \
	./$(DEPDIR)/cmd.bhc.Po ./$(DEPDIR)/cmd.bqc.Po \
	./$(DEPDIR)/cmd.err.Po ./$(DEPDIR)/cmd.hist.Po \
	./$(DEPDIR)/cmd.job.Po ./$(DEPDIR)/cmd.jobid.Po \
	./$(DEPDIR)/cmd.misc.Po ./$(DEPDIR)/cmd.move.Po \
	./$(DEPDIR)/cmd.prt.Po ./$(DEPDIR)/cmd.sig.Po \
	./$(DEPDIR)/cmd.sub.Po
am__mv = mv -f
COMPILE = $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) \
	$(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS)
AM_V_CC = $(am__v_CC_@AM_V@)
am__v_CC_ = $(am__v_CC_@AM_DEFAULT_V@)
am__v_CC_0 = @echo "  CC      " $@;
am__v_CC_1 = 
CCLD = $(CC)
LINK = $(CCLD) $(AM_CFLAGS) $(CFLAGS) $(AM_LDFLAGS) $(LDFLAGS) -o $@
AM_V_CCLD = $(am__v_CCLD_@AM_V@)
am__v_CCLD_ = $(am__v_CCLD_@AM_DEFAULT_V@)
am__v_CCLD_0 = @echo "  CCLD    " $@;
am__v_CCLD_1 = 
SOURCES = $(badmin_SOURCES) $(bbot_SOURCES) $(bhosts_SOURCES) \
	$(bjobs_SOURCES) $(bkill_SOURCES) $(bmgroup_SOURCES) \
	$(bmig_SOURCES) $(bmod_SOURCES) $(bparams_SOURCES) \
	$(bpeek_SOURCES) $(bqueues_SOURCES) $(brequeue_SOURCES) \
	$(brestart_SOURCES) $(brun_SOURCES) $(bsub_SOURCES) \
	$(bswitch_SOURCES) $(btop_SOURCES) $(busers_SOURCES)
DIST_SOURCES = $(badmin_SOURCES) $(bbot_SOURCES) $(bhosts_SOURCES) \
	$(bjobs_SOURCES) $(bkill_SOURCES) $(bmgroup_SOURCES) \
	$(bmig_SOURCES) $(bmod_SOURCES) $(bparams_SOURCES) \
	$(bpeek_SOURCES) $(bqueues_SOURCES) $(brequeue_SOURCES) \
	$(brestart_SOURCES) $(brun_SOURCES) $(bsub_SOURCES) \
	$(bswitch_SOURCES) $(btop_SOURCES) $(busers_SOURCES)
am__can_run_installinfo = \
  case $$AM_UPDATE_INFO_DIR in \
    n|no|NO) false;; \
    *) (install-info --version) >/dev/null 2>&1;; \
  esac
am__tagged_files = $(HEADERS) $(SOURCES) $(TAGS_FILES) $(LISP)
# Read a list of newline-separated strings from the standard input,
# and print each of them once, without duplicates.  Input order is
# *not* preserved.
am__uniquify_input = $(AWK) '\
  BEGIN { nonempty = 0; } \
  { items[$$0] = 1; nonempty = 1; } \
  END { if (nonempty) { for (i in items) print i; }; } \
'
# Make sure the list of sources is unique.  This is necessary because,
# e.g., the same source file might be shared among _SOURCES variables
# for different programs/libraries.
am__define_uniq_tagged_files = \
  list='$(am__tagged_files)'; \
  unique=`for i in $$list; do \
    if test -f "$$i"; then echo $$i; else echo $(srcdir)/$$i; fi; \
  done | $(am__uniquify_input)`
ETAGS = etags
CTAGS = ctags
am__DIST_COMMON = $(srcdir)/Makefile.in $(top_srcdir)/depcomp \
	ChangeLog
DISTFILES = $(DIST_COMMON) $(DIST_SOURCES) $(TEXINFOS) $(EXTRA_DIST)
ACLOCAL = @ACLOCAL@
AMTAR = @AMTAR@
AM_DEFAULT_VERBOSITY = @AM_DEFAULT_VERBOSITY@
AUTOCONF = @AUTOCONF@
AUTOHEADER = @AUTOHEADER@
AUTOMAKE = @AUTOMAKE@
AWK = @AWK@
CC = @CC@
CCDEPMODE = @CCDEPMODE@
CFLAGS = @CFLAGS@
CPP = @CPP@
CPPFLAGS = @CPPFLAGS@
CYGPATH_W = @CYGPATH_W@
DEFS = @DEFS@
DEPDIR = @DEPDIR@
ECHO_C = @ECHO_C@
ECHO_N = @ECHO_N@
ECHO_T = @ECHO_T@
EGREP = @EGREP@
EXEEXT = @EXEEXT@
GREP = @GREP@
INSTALL = @INSTALL@
INSTALL_DATA = @INSTALL_DATA@
INSTALL_PROGRAM = @INSTALL_PROGRAM@
INSTALL_SCRIPT = @INSTALL_SCRIPT@
INSTALL_STRIP_PROGRAM = @INSTALL_STRIP_PROGRAM@
LDFLAGS = @LDFLAGS@
LEX = @LEX@
LEXLIB = @LEXLIB@
LEX_OUTPUT_ROOT = @LEX_OUTPUT_ROOT@
LIBOBJS = @LIBOBJS@
LIBS = @LIBS@
LN_S = @LN_S@
LTLIBOBJS = @LTLIBOBJS@
MAKEINFO = @MAKEINFO@
MKDIR_P = @MKDIR_P@
OBJEXT = @OBJEXT@
PACKAGE = @PACKAGE@
PACKAGE_BUGREPORT = @PACKAGE_BUGREPORT@
PACKAGE_NAME = @PACKAGE_NAME@
PACKAGE_STRING = @PACKAGE_STRING@
PACKAGE_TARNAME = @PACKAGE_TARNAME@
PACKAGE_URL = @PACKAGE_URL@
PACKAGE_VERSION = @PACKAGE_VERSION@
PATH_SEPARATOR = @PATH_SEPARATOR@
RANLIB = @RANLIB@
SET_MAKE = @SET_MAKE@
SHELL = @SHELL@
STRIP = @STRIP@
VERSION = @VERSION@
YACC = @YACC@
YFLAGS = @YFLAGS@
abs_builddir = @abs_builddir@
abs_srcdir = @abs_srcdir@
abs_top_builddir = @abs_top_builddir@
abs_top_srcdir = @abs_top_srcdir@
ac_ct_CC = @ac_ct_CC@
am__include = @am__include@
am__leading_dot = @am__leading_dot@
am__quote = @am__quote@
am__tar = @am__tar@
am__untar = @am__untar@
bindir = @bindir@
build = @build@
build_alias = @build_alias@
build_cpu = @build_cpu@
build_os = @build_os@
build_vendor = @build_vendor@
builddir = @builddir@
datadir = @datadir@
datarootdir = @datarootdir@
docdir = @docdir@
dvidir = @dvidir@
exec_prefix = @exec_prefix@
host = @host@
host_alias = @host_alias@
host_cpu = @host_cpu@
host_os = @host_os@
host_vendor = @host_vendor@
htmldir = @htmldir@
includedir = @includedir@
infodir = @infodir@
install_sh = @install_sh@
libdir = @libdir@
libexecdir = @libexecdir@
localedir = @localedir@
localstatedir = @localstatedir@
mandir = @mandir@
mkdir_p = @mkdir_p@
oldincludedir = @oldincludedir@
pdfdir = @pdfdir@
prefix = @prefix@
program_transform_name = @program_transform_name@
psdir = @psdir@
runstatedir = @runstatedir@
sbindir = @sbindir@
sharedstatedir = @sharedstatedir@
srcdir = @srcdir@
sysconfdir = @sysconfdir@
target = @target@
target_alias = @target_alias@
target_cpu = @target_cpu@
target_os = @target_os@
target_vendor = @target_vendor@
top_build_prefix = @top_build_prefix@
top_builddir = @top_builddir@
top_srcdir = @top_srcdir@
volclavaadmin = @volclavaadmin@
volclavacluster = @volclavacluster@
volclavadmin = @volclavadmin@

#
# Copyright (C) 2021-2025 Bytedance Ltd. and/or its affiliates
# Copyright (C) openlava foundation
#
INCLUDES = -I$(top_srcdir)/lsf  -I$(top_srcdir)/lsf/lib \
           -I$(top_srcdir)/lsbatch  -I$(top_srcdir)/lsbatch/lib -I./

badmin_SOURCES = badmin.c cmd.bqc.c cmd.hist.c \
	cmd.bhc.c cmd.misc.c cmd.job.c cmd.prt.c \
	badmin.h cmd.h

badmin_LDADD = ../../lsf/lsadm/startup.o ../lib/liblsbatch.a \
	../../lsf/lib/liblsf.a ../../lsf/intlib/liblsfint.a -lm \
	$(am__append_1)
bkill_SOURCES = bkill.c cmd.sig.c cmd.jobid.c cmd.err.c
bkill_LDADD = ../lib/liblsbatch.a ../../lsf/lib/liblsf.a \
	../../lsf/intlib/liblsfint.a -lm $(am__append_2)
bparams_SOURCES = bparams.c cmd.h
bparams_LDADD = ../lib/liblsbatch.a ../../lsf/lib/liblsf.a \
	../../lsf/intlib/liblsfint.a -lm $(am__append_3)
brestart_SOURCES = brestart.c cmd.sub.c cmd.jobid.c \
	cmd.err.c cmd.h \
	batch_job_collector.c batch_job_collector.h \
	batch_transmitter.c batch_transmitter.h

brestart_LDADD = ../lib/liblsbatch.a ../../lsf/lib/liblsf.a \
	../../lsf/intlib/liblsfint.a -lm $(am__append_4)
btop_SOURCES = btop.c cmd.move.c cmd.jobid.c cmd.misc.c \
	 cmd.prt.c cmd.err.c cmd.h

btop_LDADD = ../lib/liblsbatch.a ../../lsf/lib/liblsf.a \
	../../lsf/intlib/liblsfint.a -lm $(am__append_5)
bbot_SOURCES = bbot.c cmd.move.c cmd.jobid.c cmd.misc.c \
	 cmd.prt.c cmd.err.c cmd.h

bbot_LDADD = ../lib/liblsbatch.a ../../lsf/lib/liblsf.a \
	../../lsf/intlib/liblsfint.a -lm $(am__append_6)
bmgroup_SOURCES = bmgroup.c cmd.misc.c cmd.h
bmgroup_LDADD = ../lib/liblsbatch.a ../../lsf/lib/liblsf.a \
	../../lsf/intlib/liblsfint.a -lm $(am__append_7)
bpeek_SOURCES = bpeek.c cmd.err.c cmd.jobid.c cmd.misc.c cmd.prt.c cmd.h
bpeek_LDADD = ../lib/liblsbatch.a ../../lsf/lib/liblsf.a \
	../../lsf/intlib/liblsfint.a -lm $(am__append_8)
brun_SOURCES = brun.c cmd.jobid.c cmd.err.c cmd.h
brun_LDADD = ../lib/liblsbatch.a ../../lsf/lib/liblsf.a \
	../../lsf/intlib/liblsfint.a -lm $(am__append_9)
busers_SOURCES = busers.c cmd.misc.c
busers_LDADD = ../lib/liblsbatch.a ../../lsf/lib/liblsf.a \
	../../lsf/intlib/liblsfint.a -lm $(am__append_10)
bhosts_SOURCES = bhosts.c cmd.prt.c cmd.misc.c cmd.h
bhosts_LDADD = ../lib/liblsbatch.a ../../lsf/lib/liblsf.a \
	../../lsf/intlib/liblsfint.a -lm $(am__append_11)
bmig_SOURCES = bmig.c cmd.jobid.c cmd.err.c cmd.h
bmig_LDADD = ../lib/liblsbatch.a ../../lsf/lib/liblsf.a \
	../../lsf/intlib/liblsfint.a -lm $(am__append_12)
bqueues_SOURCES = bqueues.c cmd.prt.c cmd.misc.c cmd.h
bqueues_LDADD = ../lib/liblsbatch.a ../../lsf/lib/liblsf.a \
	../../lsf/intlib/liblsfint.a -lm $(am__append_13)
bsub_SOURCES = bsub.c cmd.sub.c cmd.jobid.c cmd.err.c cmd.h \
	batch_job_collector.c batch_job_collector.h \
	batch_transmitter.c batch_transmitter.h

bsub_LDADD = ../lib/liblsbatch.a ../../lsf/lib/liblsf.a \
	../../lsf/intlib/liblsfint.a -lm $(am__append_14)
bjobs_SOURCES = bjobs.c cmd.prt.c cmd.err.c cmd.job.c \
	cmd.jobid.c cmd.misc.c cmd.h cJSON.c cJSON.h

bjobs_LDADD = ../lib/liblsbatch.a ../../lsf/lib/liblsf.a \
	../../lsf/intlib/liblsfint.a -lm $(am__append_15)
bmod_SOURCES = bmod.c cmd.sub.c cmd.jobid.c cmd.err.c cmd.h \
	batch_job_collector.c batch_job_collector.h \
	batch_transmitter.c batch_transmitter.h \
	batch_error_handler.c batch_error_handler.h

bmod_LDADD = ../lib/liblsbatch.a ../../lsf/lib/liblsf.a \
	../../lsf/intlib/liblsfint.a -lm $(am__append_16)
brequeue_SOURCES = brequeue.c cmd.jobid.c cmd.err.c cmd.h
brequeue_LDADD = ../lib/liblsbatch.a ../../lsf/lib/liblsf.a \
	../../lsf/intlib/liblsfint.a -lm $(am__append_17)
bswitch_SOURCES = bswitch.c cmd.jobid.c cmd.err.c cmd.h
bswitch_LDADD = ../lib/liblsbatch.a ../../lsf/lib/liblsf.a \
	../../lsf/intlib/liblsfint.a -lm $(am__append_18)
all: all-am

.SUFFIXES:
.SUFFIXES: .c .o .obj
$(srcdir)/Makefile.in:  $(srcdir)/Makefile.am  $(am__configure_deps)
	@for dep in $?; do \
	  case '$(am__configure_deps)' in \
	    *$$dep*) \
	      ( cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh ) \
	        && { if test -f $@; then exit 0; else break; fi; }; \
	      exit 1;; \
	  esac; \
	done; \
	echo ' cd $(top_srcdir) && $(AUTOMAKE) --gnu lsbatch/cmd/Makefile'; \
	$(am__cd) $(top_srcdir) && \
	  $(AUTOMAKE) --gnu lsbatch/cmd/Makefile
Makefile: $(srcdir)/Makefile.in $(top_builddir)/config.status
	@case '$?' in \
	  *config.status*) \
	    cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh;; \
	  *) \
	    echo ' cd $(top_builddir) && $(SHELL) ./config.status $(subdir)/$@ $(am__maybe_remake_depfiles)'; \
	    cd $(top_builddir) && $(SHELL) ./config.status $(subdir)/$@ $(am__maybe_remake_depfiles);; \
	esac;

$(top_builddir)/config.status: $(top_srcdir)/configure $(CONFIG_STATUS_DEPENDENCIES)
	cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh

$(top_srcdir)/configure:  $(am__configure_deps)
	cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh
$(ACLOCAL_M4):  $(am__aclocal_m4_deps)
	cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh
$(am__aclocal_m4_deps):
install-binPROGRAMS: $(bin_PROGRAMS)
	@$(NORMAL_INSTALL)
	@list='$(bin_PROGRAMS)'; test -n "$(bindir)" || list=; \
	if test -n "$$list"; then \
	  echo " $(MKDIR_P) '$(DESTDIR)$(bindir)'"; \
	  $(MKDIR_P) "$(DESTDIR)$(bindir)" || exit 1; \
	fi; \
	for p in $$list; do echo "$$p $$p"; done | \
	sed 's/$(EXEEXT)$$//' | \
	while read p p1; do if test -f $$p \
	  ; then echo "$$p"; echo "$$p"; else :; fi; \
	done | \
	sed -e 'p;s,.*/,,;n;h' \
	    -e 's|.*|.|' \
	    -e 'p;x;s,.*/,,;s/$(EXEEXT)$$//;$(transform);s/$$/$(EXEEXT)/' | \
	sed 'N;N;N;s,\n, ,g' | \
	$(AWK) 'BEGIN { files["."] = ""; dirs["."] = 1 } \
	  { d=$$3; if (dirs[d] != 1) { print "d", d; dirs[d] = 1 } \
	    if ($$2 == $$4) files[d] = files[d] " " $$1; \
	    else { print "f", $$3 "/" $$4, $$1; } } \
	  END { for (d in files) print "f", d, files[d] }' | \
	while read type dir files; do \
	    if test "$$dir" = .; then dir=; else dir=/$$dir; fi; \
	    test -z "$$files" || { \
	      echo " $(INSTALL_PROGRAM_ENV) $(INSTALL_PROGRAM) $$files '$(DESTDIR)$(bindir)$$dir'"; \
	      $(INSTALL_PROGRAM_ENV) $(INSTALL_PROGRAM) $$files "$(DESTDIR)$(bindir)$$dir" || exit $$?; \
	    } \
	; done

uninstall-binPROGRAMS:
	@$(NORMAL_UNINSTALL)
	@list='$(bin_PROGRAMS)'; test -n "$(bindir)" || list=; \
	files=`for p in $$list; do echo "$$p"; done | \
	  sed -e 'h;s,^.*/,,;s/$(EXEEXT)$$//;$(transform)' \
	      -e 's/$$/$(EXEEXT)/' \
	`; \
	test -n "$$list" || exit 0; \
	echo " ( cd '$(DESTDIR)$(bindir)' && rm -f" $$files ")"; \
	cd "$(DESTDIR)$(bindir)" && rm -f $$files

clean-binPROGRAMS:
	-test -z "$(bin_PROGRAMS)" || rm -f $(bin_PROGRAMS)

badmin$(EXEEXT): $(badmin_OBJECTS) $(badmin_DEPENDENCIES) $(EXTRA_badmin_DEPENDENCIES) 
	@rm -f badmin$(EXEEXT)
	$(AM_V_CCLD)$(LINK) $(badmin_OBJECTS) $(badmin_LDADD) $(LIBS)

bbot$(EXEEXT): $(bbot_OBJECTS) $(bbot_DEPENDENCIES) $(EXTRA_bbot_DEPENDENCIES) 
	@rm -f bbot$(EXEEXT)
	$(AM_V_CCLD)$(LINK) $(bbot_OBJECTS) $(bbot_LDADD) $(LIBS)

bhosts$(EXEEXT): $(bhosts_OBJECTS) $(bhosts_DEPENDENCIES) $(EXTRA_bhosts_DEPENDENCIES) 
	@rm -f bhosts$(EXEEXT)
	$(AM_V_CCLD)$(LINK) $(bhosts_OBJECTS) $(bhosts_LDADD) $(LIBS)

bjobs$(EXEEXT): $(bjobs_OBJECTS) $(bjobs_DEPENDENCIES) $(EXTRA_bjobs_DEPENDENCIES) 
	@rm -f bjobs$(EXEEXT)
	$(AM_V_CCLD)$(LINK) $(bjobs_OBJECTS) $(bjobs_LDADD) $(LIBS)

bkill$(EXEEXT): $(bkill_OBJECTS) $(bkill_DEPENDENCIES) $(EXTRA_bkill_DEPENDENCIES) 
	@rm -f bkill$(EXEEXT)
	$(AM_V_CCLD)$(LINK) $(bkill_OBJECTS) $(bkill_LDADD) $(LIBS)

bmgroup$(EXEEXT): $(bmgroup_OBJECTS) $(bmgroup_DEPENDENCIES) $(EXTRA_bmgroup_DEPENDENCIES) 
	@rm -f bmgroup$(EXEEXT)
	$(AM_V_CCLD)$(LINK) $(bmgroup_OBJECTS) $(bmgroup_LDADD) $(LIBS)

bmig$(EXEEXT): $(bmig_OBJECTS) $(bmig_DEPENDENCIES) $(EXTRA_bmig_DEPENDENCIES) 
	@rm -f bmig$(EXEEXT)
	$(AM_V_CCLD)$(LINK) $(bmig_OBJECTS) $(bmig_LDADD) $(LIBS)

bmod$(EXEEXT): $(bmod_OBJECTS) $(bmod_DEPENDENCIES) $(EXTRA_bmod_DEPENDENCIES) 
	@rm -f bmod$(EXEEXT)
	$(AM_V_CCLD)$(LINK) $(bmod_OBJECTS) $(bmod_LDADD) $(LIBS)

bparams$(EXEEXT): $(bparams_OBJECTS) $(bparams_DEPENDENCIES) $(EXTRA_bparams_DEPENDENCIES) 
	@rm -f bparams$(EXEEXT)
	$(AM_V_CCLD)$(LINK) $(bparams_OBJECTS) $(bparams_LDADD) $(LIBS)

bpeek$(EXEEXT): $(bpeek_OBJECTS) $(bpeek_DEPENDENCIES) $(EXTRA_bpeek_DEPENDENCIES) 
	@rm -f bpeek$(EXEEXT)
	$(AM_V_CCLD)$(LINK) $(bpeek_OBJECTS) $(bpeek_LDADD) $(LIBS)

bqueues$(EXEEXT): $(bqueues_OBJECTS) $(bqueues_DEPENDENCIES) $(EXTRA_bqueues_DEPENDENCIES) 
	@rm -f bqueues$(EXEEXT)
	$(AM_V_CCLD)$(LINK) $(bqueues_OBJECTS) $(bqueues_LDADD) $(LIBS)

brequeue$(EXEEXT): $(brequeue_OBJECTS) $(brequeue_DEPENDENCIES) $(EXTRA_brequeue_DEPENDENCIES) 
	@rm -f brequeue$(EXEEXT)
	$(AM_V_CCLD)$(LINK) $(brequeue_OBJECTS) $(brequeue_LDADD) $(LIBS)

brestart$(EXEEXT): $(brestart_OBJECTS) $(brestart_DEPENDENCIES) $(EXTRA_brestart_DEPENDENCIES) 
	@rm -f brestart$(EXEEXT)
	$(AM_V_CCLD)$(LINK) $(brestart_OBJECTS) $(brestart_LDADD) $(LIBS)

brun$(EXEEXT): $(brun_OBJECTS) $(brun_DEPENDENCIES) $(EXTRA_brun_DEPENDENCIES) 
	@rm -f brun$(EXEEXT)
	$(AM_V_CCLD)$(LINK) $(brun_OBJECTS) $(brun_LDADD) $(LIBS)

bsub$(EXEEXT): $(bsub_OBJECTS) $(bsub_DEPENDENCIES) $(EXTRA_bsub_DEPENDENCIES) 
	@rm -f bsub$(EXEEXT)
	$(AM_V_CCLD)$(LINK) $(bsub_OBJECTS) $(bsub_LDADD) $(LIBS)

bswitch$(EXEEXT): $(bswitch_OBJECTS) $(bswitch_DEPENDENCIES) $(EXTRA_bswitch_DEPENDENCIES) 
	@rm -f bswitch$(EXEEXT)
	$(AM_V_CCLD)$(LINK) $(bswitch_OBJECTS) $(bswitch_LDADD) $(LIBS)

btop$(EXEEXT): $(btop_OBJECTS) $(btop_DEPENDENCIES) $(EXTRA_btop_DEPENDENCIES) 
	@rm -f btop$(EXEEXT)
	$(AM_V_CCLD)$(LINK) $(btop_OBJECTS) $(btop_LDADD) $(LIBS)

busers$(EXEEXT): $(busers_OBJECTS) $(busers_DEPENDENCIES) $(EXTRA_busers_DEPENDENCIES) 
	@rm -f busers$(EXEEXT)
	$(AM_V_CCLD)$(LINK) $(busers_OBJECTS) $(busers_LDADD) $(LIBS)

mostlyclean-compile:
	-rm -f *.$(OBJEXT)

distclean-compile:
	-rm -f *.tab.c

@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/badmin.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/batch_error_handler.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/batch_job_collector.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/batch_transmitter.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/bbot.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/bhosts.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/bjobs.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/bkill.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/bmgroup.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/bmig.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/bmod.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/bparams.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/bpeek.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/bqueues.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/brequeue.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/brestart.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/brun.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/bsub.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/bswitch.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/btop.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/busers.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/cJSON.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/cmd.bhc.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/cmd.bqc.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/cmd.err.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/cmd.hist.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/cmd.job.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/cmd.jobid.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/cmd.misc.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/cmd.move.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/cmd.prt.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/cmd.sig.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/cmd.sub.Po@am__quote@ # am--include-marker

$(am__depfiles_remade):
	@$(MKDIR_P) $(@D)
	@echo '# dummy' >$@-t && $(am__mv) $@-t $@

am--depfiles: $(am__depfiles_remade)

.c.o:
@am__fastdepCC_TRUE@	$(AM_V_CC)$(COMPILE) -MT $@ -MD -MP -MF $(DEPDIR)/$*.Tpo -c -o $@ $<
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/$*.Tpo $(DEPDIR)/$*.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='$<' object='$@' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(COMPILE) -c -o $@ $<

.c.obj:
@am__fastdepCC_TRUE@	$(AM_V_CC)$(COMPILE) -MT $@ -MD -MP -MF $(DEPDIR)/$*.Tpo -c -o $@ `$(CYGPATH_W) '$<'`
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/$*.Tpo $(DEPDIR)/$*.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='$<' object='$@' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(COMPILE) -c -o $@ `$(CYGPATH_W) '$<'`

ID: $(am__tagged_files)
	$(am__define_uniq_tagged_files); mkid -fID $$unique
tags: tags-am
TAGS: tags

tags-am: $(TAGS_DEPENDENCIES) $(am__tagged_files)
	set x; \
	here=`pwd`; \
	$(am__define_uniq_tagged_files); \
	shift; \
	if test -z "$(ETAGS_ARGS)$$*$$unique"; then :; else \
	  test -n "$$unique" || unique=$$empty_fix; \
	  if test $$# -gt 0; then \
	    $(ETAGS) $(ETAGSFLAGS) $(AM_ETAGSFLAGS) $(ETAGS_ARGS) \
	      "$$@" $$unique; \
	  else \
	    $(ETAGS) $(ETAGSFLAGS) $(AM_ETAGSFLAGS) $(ETAGS_ARGS) \
	      $$unique; \
	  fi; \
	fi
ctags: ctags-am

CTAGS: ctags
ctags-am: $(TAGS_DEPENDENCIES) $(am__tagged_files)
	$(am__define_uniq_tagged_files); \
	test -z "$(CTAGS_ARGS)$$unique" \
	  || $(CTAGS) $(CTAGSFLAGS) $(AM_CTAGSFLAGS) $(CTAGS_ARGS) \
	     $$unique

GTAGS:
	here=`$(am__cd) $(top_builddir) && pwd` \
	  && $(am__cd) $(top_srcdir) \
	  && gtags -i $(GTAGS_ARGS) "$$here"
cscopelist: cscopelist-am

cscopelist-am: $(am__tagged_files)
	list='$(am__tagged_files)'; \
	case "$(srcdir)" in \
	  [\\/]* | ?:[\\/]*) sdir="$(srcdir)" ;; \
	  *) sdir=$(subdir)/$(srcdir) ;; \
	esac; \
	for i in $$list; do \
	  if test -f "$$i"; then \
	    echo "$(subdir)/$$i"; \
	  else \
	    echo "$$sdir/$$i"; \
	  fi; \
	done >> $(top_builddir)/cscope.files

distclean-tags:
	-rm -f TAGS ID GTAGS GRTAGS GSYMS GPATH tags

distdir: $(BUILT_SOURCES)
	$(MAKE) $(AM_MAKEFLAGS) distdir-am

distdir-am: $(DISTFILES)
	@srcdirstrip=`echo "$(srcdir)" | sed 's/[].[^$$\\*]/\\\\&/g'`; \
	topsrcdirstrip=`echo "$(top_srcdir)" | sed 's/[].[^$$\\*]/\\\\&/g'`; \
	list='$(DISTFILES)'; \
	  dist_files=`for file in $$list; do echo $$file; done | \
	  sed -e "s|^$$srcdirstrip/||;t" \
	      -e "s|^$$topsrcdirstrip/|$(top_builddir)/|;t"`; \
	case $$dist_files in \
	  */*) $(MKDIR_P) `echo "$$dist_files" | \
			   sed '/\//!d;s|^|$(distdir)/|;s,/[^/]*$$,,' | \
			   sort -u` ;; \
	esac; \
	for file in $$dist_files; do \
	  if test -f $$file || test -d $$file; then d=.; else d=$(srcdir); fi; \
	  if test -d $$d/$$file; then \
	    dir=`echo "/$$file" | sed -e 's,/[^/]*$$,,'`; \
	    if test -d "$(distdir)/$$file"; then \
	      find "$(distdir)/$$file" -type d ! -perm -700 -exec chmod u+rwx {} \;; \
	    fi; \
	    if test -d $(srcdir)/$$file && test $$d != $(srcdir); then \
	      cp -fpR $(srcdir)/$$file "$(distdir)$$dir" || exit 1; \
	      find "$(distdir)/$$file" -type d ! -perm -700 -exec chmod u+rwx {} \;; \
	    fi; \
	    cp -fpR $$d/$$file "$(distdir)$$dir" || exit 1; \
	  else \
	    test -f "$(distdir)/$$file" \
	    || cp -p $$d/$$file "$(distdir)/$$file" \
	    || exit 1; \
	  fi; \
	done
check-am: all-am
check: check-am
all-am: Makefile $(PROGRAMS)
installdirs:
	for dir in "$(DESTDIR)$(bindir)"; do \
	  test -z "$$dir" || $(MKDIR_P) "$$dir"; \
	done
install: install-am
install-exec: install-exec-am
install-data: install-data-am
uninstall: uninstall-am

install-am: all-am
	@$(MAKE) $(AM_MAKEFLAGS) install-exec-am install-data-am

installcheck: installcheck-am
install-strip:
	if test -z '$(STRIP)'; then \
	  $(MAKE) $(AM_MAKEFLAGS) INSTALL_PROGRAM="$(INSTALL_STRIP_PROGRAM)" \
	    install_sh_PROGRAM="$(INSTALL_STRIP_PROGRAM)" INSTALL_STRIP_FLAG=-s \
	      install; \
	else \
	  $(MAKE) $(AM_MAKEFLAGS) INSTALL_PROGRAM="$(INSTALL_STRIP_PROGRAM)" \
	    install_sh_PROGRAM="$(INSTALL_STRIP_PROGRAM)" INSTALL_STRIP_FLAG=-s \
	    "INSTALL_PROGRAM_ENV=STRIPPROG='$(STRIP)'" install; \
	fi
mostlyclean-generic:

clean-generic:

distclean-generic:
	-test -z "$(CONFIG_CLEAN_FILES)" || rm -f $(CONFIG_CLEAN_FILES)
	-test . = "$(srcdir)" || test -z "$(CONFIG_CLEAN_VPATH_FILES)" || rm -f $(CONFIG_CLEAN_VPATH_FILES)

maintainer-clean-generic:
	@echo "This command is intended for maintainers to use"
	@echo "it deletes files that may require special tools to rebuild."
clean: clean-am

clean-am: clean-binPROGRAMS clean-generic mostlyclean-am

distclean: distclean-am
		-rm -f ./$(DEPDIR)/badmin.Po
	-rm -f ./$(DEPDIR)/batch_error_handler.Po
	-rm -f ./$(DEPDIR)/batch_job_collector.Po
	-rm -f ./$(DEPDIR)/batch_transmitter.Po
	-rm -f ./$(DEPDIR)/bbot.Po
	-rm -f ./$(DEPDIR)/bhosts.Po
	-rm -f ./$(DEPDIR)/bjobs.Po
	-rm -f ./$(DEPDIR)/bkill.Po
	-rm -f ./$(DEPDIR)/bmgroup.Po
	-rm -f ./$(DEPDIR)/bmig.Po
	-rm -f ./$(DEPDIR)/bmod.Po
	-rm -f ./$(DEPDIR)/bparams.Po
	-rm -f ./$(DEPDIR)/bpeek.Po
	-rm -f ./$(DEPDIR)/bqueues.Po
	-rm -f ./$(DEPDIR)/brequeue.Po
	-rm -f ./$(DEPDIR)/brestart.Po
	-rm -f ./$(DEPDIR)/brun.Po
	-rm -f ./$(DEPDIR)/bsub.Po
	-rm -f ./$(DEPDIR)/bswitch.Po
	-rm -f ./$(DEPDIR)/btop.Po
	-rm -f ./$(DEPDIR)/busers.Po
	-rm -f ./$(DEPDIR)/cJSON.Po
	-rm -f ./$(DEPDIR)/cmd.bhc.Po
	-rm -f ./$(DEPDIR)/cmd.bqc.Po
	-rm -f ./$(DEPDIR)/cmd.err.Po
	-rm -f ./$(DEPDIR)/cmd.hist.Po
	-rm -f ./$(DEPDIR)/cmd.job.Po
	-rm -f ./$(DEPDIR)/cmd.jobid.Po
	-rm -f ./$(DEPDIR)/cmd.misc.Po
	-rm -f ./$(DEPDIR)/cmd.move.Po
	-rm -f ./$(DEPDIR)/cmd.prt.Po
	-rm -f ./$(DEPDIR)/cmd.sig.Po
	-rm -f ./$(DEPDIR)/cmd.sub.Po
	-rm -f Makefile
distclean-am: clean-am distclean-compile distclean-generic \
	distclean-tags

dvi: dvi-am

dvi-am:

html: html-am

html-am:

info: info-am

info-am:

install-data-am: install-data-local

install-dvi: install-dvi-am

install-dvi-am:

install-exec-am: install-binPROGRAMS

install-html: install-html-am

install-html-am:

install-info: install-info-am

install-info-am:

install-man:

install-pdf: install-pdf-am

install-pdf-am:

install-ps: install-ps-am

install-ps-am:

installcheck-am:

maintainer-clean: maintainer-clean-am
		-rm -f ./$(DEPDIR)/badmin.Po
	-rm -f ./$(DEPDIR)/batch_error_handler.Po
	-rm -f ./$(DEPDIR)/batch_job_collector.Po
	-rm -f ./$(DEPDIR)/batch_transmitter.Po
	-rm -f ./$(DEPDIR)/bbot.Po
	-rm -f ./$(DEPDIR)/bhosts.Po
	-rm -f ./$(DEPDIR)/bjobs.Po
	-rm -f ./$(DEPDIR)/bkill.Po
	-rm -f ./$(DEPDIR)/bmgroup.Po
	-rm -f ./$(DEPDIR)/bmig.Po
	-rm -f ./$(DEPDIR)/bmod.Po
	-rm -f ./$(DEPDIR)/bparams.Po
	-rm -f ./$(DEPDIR)/bpeek.Po
	-rm -f ./$(DEPDIR)/bqueues.Po
	-rm -f ./$(DEPDIR)/brequeue.Po
	-rm -f ./$(DEPDIR)/brestart.Po
	-rm -f ./$(DEPDIR)/brun.Po
	-rm -f ./$(DEPDIR)/bsub.Po
	-rm -f ./$(DEPDIR)/bswitch.Po
	-rm -f ./$(DEPDIR)/btop.Po
	-rm -f ./$(DEPDIR)/busers.Po
	-rm -f ./$(DEPDIR)/cJSON.Po
	-rm -f ./$(DEPDIR)/cmd.bhc.Po
	-rm -f ./$(DEPDIR)/cmd.bqc.Po
	-rm -f ./$(DEPDIR)/cmd.err.Po
	-rm -f ./$(DEPDIR)/cmd.hist.Po
	-rm -f ./$(DEPDIR)/cmd.job.Po
	-rm -f ./$(DEPDIR)/cmd.jobid.Po
	-rm -f ./$(DEPDIR)/cmd.misc.Po
	-rm -f ./$(DEPDIR)/cmd.move.Po
	-rm -f ./$(DEPDIR)/cmd.prt.Po
	-rm -f ./$(DEPDIR)/cmd.sig.Po
	-rm -f ./$(DEPDIR)/cmd.sub.Po
	-rm -f Makefile
maintainer-clean-am: distclean-am maintainer-clean-generic

mostlyclean: mostlyclean-am

mostlyclean-am: mostlyclean-compile mostlyclean-generic

pdf: pdf-am

pdf-am:

ps: ps-am

ps-am:

uninstall-am: uninstall-binPROGRAMS

.MAKE: install-am install-strip

.PHONY: CTAGS GTAGS TAGS all all-am am--depfiles check check-am clean \
	clean-binPROGRAMS clean-generic cscopelist-am ctags ctags-am \
	distclean distclean-compile distclean-generic distclean-tags \
	distdir dvi dvi-am html html-am info info-am install \
	install-am install-binPROGRAMS install-data install-data-am \
	install-data-local install-dvi install-dvi-am install-exec \
	install-exec-am install-html install-html-am install-info \
	install-info-am install-man install-pdf install-pdf-am \
	install-ps install-ps-am install-strip installcheck \
	installcheck-am installdirs maintainer-clean \
	maintainer-clean-generic mostlyclean mostlyclean-compile \
	mostlyclean-generic pdf pdf-am ps ps-am tags tags-am uninstall \
	uninstall-am uninstall-binPROGRAMS

.PRECIOUS: Makefile


install-data-local:
	cd "$(DESTDIR)$(bindir)" && ln -sf bkill bstop
	cd "$(DESTDIR)$(bindir)" && ln -sf bkill bresume
	cd "$(DESTDIR)$(bindir)" && ln -sf bkill bchkpnt
	cd "$(DESTDIR)$(bindir)" && ln -sf bmgroup bugroup

etags :
	etags *.[hc] ../*.h ../lib/*.[hc] ../../lsf/*.h ../../lib/*.[hc] \
	../../intlib/*.[hc]

# Tell versions [3.59,3.63) of GNU make to not export all variables.
# Otherwise a system limit (for SysV at least) may be exceeded.
.NOEXPORT:
