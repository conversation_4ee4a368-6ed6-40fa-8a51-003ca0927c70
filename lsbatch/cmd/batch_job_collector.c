/**
 * @file batch_job_collector.c
 * @brief 批量作业收集器实现 - 增强版本
 * 
 * 主要功能：
 * 1. 解析pack文件，收集作业信息
 * 2. 复用现有LSF参数解析逻辑
 * 3. 增强错误处理和内存管理
 * 4. 支持大文件和大批量作业
 * 5. 添加详细的进度反馈
 */

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <errno.h>
#include <time.h>
#include <ctype.h>
#include <sys/stat.h>

#include "cmd.h"
#include "../lsbatch.h"
#include "../lib/batch_memory.h"

/* 内部常量定义 */
#define JOB_COLLECTOR_MAX_LINE_LENGTH    8192   /* 增加行长度限制 */
#define JOB_COLLECTOR_INITIAL_CAPACITY   100
#define JOB_COLLECTOR_GROWTH_FACTOR      2
#define JOB_COLLECTOR_PROGRESS_INTERVAL  100    /* 每100个作业报告一次进度 */

/* 作业收集器配置 */
typedef struct {
    int max_jobs_count;         /* 最大作业数 */
    int max_memory_usage_mb;    /* 最大内存使用(MB) */
    int fail_on_first_error;    /* 遇到第一个错误就停止 */
    int collect_all_errors;     /* 收集所有错误信息 */
    int enable_detailed_logging;/* 启用详细日志 */
    char *debug_log_file;       /* 调试日志文件 */
} job_collector_config_t;

/* 批量作业集合结构 */
typedef struct {
    struct submit **job_requests_array;    /* 作业请求数组 */
    int *job_line_numbers;                 /* 每个作业的行号 */
    char **original_command_lines;         /* 原始命令行 */
    int total_jobs_count;                  /* 总作业数 */
    int total_jobs_capacity;               /* 总容量 */
    int collect_original_commands;         /* 是否收集原始命令 */
    time_t collection_start_time;          /* 收集开始时间 */
    char source_file_path[PATH_MAX];       /* 源文件路径 */
    struct batch_memory_pool *memory_pool; /* 内存池 */
} batch_job_collection_t;

/* 内部函数声明 */
static int parse_job_line(const char *line, int line_number, struct submit *job_req);
static int expand_job_capacity(batch_job_collection_t *collection, int new_capacity);
static int is_empty_or_comment_line(const char *line);
static void report_progress(int processed, int total, const char *phase);
static int validate_job_request(const struct submit *job_req, int line_number);
static char **split_command_line_enhanced(const char *cmdline, int *argc);
static void cleanup_job_request(struct submit *job_req);

/* 外部函数声明 - 来自现有LSF代码 */
extern int fillReq(int argc, char **argv, int operate, struct submit *req, int isInPackFile);
extern char **split_commandline(const char *cmdline, int *argc);
extern char *getNextLineC_(FILE *fp, int *lineNum, int flag);

/**
 * @brief 创建默认作业收集器配置
 */
int job_collector_create_default_config(job_collector_config_t *config)
{
    if (!config) {
        return -1;
    }
    
    memset(config, 0, sizeof(job_collector_config_t));
    
    config->max_jobs_count = 10000;
    config->max_memory_usage_mb = 100;
    config->fail_on_first_error = 0;  /* 默认不在第一个错误时停止 */
    config->collect_all_errors = 1;   /* 默认收集所有错误 */
    config->enable_detailed_logging = 0;
    config->debug_log_file = NULL;
    
    return 0;
}

/**
 * @brief 从pack文件收集作业 - 增强版本
 */
int job_collector_collect_from_pack_file(const char *pack_file_path,
                                          batch_job_collection_t *collection,
                                          const job_collector_config_t *config)
{
    FILE *pack_file = NULL;
    char line_buffer[JOB_COLLECTOR_MAX_LINE_LENGTH];
    job_collector_config_t default_config;
    const job_collector_config_t *active_config = config;
    int line_number = 0;
    int valid_jobs_collected = 0;
    int result = -1;
    struct stat file_stat;

    /* 参数验证 */
    if (!pack_file_path || !collection) {
        fprintf(stderr, "❌ job_collector_collect_from_pack_file: 无效参数\n");
        return -1;
    }

    /* 检查文件是否存在和可读 */
    if (stat(pack_file_path, &file_stat) != 0) {
        fprintf(stderr, "❌ 无法访问文件 %s: %s\n", pack_file_path, strerror(errno));
        return -1;
    }

    /* 使用默认配置 */
    if (!active_config) {
        job_collector_create_default_config(&default_config);
        active_config = &default_config;
    }

    /* 初始化集合结构 */
    memset(collection, 0, sizeof(batch_job_collection_t));
    collection->total_jobs_capacity = JOB_COLLECTOR_INITIAL_CAPACITY;
    collection->job_requests_array = calloc(collection->total_jobs_capacity, sizeof(struct submit*));
    collection->job_line_numbers = calloc(collection->total_jobs_capacity, sizeof(int));
    collection->original_command_lines = calloc(collection->total_jobs_capacity, sizeof(char*));
    
    if (!collection->job_requests_array || !collection->job_line_numbers || !collection->original_command_lines) {
        fprintf(stderr, "❌ 内存分配失败\n");
        goto cleanup;
    }

    collection->collect_original_commands = 1;
    collection->collection_start_time = time(NULL);
    strncpy(collection->source_file_path, pack_file_path, sizeof(collection->source_file_path) - 1);

    /* 初始化内存池 */
    collection->memory_pool = malloc(sizeof(struct batch_memory_pool));
    if (!collection->memory_pool || 
        batch_memory_pool_init(collection->memory_pool, active_config->max_memory_usage_mb * 1024 * 1024) != 0) {
        fprintf(stderr, "❌ 内存池初始化失败\n");
        goto cleanup;
    }

    /* 打开pack文件 */
    pack_file = fopen(pack_file_path, "r");
    if (!pack_file) {
        fprintf(stderr, "❌ 无法打开pack文件: %s (%s)\n", pack_file_path, strerror(errno));
        goto cleanup;
    }

    fprintf(stderr, "📦 开始解析pack文件: %s\n", pack_file_path);
    fprintf(stderr, "📊 文件大小: %ld 字节\n", file_stat.st_size);

    /* 逐行解析作业 */
    while (fgets(line_buffer, sizeof(line_buffer), pack_file)) {
        line_number++;
        
        /* 去除行尾换行符 */
        char *newline = strchr(line_buffer, '\n');
        if (newline) *newline = '\0';
        
        /* 跳过空行和注释行 */
        if (is_empty_or_comment_line(line_buffer)) {
            continue;
        }
        
        /* 检查作业数量限制 */
        if (valid_jobs_collected >= active_config->max_jobs_count) {
            fprintf(stderr, "⚠️  达到最大作业数限制 %d，停止解析\n", active_config->max_jobs_count);
            break;
        }
        
        /* 扩展容量（如果需要） */
        if (valid_jobs_collected >= collection->total_jobs_capacity) {
            int new_capacity = collection->total_jobs_capacity * JOB_COLLECTOR_GROWTH_FACTOR;
            if (expand_job_capacity(collection, new_capacity) != 0) {
                fprintf(stderr, "❌ 扩展作业容量失败\n");
                if (active_config->fail_on_first_error) {
                    goto cleanup;
                }
                continue;
            }
        }
        
        /* 分配新的作业请求结构 */
        struct submit *job_req = batch_memory_pool_alloc(collection->memory_pool, sizeof(struct submit));
        if (!job_req) {
            fprintf(stderr, "❌ 行 %d: 作业请求内存分配失败\n", line_number);
            if (active_config->fail_on_first_error) {
                goto cleanup;
            }
            continue;
        }
        
        /* 解析作业行 */
        if (parse_job_line(line_buffer, line_number, job_req) != 0) {
            fprintf(stderr, "❌ 行 %d: 作业解析失败: %s\n", line_number, line_buffer);
            if (active_config->fail_on_first_error) {
                goto cleanup;
            }
            continue;
        }
        
        /* 验证作业请求 */
        if (validate_job_request(job_req, line_number) != 0) {
            fprintf(stderr, "❌ 行 %d: 作业验证失败\n", line_number);
            cleanup_job_request(job_req);
            if (active_config->fail_on_first_error) {
                goto cleanup;
            }
            continue;
        }
        
        /* 添加到集合中 */
        collection->job_requests_array[valid_jobs_collected] = job_req;
        collection->job_line_numbers[valid_jobs_collected] = line_number;
        
        /* 保存原始命令行（如果需要） */
        if (collection->collect_original_commands) {
            collection->original_command_lines[valid_jobs_collected] = 
                batch_memory_pool_alloc(collection->memory_pool, strlen(line_buffer) + 1);
            if (collection->original_command_lines[valid_jobs_collected]) {
                strcpy(collection->original_command_lines[valid_jobs_collected], line_buffer);
            }
        }
        
        valid_jobs_collected++;
        
        /* 进度报告 */
        if (valid_jobs_collected % JOB_COLLECTOR_PROGRESS_INTERVAL == 0) {
            report_progress(valid_jobs_collected, -1, "解析作业");
        }
    }
    
    collection->total_jobs_count = valid_jobs_collected;
    
    /* 最终统计 */
    fprintf(stderr, "✅ 作业收集完成\n");
    fprintf(stderr, "📊 总行数: %d\n", line_number);
    fprintf(stderr, "📊 有效作业: %d\n", valid_jobs_collected);
    fprintf(stderr, "📊 耗时: %ld 秒\n", time(NULL) - collection->collection_start_time);
    
    result = 0;

cleanup:
    if (pack_file) {
        fclose(pack_file);
    }
    
    if (result != 0 && collection) {
        /* 清理失败时的资源 */
        if (collection->memory_pool) {
            batch_memory_pool_cleanup(collection->memory_pool);
            free(collection->memory_pool);
            collection->memory_pool = NULL;
        }
        
        if (collection->job_requests_array) {
            free(collection->job_requests_array);
            collection->job_requests_array = NULL;
        }
        
        if (collection->job_line_numbers) {
            free(collection->job_line_numbers);
            collection->job_line_numbers = NULL;
        }
        
        if (collection->original_command_lines) {
            free(collection->original_command_lines);
            collection->original_command_lines = NULL;
        }
    }
    
    return result;
}

/**
 * @brief 解析单行作业命令
 */
static int parse_job_line(const char *line, int line_number, struct submit *job_req)
{
    char **argv = NULL;
    int argc = 0;
    int result = -1;
    char temp_buffer[JOB_COLLECTOR_MAX_LINE_LENGTH + 32];

    if (!line || !job_req) {
        return -1;
    }

    /* 构造完整的bsub命令行 */
    snprintf(temp_buffer, sizeof(temp_buffer), "bsub %s", line);

    /* 分割命令行 */
    argv = split_command_line_enhanced(temp_buffer, &argc);
    if (!argv || argc < 2) {
        fprintf(stderr, "❌ 行 %d: 命令行分割失败\n", line_number);
        goto cleanup;
    }

    /* 重置optind以便重新解析 */
    optind = 1;

    /* 使用现有的fillReq函数解析参数 */
    if (fillReq(argc, argv, CMD_BSUB, job_req, 1) < 0) {
        fprintf(stderr, "❌ 行 %d: 参数解析失败\n", line_number);
        goto cleanup;
    }

    /* 检查不支持的选项 */
    if (job_req->options2 & SUB2_BSUB_BLOCK) {
        fprintf(stderr, "❌ 行 %d: -K 选项在pack文件中不支持\n", line_number);
        goto cleanup;
    }

    if (job_req->options & SUB_INTERACTIVE) {
        fprintf(stderr, "❌ 行 %d: -I 选项在pack文件中不支持\n", line_number);
        goto cleanup;
    }

    if (job_req->options & SUB_PACK) {
        fprintf(stderr, "❌ 行 %d: -pack 选项在pack文件中不支持\n", line_number);
        goto cleanup;
    }

    result = 0;

cleanup:
    if (argv) {
        int i;
        for (i = 0; i < argc; i++) {
            if (argv[i]) {
                free(argv[i]);
            }
        }
        free(argv);
    }

    return result;
}

/**
 * @brief 扩展作业容量
 */
static int expand_job_capacity(batch_job_collection_t *collection, int new_capacity)
{
    if (!collection || new_capacity <= collection->total_jobs_capacity) {
        return -1;
    }

    /* 重新分配作业请求数组 */
    struct submit **new_job_array = realloc(collection->job_requests_array,
                                           new_capacity * sizeof(struct submit*));
    if (!new_job_array) {
        return -1;
    }
    collection->job_requests_array = new_job_array;

    /* 重新分配行号数组 */
    int *new_line_numbers = realloc(collection->job_line_numbers,
                                   new_capacity * sizeof(int));
    if (!new_line_numbers) {
        return -1;
    }
    collection->job_line_numbers = new_line_numbers;

    /* 重新分配原始命令行数组 */
    char **new_command_lines = realloc(collection->original_command_lines,
                                      new_capacity * sizeof(char*));
    if (!new_command_lines) {
        return -1;
    }
    collection->original_command_lines = new_command_lines;

    /* 初始化新分配的空间 */
    memset(&collection->job_requests_array[collection->total_jobs_capacity], 0,
           (new_capacity - collection->total_jobs_capacity) * sizeof(struct submit*));
    memset(&collection->job_line_numbers[collection->total_jobs_capacity], 0,
           (new_capacity - collection->total_jobs_capacity) * sizeof(int));
    memset(&collection->original_command_lines[collection->total_jobs_capacity], 0,
           (new_capacity - collection->total_jobs_capacity) * sizeof(char*));

    collection->total_jobs_capacity = new_capacity;

    return 0;
}

/**
 * @brief 检查是否为空行或注释行
 */
static int is_empty_or_comment_line(const char *line)
{
    if (!line) {
        return 1;
    }

    /* 跳过前导空白字符 */
    while (isspace(*line)) {
        line++;
    }

    /* 空行或注释行 */
    return (*line == '\0' || *line == '#');
}

/**
 * @brief 报告进度
 */
static void report_progress(int processed, int total, const char *phase)
{
    if (total > 0) {
        double percentage = (double)processed / total * 100.0;
        fprintf(stderr, "🔄 %s: %d/%d (%.1f%%)\n", phase, processed, total, percentage);
    } else {
        fprintf(stderr, "🔄 %s: %d 个作业\n", phase, processed);
    }
}

/**
 * @brief 验证作业请求
 */
static int validate_job_request(const struct submit *job_req, int line_number)
{
    if (!job_req) {
        return -1;
    }

    /* 检查必需的命令 */
    if (!job_req->command || strlen(job_req->command) == 0) {
        fprintf(stderr, "❌ 行 %d: 缺少作业命令\n", line_number);
        return -1;
    }

    /* 检查资源限制的合理性 */
    if (job_req->numProcessors < 0) {
        fprintf(stderr, "❌ 行 %d: 处理器数量不能为负数\n", line_number);
        return -1;
    }

    /* 检查时间限制 */
    if (job_req->beginTime > 0 && job_req->termTime > 0 &&
        job_req->beginTime >= job_req->termTime) {
        fprintf(stderr, "❌ 行 %d: 开始时间不能晚于结束时间\n", line_number);
        return -1;
    }

    return 0;
}

/**
 * @brief 增强版命令行分割函数
 */
static char **split_command_line_enhanced(const char *cmdline, int *argc)
{
    /* 首先尝试使用现有的split_commandline函数 */
    char **result = split_commandline(cmdline, argc);

    if (result) {
        return result;
    }

    /* 如果现有函数失败，使用简单的分割逻辑作为后备 */
    if (!cmdline || !argc) {
        return NULL;
    }

    /* 简单的空格分割实现 */
    char *cmd_copy = strdup(cmdline);
    if (!cmd_copy) {
        return NULL;
    }

    char **argv = malloc(64 * sizeof(char*));  /* 最多64个参数 */
    if (!argv) {
        free(cmd_copy);
        return NULL;
    }

    *argc = 0;
    char *token = strtok(cmd_copy, " \t");
    while (token && *argc < 63) {
        argv[*argc] = strdup(token);
        if (!argv[*argc]) {
            /* 清理已分配的内存 */
            int i;
            for (i = 0; i < *argc; i++) {
                free(argv[i]);
            }
            free(argv);
            free(cmd_copy);
            return NULL;
        }
        (*argc)++;
        token = strtok(NULL, " \t");
    }

    argv[*argc] = NULL;
    free(cmd_copy);

    return argv;
}

/**
 * @brief 清理作业请求
 */
static void cleanup_job_request(struct submit *job_req)
{
    if (!job_req) {
        return;
    }

    /* 这里可以添加特定的清理逻辑 */
    /* 注意：由于使用内存池，大部分内存会在池清理时统一释放 */
}

/**
 * @brief 清理批量作业集合
 */
void batch_job_collection_cleanup(batch_job_collection_t *collection)
{
    if (!collection) {
        return;
    }

    fprintf(stderr, "🧹 清理作业集合...\n");

    /* 清理内存池 */
    if (collection->memory_pool) {
        batch_memory_pool_cleanup(collection->memory_pool);
        free(collection->memory_pool);
        collection->memory_pool = NULL;
    }

    /* 清理数组 */
    if (collection->job_requests_array) {
        free(collection->job_requests_array);
        collection->job_requests_array = NULL;
    }

    if (collection->job_line_numbers) {
        free(collection->job_line_numbers);
        collection->job_line_numbers = NULL;
    }

    if (collection->original_command_lines) {
        free(collection->original_command_lines);
        collection->original_command_lines = NULL;
    }

    /* 重置计数器 */
    collection->total_jobs_count = 0;
    collection->total_jobs_capacity = 0;

    fprintf(stderr, "✅ 作业集合清理完成\n");
}
