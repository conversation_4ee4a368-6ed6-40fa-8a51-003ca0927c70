/**
 * @file batch_transmitter.h
 * @brief 批量作业传输器头文件
 */

#ifndef _BATCH_TRANSMITTER_H_
#define _BATCH_TRANSMITTER_H_

#include <time.h>
#include <stddef.h>

/* 前向声明 */
struct batchSubmitReply;
struct batch_memory_pool;
struct batch_job_collection;

/* 传输统计信息 */
typedef struct {
    time_t start_time;          /* 开始时间 */
    time_t end_time;            /* 结束时间 */
    int total_jobs;             /* 总作业数 */
    int successful_jobs;        /* 成功作业数 */
    int failed_jobs;            /* 失败作业数 */
    size_t bytes_sent;          /* 发送字节数 */
    size_t bytes_received;      /* 接收字节数 */
    int network_errors;         /* 网络错误数 */
    int retry_count;            /* 重试次数 */
} transmit_statistics_t;

/* 进度回调函数类型 */
typedef void (*transmit_progress_callback_t)(int current, int total, const char *phase, void *user_data);

/* 批量传输器结构 */
struct batch_transmitter {
    int connection_fd;          /* 连接文件描述符 */
    int is_connected;           /* 连接状态 */
    int enable_streaming;       /* 启用流式响应 */
    int max_retry_count;        /* 最大重试次数 */
    int connection_timeout;     /* 连接超时时间 */
    int read_timeout;           /* 读取超时时间 */
    int write_timeout;          /* 写入超时时间 */

    transmit_statistics_t statistics;  /* 统计信息 */
    transmit_progress_callback_t progress_callback;  /* 进度回调 */
    void *progress_user_data;   /* 进度回调用户数据 */

    struct batch_memory_pool *memory_pool;  /* 内存池 */
    int is_complete;            /* 传输是否完成 */
};

/* 类型别名，保持向后兼容 */
typedef struct batch_transmitter batch_transmitter_t;

/* 传输选项 */
typedef struct {
    int enable_streaming;       /* 启用流式响应 */
    int max_retry_count;        /* 最大重试次数 */
    int connection_timeout;     /* 连接超时时间（秒） */
    int read_timeout;           /* 读取超时时间（秒） */
    int write_timeout;          /* 写入超时时间（秒） */
    int enable_compression;     /* 启用数据压缩 */
    int batch_size_limit;       /* 批量大小限制 */
    char *server_host;          /* 服务器主机名 */
    int server_port;            /* 服务器端口 */
} transmit_options_t;

/* 传输结果 */
typedef struct {
    int result_code;            /* 结果代码：0=成功，<0=失败 */
    int successful_jobs;        /* 成功作业数 */
    int failed_jobs;            /* 失败作业数 */
    double transmission_time;   /* 传输耗时（秒） */
    char *error_message;        /* 错误消息 */
    struct batchSubmitReply *batch_reply;  /* 批量响应 */
} transmit_result_t;

#ifdef __cplusplus
extern "C" {
#endif

/**
 * @brief 创建批量传输器
 * @return 传输器指针，失败返回NULL
 */
batch_transmitter_t* batch_transmitter_create(void);

/**
 * @brief 配置传输器
 * @param transmitter 传输器指针
 * @param enable_streaming 是否启用流式响应
 * @param max_retry_count 最大重试次数
 * @param connection_timeout 连接超时时间（秒）
 * @return 0成功，-1失败
 */
int batch_transmitter_configure(batch_transmitter_t *transmitter,
                               int enable_streaming,
                               int max_retry_count,
                               int connection_timeout);

/**
 * @brief 设置传输选项
 * @param transmitter 传输器指针
 * @param options 传输选项
 * @return 0成功，-1失败
 */
int batch_transmitter_set_options(batch_transmitter_t *transmitter,
                                  const transmit_options_t *options);

/**
 * @brief 传输批量作业
 * @param transmitter 传输器指针
 * @param collection 作业集合
 * @return 成功提交的作业数，失败返回-1
 */
int batch_transmitter_submit_jobs(batch_transmitter_t *transmitter,
                                  const struct batch_job_collection *collection);

/**
 * @brief 传输批量作业（带结果）
 * @param transmitter 传输器指针
 * @param collection 作业集合
 * @param result 输出的传输结果
 * @return 0成功，-1失败
 */
int batch_transmitter_submit_jobs_with_result(batch_transmitter_t *transmitter,
                                              const struct batch_job_collection *collection,
                                              transmit_result_t *result);

/**
 * @brief 设置进度回调
 * @param transmitter 传输器指针
 * @param callback 进度回调函数
 * @param user_data 用户数据
 */
void batch_transmitter_set_progress_callback(batch_transmitter_t *transmitter,
                                             transmit_progress_callback_t callback,
                                             void *user_data);

/**
 * @brief 获取传输统计信息
 * @param transmitter 传输器指针
 * @param stats 输出的统计信息
 * @return 0成功，-1失败
 */
int batch_transmitter_get_statistics(const batch_transmitter_t *transmitter,
                                     transmit_statistics_t *stats);

/**
 * @brief 打印传输统计信息
 * @param transmitter 传输器指针
 */
void batch_transmitter_print_statistics(const batch_transmitter_t *transmitter);

/**
 * @brief 检查传输器状态
 * @param transmitter 传输器指针
 * @return 1=连接中，0=未连接，-1=错误
 */
int batch_transmitter_is_connected(const batch_transmitter_t *transmitter);

/**
 * @brief 取消当前传输
 * @param transmitter 传输器指针
 * @return 0成功，-1失败
 */
int batch_transmitter_cancel(batch_transmitter_t *transmitter);

/**
 * @brief 重置传输器状态
 * @param transmitter 传输器指针
 * @return 0成功，-1失败
 */
int batch_transmitter_reset(batch_transmitter_t *transmitter);

/**
 * @brief 销毁批量传输器
 * @param transmitter 传输器指针
 */
void batch_transmitter_destroy(batch_transmitter_t *transmitter);

/* 辅助函数 */

/**
 * @brief 创建默认传输选项
 * @param options 输出的选项结构
 * @return 0成功，-1失败
 */
int batch_transmitter_create_default_options(transmit_options_t *options);

/**
 * @brief 清理传输结果
 * @param result 传输结果
 */
void batch_transmitter_cleanup_result(transmit_result_t *result);

/**
 * @brief 清理submitReq数组
 * @param submit_array submitReq数组
 * @param count 数组大小
 */
void cleanup_submit_array(struct submitReq *submit_array, int count);

/* 这些函数是内部函数，不需要在头文件中声明 */

/**
 * @brief 验证传输选项
 * @param options 传输选项
 * @return 0有效，-1无效
 */
int batch_transmitter_validate_options(const transmit_options_t *options);

/**
 * @brief 估算传输时间
 * @param transmitter 传输器指针
 * @param job_count 作业数量
 * @return 估算的传输时间（秒），-1表示无法估算
 */
double batch_transmitter_estimate_time(const batch_transmitter_t *transmitter,
                                       int job_count);

/* 便利宏定义 */
#define BATCH_TRANSMITTER_DEFAULT_RETRY_COUNT    3
#define BATCH_TRANSMITTER_DEFAULT_TIMEOUT        30
#define BATCH_TRANSMITTER_DEFAULT_READ_TIMEOUT   60
#define BATCH_TRANSMITTER_DEFAULT_WRITE_TIMEOUT  60
#define BATCH_TRANSMITTER_MAX_BATCH_SIZE         10000

/* 错误代码定义 */
#define TRANSMITTER_SUCCESS                0
#define TRANSMITTER_ERROR_INVALID_ARG     -1
#define TRANSMITTER_ERROR_NETWORK         -2
#define TRANSMITTER_ERROR_TIMEOUT         -3
#define TRANSMITTER_ERROR_MEMORY          -4
#define TRANSMITTER_ERROR_PROTOCOL        -5
#define TRANSMITTER_ERROR_SERVER          -6
#define TRANSMITTER_ERROR_CANCELLED       -7

/* 传输状态定义 */
#define TRANSMITTER_STATUS_IDLE           0
#define TRANSMITTER_STATUS_CONNECTING     1
#define TRANSMITTER_STATUS_CONNECTED      2
#define TRANSMITTER_STATUS_TRANSMITTING   3
#define TRANSMITTER_STATUS_RECEIVING      4
#define TRANSMITTER_STATUS_COMPLETED      5
#define TRANSMITTER_STATUS_ERROR          6

#ifdef __cplusplus
}
#endif

#endif /* _BATCH_TRANSMITTER_H_ */
