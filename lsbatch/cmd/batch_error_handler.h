/**
 * @file batch_error_handler.h
 * @brief 批量作业提交错误处理和恢复机制头文件
 */

#ifndef _BATCH_ERROR_HANDLER_H_
#define _BATCH_ERROR_HANDLER_H_

#include <time.h>
#include <stdio.h>

/* 错误类型定义 */
typedef enum {
    BATCH_ERROR_NONE = 0,           /* 无错误 */
    BATCH_ERROR_NETWORK,            /* 网络错误 */
    BATCH_ERROR_PARSE,              /* 解析错误 */
    BATCH_ERROR_VALIDATION,         /* 验证错误 */
    BATCH_ERROR_MEMORY,             /* 内存错误 */
    BATCH_ERROR_FILE_ACCESS,        /* 文件访问错误 */
    BATCH_ERROR_SERVER,             /* 服务器错误 */
    BATCH_ERROR_TIMEOUT,            /* 超时错误 */
    BATCH_ERROR_PERMISSION,         /* 权限错误 */
    BATCH_ERROR_RESOURCE_LIMIT,     /* 资源限制错误 */
    BATCH_ERROR_UNKNOWN             /* 未知错误 */
} batch_error_type_t;

/* 错误严重程度 */
typedef enum {
    BATCH_ERROR_SEVERITY_INFO = 0,      /* 信息 */
    BATCH_ERROR_SEVERITY_WARNING,       /* 警告 */
    BATCH_ERROR_SEVERITY_ERROR,         /* 错误 */
    BATCH_ERROR_SEVERITY_CRITICAL       /* 严重错误 */
} batch_error_severity_t;

/* 错误恢复策略 */
typedef enum {
    BATCH_RECOVERY_NONE = 0,        /* 不恢复 */
    BATCH_RECOVERY_RETRY,           /* 重试 */
    BATCH_RECOVERY_SKIP,            /* 跳过 */
    BATCH_RECOVERY_FALLBACK,        /* 降级处理 */
    BATCH_RECOVERY_ABORT            /* 中止 */
} batch_recovery_strategy_t;

/* 错误信息结构 */
typedef struct {
    batch_error_type_t type;            /* 错误类型 */
    batch_error_severity_t severity;    /* 严重程度 */
    int error_code;                     /* 错误代码 */
    char *error_message;                /* 错误消息 */
    char *context_info;                 /* 上下文信息 */
    time_t timestamp;                   /* 时间戳 */
    int job_index;                      /* 相关作业索引 */
    int line_number;                    /* 相关行号 */
    batch_recovery_strategy_t recovery_strategy; /* 恢复策略 */
} batch_error_info_t;

/* 错误处理器配置 */
typedef struct {
    int max_retry_count;                /* 最大重试次数 */
    int retry_delay_seconds;            /* 重试延迟（秒） */
    int enable_exponential_backoff;     /* 启用指数退避 */
    int max_backoff_seconds;            /* 最大退避时间 */
    int continue_on_error;              /* 遇到错误继续处理 */
    int collect_all_errors;             /* 收集所有错误 */
    char *error_log_file;               /* 错误日志文件 */
    int enable_detailed_logging;        /* 启用详细日志 */
} batch_error_handler_config_t;

/* 错误处理器结构（不透明） */
typedef struct batch_error_handler batch_error_handler_t;

#ifdef __cplusplus
extern "C" {
#endif

/**
 * @brief 创建错误处理器
 * @param config 配置，NULL使用默认配置
 * @return 错误处理器指针，失败返回NULL
 */
batch_error_handler_t* batch_error_handler_create(const batch_error_handler_config_t *config);

/**
 * @brief 记录错误
 * @param handler 错误处理器
 * @param type 错误类型
 * @param severity 严重程度
 * @param error_code 错误代码
 * @param error_message 错误消息
 * @param context_info 上下文信息
 * @param job_index 作业索引
 * @param line_number 行号
 * @return 0成功，-1失败
 */
int batch_error_handler_record_error(batch_error_handler_t *handler,
                                     batch_error_type_t type,
                                     batch_error_severity_t severity,
                                     int error_code,
                                     const char *error_message,
                                     const char *context_info,
                                     int job_index,
                                     int line_number);

/**
 * @brief 处理错误并决定恢复策略
 * @param handler 错误处理器
 * @param error_index 错误索引
 * @param retry_count 当前重试次数
 * @return 恢复策略
 */
batch_recovery_strategy_t batch_error_handler_handle_error(batch_error_handler_t *handler,
                                                          int error_index,
                                                          int retry_count);

/**
 * @brief 打印错误统计
 * @param handler 错误处理器
 */
void batch_error_handler_print_statistics(const batch_error_handler_t *handler);

/**
 * @brief 销毁错误处理器
 * @param handler 错误处理器
 */
void batch_error_handler_destroy(batch_error_handler_t *handler);

/* 便利函数 */

/**
 * @brief 创建默认错误处理器配置
 * @param config 输出的配置结构
 * @return 0成功，-1失败
 */
int batch_error_handler_create_default_config(batch_error_handler_config_t *config);

/**
 * @brief 检查是否有严重错误
 * @param handler 错误处理器
 * @return 1有严重错误，0无严重错误
 */
int batch_error_handler_has_critical_errors(const batch_error_handler_t *handler);

/**
 * @brief 获取错误数量
 * @param handler 错误处理器
 * @return 错误数量
 */
int batch_error_handler_get_error_count(const batch_error_handler_t *handler);

/**
 * @brief 清除所有错误
 * @param handler 错误处理器
 */
void batch_error_handler_clear_errors(batch_error_handler_t *handler);

/**
 * @brief 获取指定索引的错误信息
 * @param handler 错误处理器
 * @param index 错误索引
 * @return 错误信息指针，失败返回NULL
 */
const batch_error_info_t* batch_error_handler_get_error(const batch_error_handler_t *handler, int index);

/**
 * @brief 导出错误报告到文件
 * @param handler 错误处理器
 * @param filename 文件名
 * @return 0成功，-1失败
 */
int batch_error_handler_export_report(const batch_error_handler_t *handler, const char *filename);

/* 便利宏定义 */
#define BATCH_ERROR_RECORD_SIMPLE(handler, type, message) \
    batch_error_handler_record_error(handler, type, BATCH_ERROR_SEVERITY_ERROR, \
                                     0, message, NULL, -1, -1)

#define BATCH_ERROR_RECORD_WITH_CONTEXT(handler, type, message, context) \
    batch_error_handler_record_error(handler, type, BATCH_ERROR_SEVERITY_ERROR, \
                                     0, message, context, -1, -1)

#define BATCH_ERROR_RECORD_JOB(handler, type, message, job_idx, line_num) \
    batch_error_handler_record_error(handler, type, BATCH_ERROR_SEVERITY_ERROR, \
                                     0, message, NULL, job_idx, line_num)

#define BATCH_ERROR_RECORD_WARNING(handler, type, message) \
    batch_error_handler_record_error(handler, type, BATCH_ERROR_SEVERITY_WARNING, \
                                     0, message, NULL, -1, -1)

#define BATCH_ERROR_RECORD_CRITICAL(handler, type, message) \
    batch_error_handler_record_error(handler, type, BATCH_ERROR_SEVERITY_CRITICAL, \
                                     0, message, NULL, -1, -1)

/* 常量定义 */
#define BATCH_ERROR_HANDLER_DEFAULT_RETRY_COUNT     3
#define BATCH_ERROR_HANDLER_DEFAULT_RETRY_DELAY     5
#define BATCH_ERROR_HANDLER_DEFAULT_MAX_BACKOFF     60
#define BATCH_ERROR_HANDLER_MAX_ERROR_MESSAGE_LEN   1024
#define BATCH_ERROR_HANDLER_MAX_CONTEXT_LEN         512

/* 错误代码定义 */
#define BATCH_ERROR_CODE_SUCCESS                    0
#define BATCH_ERROR_CODE_INVALID_ARGUMENT          -1
#define BATCH_ERROR_CODE_MEMORY_ALLOCATION         -2
#define BATCH_ERROR_CODE_FILE_NOT_FOUND            -3
#define BATCH_ERROR_CODE_PERMISSION_DENIED         -4
#define BATCH_ERROR_CODE_NETWORK_UNREACHABLE       -5
#define BATCH_ERROR_CODE_CONNECTION_TIMEOUT        -6
#define BATCH_ERROR_CODE_SERVER_ERROR              -7
#define BATCH_ERROR_CODE_PARSE_ERROR               -8
#define BATCH_ERROR_CODE_VALIDATION_ERROR          -9
#define BATCH_ERROR_CODE_RESOURCE_EXHAUSTED        -10

#ifdef __cplusplus
}
#endif

#endif /* _BATCH_ERROR_HANDLER_H_ */
