/**
 * @file batch_error_handler.c
 * @brief 批量作业提交错误处理和恢复机制
 * 
 * 主要功能：
 * 1. 错误分类和处理
 * 2. 重试逻辑和策略
 * 3. 部分失败恢复
 * 4. 错误统计和报告
 * 5. 故障转移机制
 */

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <errno.h>
#include <time.h>
#include <unistd.h>

#include "cmd.h"
#include "../lsbatch.h"
#include "batch_job_collector.h"
#include "batch_transmitter.h"

/* 错误类型定义已在lsbatch.h中，这里不需要重复定义 */

/* 错误严重程度和恢复策略定义已在lsbatch.h中 */

/* 错误相关结构定义已在lsbatch.h中 */

/* 内部函数声明 */
static const char* error_type_to_string(batch_error_type_t type);
static const char* severity_to_string(batch_error_severity_t severity);
static const char* recovery_strategy_to_string(batch_recovery_strategy_t strategy);
static batch_recovery_strategy_t determine_recovery_strategy(const struct batch_error_info *error);
static int should_retry_error(const struct batch_error_info *error, int retry_count);
static int calculate_retry_delay(int retry_count, int base_delay, int enable_backoff, int max_delay);

/**
 * @brief 创建错误处理器
 */
struct batch_error_handler* batch_error_handler_create(const struct batch_error_handler_config *config)
{
    struct batch_error_handler *handler = malloc(sizeof(struct batch_error_handler));
    if (!handler) {
        fprintf(stderr, "❌ 错误处理器内存分配失败\n");
        return NULL;
    }
    
    memset(handler, 0, sizeof(struct batch_error_handler));
    
    /* 设置默认配置 */
    if (config) {
        handler->config = *config;
    } else {
        handler->config.max_retry_count = 3;
        handler->config.retry_delay_seconds = 5;
        handler->config.enable_exponential_backoff = 1;
        handler->config.max_backoff_seconds = 60;
        handler->config.continue_on_error = 1;
        handler->config.collect_all_errors = 1;
        handler->config.error_log_file = NULL;
        handler->config.enable_detailed_logging = 0;
    }
    
    /* 初始化错误数组 */
    handler->error_capacity = 100;
    handler->errors = malloc(handler->error_capacity * sizeof(struct batch_error_info));
    if (!handler->errors) {
        free(handler);
        fprintf(stderr, "❌ 错误数组内存分配失败\n");
        return NULL;
    }
    
    /* 打开日志文件 */
    if (handler->config.error_log_file) {
        handler->log_file = fopen(handler->config.error_log_file, "a");
        if (!handler->log_file) {
            fprintf(stderr, "⚠️  无法打开错误日志文件: %s\n", handler->config.error_log_file);
        }
    }
    
    fprintf(stderr, "✅ 错误处理器创建成功\n");
    return handler;
}

/**
 * @brief 记录错误
 */
int batch_error_handler_record_error(struct batch_error_handler *handler,
                                     batch_error_type_t type,
                                     batch_error_severity_t severity,
                                     int error_code,
                                     const char *error_message,
                                     const char *context_info,
                                     int job_index,
                                     int line_number)
{
    if (!handler) {
        return -1;
    }
    
    /* 扩展错误数组（如果需要） */
    if (handler->error_count >= handler->error_capacity) {
        int new_capacity = handler->error_capacity * 2;
        batch_error_info_t *new_errors = realloc(handler->errors, 
                                                 new_capacity * sizeof(batch_error_info_t));
        if (!new_errors) {
            fprintf(stderr, "❌ 扩展错误数组失败\n");
            return -1;
        }
        handler->errors = new_errors;
        handler->error_capacity = new_capacity;
    }
    
    /* 记录错误信息 */
    batch_error_info_t *error = &handler->errors[handler->error_count];
    memset(error, 0, sizeof(batch_error_info_t));
    
    error->type = type;
    error->severity = severity;
    error->error_code = error_code;
    error->error_message = error_message ? strdup(error_message) : NULL;
    error->context_info = context_info ? strdup(context_info) : NULL;
    error->timestamp = time(NULL);
    error->job_index = job_index;
    error->line_number = line_number;
    error->recovery_strategy = determine_recovery_strategy(error);
    
    handler->error_count++;
    
    /* 输出错误信息 */
    fprintf(stderr, "❌ [%s] %s: %s\n", 
            severity_to_string(severity),
            error_type_to_string(type),
            error_message ? error_message : "未知错误");
    
    if (context_info) {
        fprintf(stderr, "   上下文: %s\n", context_info);
    }
    
    if (job_index >= 0) {
        fprintf(stderr, "   作业索引: %d\n", job_index);
    }
    
    if (line_number > 0) {
        fprintf(stderr, "   行号: %d\n", line_number);
    }
    
    /* 写入日志文件 */
    if (handler->log_file) {
        fprintf(handler->log_file, "[%ld] [%s] [%s] %s",
                error->timestamp,
                severity_to_string(severity),
                error_type_to_string(type),
                error_message ? error_message : "未知错误");
        
        if (context_info) {
            fprintf(handler->log_file, " - %s", context_info);
        }
        
        fprintf(handler->log_file, "\n");
        fflush(handler->log_file);
    }
    
    return 0;
}

/**
 * @brief 处理错误并决定恢复策略
 */
batch_recovery_strategy_t batch_error_handler_handle_error(struct batch_error_handler *handler,
                                                          int error_index,
                                                          int retry_count)
{
    if (!handler || error_index < 0 || error_index >= handler->error_count) {
        return BATCH_RECOVERY_ABORT;
    }
    
    batch_error_info_t *error = &handler->errors[error_index];
    
    /* 检查是否应该重试 */
    if (should_retry_error(error, retry_count)) {
        int delay = calculate_retry_delay(retry_count, 
                                         handler->config.retry_delay_seconds,
                                         handler->config.enable_exponential_backoff,
                                         handler->config.max_backoff_seconds);
        
        fprintf(stderr, "🔄 错误可重试，%d 秒后重试（第 %d 次）\n", delay, retry_count + 1);
        
        if (delay > 0) {
            sleep(delay);
        }
        
        handler->total_retries++;
        return BATCH_RECOVERY_RETRY;
    }
    
    /* 根据错误类型和配置决定策略 */
    switch (error->type) {
        case BATCH_ERROR_PARSE:
        case BATCH_ERROR_VALIDATION:
            /* 解析和验证错误通常不可恢复，跳过 */
            if (handler->config.continue_on_error) {
                fprintf(stderr, "⏭️  跳过无法修复的错误\n");
                return BATCH_RECOVERY_SKIP;
            } else {
                fprintf(stderr, "🛑 遇到严重错误，中止处理\n");
                return BATCH_RECOVERY_ABORT;
            }
            
        case BATCH_ERROR_NETWORK:
        case BATCH_ERROR_TIMEOUT:
            /* 网络和超时错误可能是临时的 */
            if (handler->config.continue_on_error) {
                fprintf(stderr, "⏭️  跳过临时网络错误\n");
                return BATCH_RECOVERY_SKIP;
            } else {
                return BATCH_RECOVERY_ABORT;
            }
            
        case BATCH_ERROR_MEMORY:
        case BATCH_ERROR_RESOURCE_LIMIT:
            /* 资源错误可能需要降级处理 */
            fprintf(stderr, "📉 尝试降级处理\n");
            return BATCH_RECOVERY_FALLBACK;
            
        default:
            if (handler->config.continue_on_error) {
                return BATCH_RECOVERY_SKIP;
            } else {
                return BATCH_RECOVERY_ABORT;
            }
    }
}

/**
 * @brief 打印错误统计
 */
void batch_error_handler_print_statistics(const struct batch_error_handler *handler)
{
    if (!handler) {
        return;
    }
    
    fprintf(stderr, "\n📊 错误处理统计\n");
    fprintf(stderr, "========================================\n");
    fprintf(stderr, "总错误数:     %d\n", handler->error_count);
    fprintf(stderr, "总重试次数:   %d\n", handler->total_retries);
    
    /* 按类型统计 */
    int type_counts[BATCH_ERROR_UNKNOWN + 1] = {0};
    int severity_counts[BATCH_ERROR_SEVERITY_CRITICAL + 1] = {0};
    
    int i;
    for (i = 0; i < handler->error_count; i++) {
        type_counts[handler->errors[i].type]++;
        severity_counts[handler->errors[i].severity]++;
    }
    
    fprintf(stderr, "\n按类型统计:\n");
    for (i = 0; i <= BATCH_ERROR_UNKNOWN; i++) {
        if (type_counts[i] > 0) {
            fprintf(stderr, "  %s: %d\n", error_type_to_string(i), type_counts[i]);
        }
    }
    
    fprintf(stderr, "\n按严重程度统计:\n");
    for (i = 0; i <= BATCH_ERROR_SEVERITY_CRITICAL; i++) {
        if (severity_counts[i] > 0) {
            fprintf(stderr, "  %s: %d\n", severity_to_string(i), severity_counts[i]);
        }
    }
    
    fprintf(stderr, "========================================\n");
}

/**
 * @brief 销毁错误处理器
 */
void batch_error_handler_destroy(struct batch_error_handler *handler)
{
    if (!handler) {
        return;
    }
    
    fprintf(stderr, "🧹 清理错误处理器...\n");
    
    /* 清理错误信息 */
    if (handler->errors) {
        int i;
        for (i = 0; i < handler->error_count; i++) {
            if (handler->errors[i].error_message) {
                free(handler->errors[i].error_message);
            }
            if (handler->errors[i].context_info) {
                free(handler->errors[i].context_info);
            }
        }
        free(handler->errors);
    }
    
    /* 关闭日志文件 */
    if (handler->log_file) {
        fclose(handler->log_file);
    }
    
    free(handler);
    
    fprintf(stderr, "✅ 错误处理器清理完成\n");
}

/* ==================== 内部函数实现 ==================== */

/**
 * @brief 错误类型转字符串
 */
static const char* error_type_to_string(batch_error_type_t type)
{
    switch (type) {
        case BATCH_ERROR_NONE:              return "无错误";
        case BATCH_ERROR_NETWORK:           return "网络错误";
        case BATCH_ERROR_PARSE:             return "解析错误";
        case BATCH_ERROR_VALIDATION:        return "验证错误";
        case BATCH_ERROR_MEMORY:            return "内存错误";
        case BATCH_ERROR_FILE_ACCESS:       return "文件访问错误";
        case BATCH_ERROR_SERVER:            return "服务器错误";
        case BATCH_ERROR_TIMEOUT:           return "超时错误";
        case BATCH_ERROR_PERMISSION:        return "权限错误";
        case BATCH_ERROR_RESOURCE_LIMIT:    return "资源限制错误";
        case BATCH_ERROR_UNKNOWN:           return "未知错误";
        default:                            return "未定义错误";
    }
}

/**
 * @brief 严重程度转字符串
 */
static const char* severity_to_string(batch_error_severity_t severity)
{
    switch (severity) {
        case BATCH_ERROR_SEVERITY_INFO:     return "信息";
        case BATCH_ERROR_SEVERITY_WARNING:  return "警告";
        case BATCH_ERROR_SEVERITY_ERROR:    return "错误";
        case BATCH_ERROR_SEVERITY_CRITICAL: return "严重";
        default:                            return "未知";
    }
}

/**
 * @brief 恢复策略转字符串
 */
static const char* recovery_strategy_to_string(batch_recovery_strategy_t strategy)
{
    switch (strategy) {
        case BATCH_RECOVERY_NONE:       return "不恢复";
        case BATCH_RECOVERY_RETRY:      return "重试";
        case BATCH_RECOVERY_SKIP:       return "跳过";
        case BATCH_RECOVERY_FALLBACK:   return "降级";
        case BATCH_RECOVERY_ABORT:      return "中止";
        default:                        return "未知";
    }
}

/**
 * @brief 确定恢复策略
 */
static batch_recovery_strategy_t determine_recovery_strategy(const struct batch_error_info *error)
{
    if (!error) {
        return BATCH_RECOVERY_ABORT;
    }

    /* 根据错误类型和严重程度确定策略 */
    switch (error->type) {
        case BATCH_ERROR_NETWORK:
        case BATCH_ERROR_TIMEOUT:
        case BATCH_ERROR_SERVER:
            /* 网络相关错误通常可以重试 */
            return BATCH_RECOVERY_RETRY;

        case BATCH_ERROR_PARSE:
        case BATCH_ERROR_VALIDATION:
            /* 解析和验证错误通常不可恢复 */
            if (error->severity >= BATCH_ERROR_SEVERITY_ERROR) {
                return BATCH_RECOVERY_SKIP;
            } else {
                return BATCH_RECOVERY_RETRY;
            }

        case BATCH_ERROR_MEMORY:
        case BATCH_ERROR_RESOURCE_LIMIT:
            /* 资源错误可能需要降级处理 */
            return BATCH_RECOVERY_FALLBACK;

        case BATCH_ERROR_FILE_ACCESS:
        case BATCH_ERROR_PERMISSION:
            /* 文件和权限错误通常是配置问题 */
            return BATCH_RECOVERY_SKIP;

        default:
            /* 未知错误，根据严重程度决定 */
            if (error->severity >= BATCH_ERROR_SEVERITY_CRITICAL) {
                return BATCH_RECOVERY_ABORT;
            } else {
                return BATCH_RECOVERY_RETRY;
            }
    }
}

/**
 * @brief 判断是否应该重试错误
 */
static int should_retry_error(const struct batch_error_info *error, int retry_count)
{
    if (!error) {
        return 0;
    }

    /* 检查重试次数限制 */
    if (retry_count >= 3) {  /* 默认最大重试3次 */
        return 0;
    }

    /* 根据错误类型判断 */
    switch (error->type) {
        case BATCH_ERROR_NETWORK:
        case BATCH_ERROR_TIMEOUT:
        case BATCH_ERROR_SERVER:
            /* 网络相关错误可以重试 */
            return 1;

        case BATCH_ERROR_MEMORY:
            /* 内存错误可以重试，但次数要少 */
            return retry_count < 1;

        case BATCH_ERROR_PARSE:
        case BATCH_ERROR_VALIDATION:
        case BATCH_ERROR_FILE_ACCESS:
        case BATCH_ERROR_PERMISSION:
            /* 这些错误通常不可重试 */
            return 0;

        default:
            /* 其他错误可以重试一次 */
            return retry_count < 1;
    }
}

/**
 * @brief 计算重试延迟时间
 */
static int calculate_retry_delay(int retry_count, int base_delay, int enable_backoff, int max_delay)
{
    if (retry_count <= 0) {
        return base_delay;
    }

    if (!enable_backoff) {
        return base_delay;
    }

    /* 指数退避：delay = base_delay * (2 ^ retry_count) */
    int delay = base_delay;
    int i;
    for (i = 0; i < retry_count; i++) {
        delay *= 2;
        if (delay > max_delay) {
            delay = max_delay;
            break;
        }
    }

    return delay;
}

/* ==================== 便利函数 ==================== */

/**
 * @brief 创建默认错误处理器配置
 */
int batch_error_handler_create_default_config(batch_error_handler_config_t *config)
{
    if (!config) {
        return -1;
    }

    memset(config, 0, sizeof(batch_error_handler_config_t));

    config->max_retry_count = 3;
    config->retry_delay_seconds = 5;
    config->enable_exponential_backoff = 1;
    config->max_backoff_seconds = 60;
    config->continue_on_error = 1;
    config->collect_all_errors = 1;
    config->error_log_file = NULL;
    config->enable_detailed_logging = 0;

    return 0;
}

/**
 * @brief 检查是否有严重错误
 */
int batch_error_handler_has_critical_errors(const batch_error_handler_t *handler)
{
    if (!handler) {
        return 0;
    }

    int i;
    for (i = 0; i < handler->error_count; i++) {
        if (handler->errors[i].severity >= BATCH_ERROR_SEVERITY_CRITICAL) {
            return 1;
        }
    }

    return 0;
}

/**
 * @brief 获取错误数量
 */
int batch_error_handler_get_error_count(const batch_error_handler_t *handler)
{
    return handler ? handler->error_count : 0;
}

/**
 * @brief 清除所有错误
 */
void batch_error_handler_clear_errors(batch_error_handler_t *handler)
{
    if (!handler) {
        return;
    }

    /* 清理错误信息 */
    int i;
    for (i = 0; i < handler->error_count; i++) {
        if (handler->errors[i].error_message) {
            free(handler->errors[i].error_message);
            handler->errors[i].error_message = NULL;
        }
        if (handler->errors[i].context_info) {
            free(handler->errors[i].context_info);
            handler->errors[i].context_info = NULL;
        }
    }

    handler->error_count = 0;
    handler->total_retries = 0;
}
