/**
 * @file batch_job_collector.h
 * @brief 批量作业收集器头文件
 */

#ifndef _BATCH_JOB_COLLECTOR_H_
#define _BATCH_JOB_COLLECTOR_H_

#include <time.h>
#include <limits.h>

/* 前向声明 */
struct submit;
struct batch_memory_pool;

/* 作业收集器配置 */
struct job_collector_config {
    int max_jobs_count;         /* 最大作业数 */
    int max_memory_usage_mb;    /* 最大内存使用(MB) */
    int fail_on_first_error;    /* 遇到第一个错误就停止 */
    int collect_all_errors;     /* 收集所有错误信息 */
    int enable_detailed_logging;/* 启用详细日志 */
    char *debug_log_file;       /* 调试日志文件 */
};

/* 类型别名，保持向后兼容 */
typedef struct job_collector_config job_collector_config_t;

/* 批量作业集合结构 */
struct batch_job_collection {
    struct submit **job_requests_array;    /* 作业请求数组 */
    int *job_line_numbers;                 /* 每个作业的行号 */
    char **original_command_lines;         /* 原始命令行 */
    int total_jobs_count;                  /* 总作业数 */
    int total_jobs_capacity;               /* 总容量 */
    int collect_original_commands;         /* 是否收集原始命令 */
    time_t collection_start_time;          /* 收集开始时间 */
    char source_file_path[PATH_MAX];       /* 源文件路径 */
    struct batch_memory_pool *memory_pool; /* 内存池 */
};

/* 类型别名，保持向后兼容 */
typedef struct batch_job_collection batch_job_collection_t;

/* 作业收集统计信息 */
typedef struct {
    int total_lines_processed;     /* 处理的总行数 */
    int valid_jobs_collected;      /* 收集的有效作业数 */
    int empty_lines_skipped;       /* 跳过的空行数 */
    int comment_lines_skipped;     /* 跳过的注释行数 */
    int parse_errors_encountered;  /* 遇到的解析错误数 */
    int validation_errors_encountered; /* 遇到的验证错误数 */
    double collection_time_seconds; /* 收集耗时（秒） */
    size_t memory_usage_bytes;     /* 内存使用量（字节） */
} job_collection_stats_t;

#ifdef __cplusplus
extern "C" {
#endif

/**
 * @brief 创建默认作业收集器配置
 * @param config 配置结构指针
 * @return 0成功，-1失败
 */
int job_collector_create_default_config(struct job_collector_config *config);

/**
 * @brief 从pack文件收集作业
 * @param pack_file_path pack文件路径
 * @param collection 输出的作业集合
 * @param config 收集器配置，NULL使用默认配置
 * @return 0成功，-1失败
 */
int job_collector_collect_from_pack_file(const char *pack_file_path,
                                          struct batch_job_collection *collection,
                                          const struct job_collector_config *config);

/**
 * @brief 从字符串收集作业
 * @param pack_content pack内容字符串
 * @param collection 输出的作业集合
 * @param config 收集器配置，NULL使用默认配置
 * @return 0成功，-1失败
 */
int job_collector_collect_from_string(const char *pack_content,
                                       batch_job_collection_t *collection,
                                       const job_collector_config_t *config);

/**
 * @brief 验证作业集合
 * @param collection 作业集合
 * @return 0成功，-1失败
 */
int job_collector_validate_collection(const batch_job_collection_t *collection);

/**
 * @brief 获取作业集合统计信息
 * @param collection 作业集合
 * @param stats 输出的统计信息
 * @return 0成功，-1失败
 */
int job_collector_get_stats(const batch_job_collection_t *collection,
                            job_collection_stats_t *stats);

/**
 * @brief 打印作业集合摘要
 * @param collection 作业集合
 * @param verbose 是否详细输出
 */
void job_collector_print_summary(const batch_job_collection_t *collection, int verbose);

/**
 * @brief 转换作业集合为submitReq数组
 * @param collection 作业集合
 * @param submit_reqs 输出的submitReq数组指针
 * @param count 输出的数组大小
 * @return 0成功，-1失败
 */
int job_collector_convert_to_submit_reqs(const batch_job_collection_t *collection,
                                          struct submitReq **submit_reqs,
                                          int *count);

/**
 * @brief 清理批量作业集合
 * @param collection 作业集合
 */
void batch_job_collection_cleanup(struct batch_job_collection *collection);

/**
 * @brief 复制作业集合
 * @param dest 目标集合
 * @param src 源集合
 * @return 0成功，-1失败
 */
int job_collector_copy_collection(batch_job_collection_t *dest,
                                   const batch_job_collection_t *src);

/**
 * @brief 合并多个作业集合
 * @param dest 目标集合
 * @param sources 源集合数组
 * @param source_count 源集合数量
 * @return 0成功，-1失败
 */
int job_collector_merge_collections(batch_job_collection_t *dest,
                                     const batch_job_collection_t *sources,
                                     int source_count);

/**
 * @brief 过滤作业集合
 * @param collection 作业集合
 * @param filter_func 过滤函数，返回1保留，0过滤掉
 * @param user_data 用户数据
 * @return 过滤后保留的作业数量，-1失败
 */
int job_collector_filter_collection(batch_job_collection_t *collection,
                                     int (*filter_func)(const struct submit *, void *),
                                     void *user_data);

/**
 * @brief 排序作业集合
 * @param collection 作业集合
 * @param compare_func 比较函数
 * @return 0成功，-1失败
 */
int job_collector_sort_collection(batch_job_collection_t *collection,
                                   int (*compare_func)(const struct submit *, const struct submit *));

/* 便利宏定义 */
#define JOB_COLLECTOR_FOR_EACH(collection, index, job_req) \
    for ((index) = 0; \
         (index) < (collection)->total_jobs_count && \
         ((job_req) = (collection)->job_requests_array[index]); \
         (index)++)

#define JOB_COLLECTOR_IS_EMPTY(collection) \
    ((collection) == NULL || (collection)->total_jobs_count == 0)

#define JOB_COLLECTOR_GET_JOB(collection, index) \
    (((index) >= 0 && (index) < (collection)->total_jobs_count) ? \
     (collection)->job_requests_array[index] : NULL)

#define JOB_COLLECTOR_GET_LINE_NUMBER(collection, index) \
    (((index) >= 0 && (index) < (collection)->total_jobs_count) ? \
     (collection)->job_line_numbers[index] : -1)

#define JOB_COLLECTOR_GET_ORIGINAL_COMMAND(collection, index) \
    (((index) >= 0 && (index) < (collection)->total_jobs_count) ? \
     (collection)->original_command_lines[index] : NULL)

/* 错误代码定义 */
#define JOB_COLLECTOR_SUCCESS           0
#define JOB_COLLECTOR_ERROR_INVALID_ARG -1
#define JOB_COLLECTOR_ERROR_FILE_ACCESS -2
#define JOB_COLLECTOR_ERROR_MEMORY      -3
#define JOB_COLLECTOR_ERROR_PARSE       -4
#define JOB_COLLECTOR_ERROR_VALIDATION  -5
#define JOB_COLLECTOR_ERROR_LIMIT       -6

/* 常量定义 */
#define JOB_COLLECTOR_MAX_JOBS_DEFAULT      10000
#define JOB_COLLECTOR_MAX_MEMORY_MB_DEFAULT 100
#define JOB_COLLECTOR_MAX_LINE_LENGTH       8192

#ifdef __cplusplus
}
#endif

#endif /* _BATCH_JOB_COLLECTOR_H_ */
