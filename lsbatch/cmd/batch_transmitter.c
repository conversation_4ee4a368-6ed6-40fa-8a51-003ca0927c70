/**
 * @file batch_transmitter.c
 * @brief 批量作业传输器实现
 * 
 * 主要功能：
 * 1. 网络传输管理
 * 2. 流式响应处理
 * 3. 连接管理和重连
 * 4. 进度反馈和统计
 * 5. 错误处理和恢复
 */

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <errno.h>
#include <time.h>
#include <unistd.h>
#include <sys/time.h>

#include "cmd.h"
#include "../lsbatch.h"
#include "../lib/batch_memory.h"
#include "batch_job_collector.h"

/* 传输统计信息 */
typedef struct {
    time_t start_time;          /* 开始时间 */
    time_t end_time;            /* 结束时间 */
    int total_jobs;             /* 总作业数 */
    int successful_jobs;        /* 成功作业数 */
    int failed_jobs;            /* 失败作业数 */
    size_t bytes_sent;          /* 发送字节数 */
    size_t bytes_received;      /* 接收字节数 */
    int network_errors;         /* 网络错误数 */
    int retry_count;            /* 重试次数 */
} transmit_statistics_t;

/* 进度回调函数类型 */
typedef void (*transmit_progress_callback_t)(int current, int total, const char *phase, void *user_data);

/* 批量传输器结构 */
typedef struct {
    int connection_fd;          /* 连接文件描述符 */
    int is_connected;           /* 连接状态 */
    int enable_streaming;       /* 启用流式响应 */
    int max_retry_count;        /* 最大重试次数 */
    int connection_timeout;     /* 连接超时时间 */
    int read_timeout;           /* 读取超时时间 */
    int write_timeout;          /* 写入超时时间 */
    
    transmit_statistics_t statistics;  /* 统计信息 */
    transmit_progress_callback_t progress_callback;  /* 进度回调 */
    void *progress_user_data;   /* 进度回调用户数据 */
    
    struct batch_memory_pool *memory_pool;  /* 内存池 */
    int is_complete;            /* 传输是否完成 */
} batch_transmitter_t;

/* 内部函数声明 */
static int establish_connection(batch_transmitter_t *transmitter);
static int send_batch_request(batch_transmitter_t *transmitter, 
                             const batch_job_collection_t *collection);
static int receive_batch_response(batch_transmitter_t *transmitter,
                                 struct batchSubmitReply *reply);
static int handle_stream_response(batch_transmitter_t *transmitter);
static void update_statistics(batch_transmitter_t *transmitter, 
                             int success_count, int failure_count);
static void report_progress(batch_transmitter_t *transmitter, 
                           int current, int total, const char *phase);

/* 外部函数声明 - 来自现有LSF代码 */
extern LS_LONG_INT lsb_submit(struct submit *req, struct submitReply *reply);
extern int lsb_init(char *appName);

/**
 * @brief 创建批量传输器
 */
batch_transmitter_t* batch_transmitter_create(void)
{
    batch_transmitter_t *transmitter = malloc(sizeof(batch_transmitter_t));
    if (!transmitter) {
        fprintf(stderr, "❌ 传输器内存分配失败\n");
        return NULL;
    }
    
    memset(transmitter, 0, sizeof(batch_transmitter_t));
    
    /* 设置默认值 */
    transmitter->connection_fd = -1;
    transmitter->is_connected = 0;
    transmitter->enable_streaming = 1;
    transmitter->max_retry_count = 3;
    transmitter->connection_timeout = 30;
    transmitter->read_timeout = 60;
    transmitter->write_timeout = 60;
    
    /* 初始化内存池 */
    transmitter->memory_pool = malloc(sizeof(struct batch_memory_pool));
    if (!transmitter->memory_pool || 
        batch_memory_pool_init(transmitter->memory_pool, 50 * 1024 * 1024) != 0) {
        fprintf(stderr, "❌ 传输器内存池初始化失败\n");
        free(transmitter);
        return NULL;
    }
    
    fprintf(stderr, "✅ 批量传输器创建成功\n");
    return transmitter;
}

/**
 * @brief 配置传输器
 */
int batch_transmitter_configure(batch_transmitter_t *transmitter,
                               int enable_streaming,
                               int max_retry_count,
                               int connection_timeout)
{
    if (!transmitter) {
        return -1;
    }
    
    transmitter->enable_streaming = enable_streaming;
    transmitter->max_retry_count = max_retry_count;
    transmitter->connection_timeout = connection_timeout;
    
    fprintf(stderr, "🔧 传输器配置: 流式=%s, 最大重试=%d, 连接超时=%ds\n",
            enable_streaming ? "启用" : "禁用", max_retry_count, connection_timeout);
    
    return 0;
}

/**
 * @brief 传输批量作业
 */
int batch_transmitter_submit_jobs(batch_transmitter_t *transmitter,
                                  const batch_job_collection_t *collection)
{
    if (!transmitter || !collection) {
        fprintf(stderr, "❌ 传输器或作业集合为空\n");
        return -1;
    }
    
    if (collection->total_jobs_count <= 0) {
        fprintf(stderr, "❌ 没有作业需要提交\n");
        return -1;
    }
    
    fprintf(stderr, "🚀 开始批量作业传输\n");
    fprintf(stderr, "📊 作业总数: %d\n", collection->total_jobs_count);
    
    /* 初始化统计信息 */
    transmitter->statistics.start_time = time(NULL);
    transmitter->statistics.total_jobs = collection->total_jobs_count;
    transmitter->statistics.successful_jobs = 0;
    transmitter->statistics.failed_jobs = 0;
    
    int result = -1;
    
    /* 当前实现：使用现有的单作业提交方式 */
    /* TODO: 在后续阶段实现真正的批量传输协议 */
    
    /* 转换为submitReq数组 */
    struct submitReq *submit_array = malloc(collection->total_jobs_count * sizeof(struct submitReq));
    if (!submit_array) {
        fprintf(stderr, "❌ submitReq数组分配失败\n");
        return -1;
    }
    
    /* 转换作业集合到submitReq格式 */
    int i;
    for (i = 0; i < collection->total_jobs_count; i++) {
        struct submit *job = collection->job_requests_array[i];
        if (!job) {
            fprintf(stderr, "❌ 作业 %d 为空\n", i);
            continue;
        }
        
        /* 简单的转换逻辑 - 这里需要完整的转换实现 */
        memset(&submit_array[i], 0, sizeof(struct submitReq));
        
        /* 复制基本字段 */
        submit_array[i].options = job->options;
        submit_array[i].options2 = job->options2;
        submit_array[i].numProcessors = job->numProcessors;
        submit_array[i].beginTime = job->beginTime;
        submit_array[i].termTime = job->termTime;
        
        /* 复制字符串字段 */
        if (job->command) {
            submit_array[i].command = strdup(job->command);
        }
        if (job->jobName) {
            submit_array[i].jobName = strdup(job->jobName);
        }
        if (job->queue) {
            submit_array[i].queue = strdup(job->queue);
        }
        if (job->resReq) {
            submit_array[i].resReq = strdup(job->resReq);
        }
        
        /* 设置提交时间 */
        submit_array[i].submitTime = time(NULL);
    }
    
    /* 使用现有的批量提交方式 */
    struct batchSubmitReply batch_reply;
    memset(&batch_reply, 0, sizeof(batch_reply));
    
    /* 分配结果数组 */
    batch_reply.results = malloc(collection->total_jobs_count * sizeof(struct batchJobResult));
    if (!batch_reply.results) {
        fprintf(stderr, "❌ 结果数组分配失败\n");
        goto cleanup;
    }
    
    batch_reply.batchId = (int)time(NULL);  /* 简单的批量ID */
    batch_reply.startTime = transmitter->statistics.start_time;
    
    /* 逐个提交作业（临时实现） */
    int success_count = 0;
    int failure_count = 0;
    
    for (i = 0; i < collection->total_jobs_count; i++) {
        struct submit *job = collection->job_requests_array[i];
        struct submitReply reply;
        memset(&reply, 0, sizeof(reply));
        
        report_progress(transmitter, i + 1, collection->total_jobs_count, "提交作业");
        
        /* 提交单个作业 */
        LS_LONG_INT jobId = lsb_submit(job, &reply);
        
        /* 记录结果 */
        batch_reply.results[i].jobIndex = i;
        batch_reply.results[i].submitTime = time(NULL);
        batch_reply.results[i].processTime = time(NULL);
        
        if (jobId > 0) {
            batch_reply.results[i].jobId = jobId;
            batch_reply.results[i].resultCode = 0;
            batch_reply.results[i].queue = reply.queue ? strdup(reply.queue) : NULL;
            success_count++;
            
            fprintf(stderr, "✅ 作业 %d 提交成功，JobID: %lld\n", i + 1, jobId);
        } else {
            batch_reply.results[i].jobId = 0;
            batch_reply.results[i].resultCode = -1;
            batch_reply.results[i].errorMsg = strdup("作业提交失败");
            failure_count++;
            
            fprintf(stderr, "❌ 作业 %d 提交失败\n", i + 1);
        }
    }
    
    /* 更新统计信息 */
    batch_reply.successCount = success_count;
    batch_reply.failureCount = failure_count;
    batch_reply.completionTime = time(NULL);
    
    update_statistics(transmitter, success_count, failure_count);
    
    result = success_count > 0 ? success_count : -1;

cleanup:
    /* 清理资源 */
    if (submit_array) {
        for (i = 0; i < collection->total_jobs_count; i++) {
            if (submit_array[i].command) free(submit_array[i].command);
            if (submit_array[i].jobName) free(submit_array[i].jobName);
            if (submit_array[i].queue) free(submit_array[i].queue);
            if (submit_array[i].resReq) free(submit_array[i].resReq);
        }
        free(submit_array);
    }
    
    if (batch_reply.results) {
        for (i = 0; i < collection->total_jobs_count; i++) {
            if (batch_reply.results[i].queue) {
                free(batch_reply.results[i].queue);
            }
            if (batch_reply.results[i].errorMsg) {
                free(batch_reply.results[i].errorMsg);
            }
        }
        free(batch_reply.results);
    }
    
    cleanup_submit_array(submit_array, collection->total_jobs_count);
    
    fprintf(stderr, "✅ 批量传输完成，成功: %zu，失败: %zu\n", 
            transmitter->statistics.successful_jobs, transmitter->statistics.failed_jobs);
    
    return result;
}

/**
 * @brief 设置进度回调
 */
void batch_transmitter_set_progress_callback(batch_transmitter_t *transmitter,
                                             transmit_progress_callback_t callback,
                                             void *user_data)
{
    if (!transmitter) {
        return;
    }

    transmitter->progress_callback = callback;
    transmitter->progress_user_data = user_data;
}

/**
 * @brief 获取传输统计信息
 */
int batch_transmitter_get_statistics(const batch_transmitter_t *transmitter,
                                     transmit_statistics_t *stats)
{
    if (!transmitter || !stats) {
        return -1;
    }

    memcpy(stats, &transmitter->statistics, sizeof(transmit_statistics_t));
    return 0;
}

/**
 * @brief 打印传输统计信息
 */
void batch_transmitter_print_statistics(const batch_transmitter_t *transmitter)
{
    if (!transmitter) {
        return;
    }

    const transmit_statistics_t *stats = &transmitter->statistics;

    fprintf(stderr, "\n📊 批量传输统计信息\n");
    fprintf(stderr, "========================================\n");
    fprintf(stderr, "总作业数:     %d\n", stats->total_jobs);
    fprintf(stderr, "成功作业:     %d ✅\n", stats->successful_jobs);
    fprintf(stderr, "失败作业:     %d ❌\n", stats->failed_jobs);

    if (stats->total_jobs > 0) {
        double success_rate = (double)stats->successful_jobs / stats->total_jobs * 100.0;
        fprintf(stderr, "成功率:       %.2f%%\n", success_rate);
    }

    if (stats->end_time > stats->start_time) {
        double duration = difftime(stats->end_time, stats->start_time);
        fprintf(stderr, "总耗时:       %.2f 秒\n", duration);

        if (duration > 0) {
            double throughput = stats->total_jobs / duration;
            fprintf(stderr, "吞吐量:       %.2f 作业/秒\n", throughput);
        }
    }

    fprintf(stderr, "发送字节:     %zu\n", stats->bytes_sent);
    fprintf(stderr, "接收字节:     %zu\n", stats->bytes_received);
    fprintf(stderr, "网络错误:     %d\n", stats->network_errors);
    fprintf(stderr, "重试次数:     %d\n", stats->retry_count);
    fprintf(stderr, "========================================\n");
}

/**
 * @brief 销毁批量传输器
 */
void batch_transmitter_destroy(batch_transmitter_t *transmitter)
{
    if (!transmitter) {
        return;
    }

    fprintf(stderr, "🧹 销毁批量传输器...\n");

    /* 关闭连接 */
    if (transmitter->connection_fd >= 0) {
        close(transmitter->connection_fd);
        transmitter->connection_fd = -1;
    }

    /* 清理内存池 */
    if (transmitter->memory_pool) {
        batch_memory_pool_cleanup(transmitter->memory_pool);
        free(transmitter->memory_pool);
        transmitter->memory_pool = NULL;
    }

    /* 释放传输器结构 */
    free(transmitter);

    fprintf(stderr, "✅ 批量传输器销毁完成\n");
}

/* ==================== 内部函数实现 ==================== */

/**
 * @brief 建立连接
 */
static int establish_connection(batch_transmitter_t *transmitter)
{
    if (!transmitter) {
        return -1;
    }

    /* TODO: 实现真正的网络连接建立 */
    /* 当前使用现有的LSF连接机制 */

    if (lsb_init("bsub") < 0) {
        fprintf(stderr, "❌ LSF初始化失败\n");
        return -1;
    }

    transmitter->is_connected = 1;
    fprintf(stderr, "✅ 连接建立成功\n");

    return 0;
}

/**
 * @brief 发送批量请求
 */
static int send_batch_request(batch_transmitter_t *transmitter,
                             const batch_job_collection_t *collection)
{
    if (!transmitter || !collection) {
        return -1;
    }

    /* TODO: 实现真正的批量请求发送 */
    /* 当前版本使用现有的单作业提交方式 */

    fprintf(stderr, "📤 发送批量请求，作业数: %d\n", collection->total_jobs_count);

    return 0;
}

/**
 * @brief 接收批量响应
 */
static int receive_batch_response(batch_transmitter_t *transmitter,
                                 struct batchSubmitReply *reply)
{
    if (!transmitter || !reply) {
        return -1;
    }

    /* TODO: 实现真正的批量响应接收 */
    /* 当前版本在submit_jobs函数中处理 */

    fprintf(stderr, "📥 接收批量响应\n");

    return 0;
}

/**
 * @brief 处理流式响应
 */
static int handle_stream_response(batch_transmitter_t *transmitter)
{
    if (!transmitter) {
        return -1;
    }

    if (!transmitter->enable_streaming) {
        return 0;
    }

    /* TODO: 实现流式响应处理 */
    fprintf(stderr, "🌊 处理流式响应\n");

    return 0;
}

/**
 * @brief 更新统计信息
 */
static void update_statistics(batch_transmitter_t *transmitter,
                             int success_count, int failure_count)
{
    if (!transmitter) {
        return;
    }

    transmitter->statistics.successful_jobs = success_count;
    transmitter->statistics.failed_jobs = failure_count;
    transmitter->statistics.end_time = time(NULL);
}

/**
 * @brief 报告进度
 */
static void report_progress(batch_transmitter_t *transmitter,
                           int current, int total, const char *phase)
{
    if (!transmitter) {
        return;
    }

    /* 调用用户回调 */
    if (transmitter->progress_callback) {
        transmitter->progress_callback(current, total, phase, transmitter->progress_user_data);
    }

    /* 默认进度输出 */
    if (total > 0) {
        double percentage = (double)current / total * 100.0;
        fprintf(stderr, "🔄 %s: %d/%d (%.1f%%)\n", phase, current, total, percentage);
    } else {
        fprintf(stderr, "🔄 %s: %d\n", phase, current);
    }
}

/**
 * @brief 清理submitReq数组
 */
void cleanup_submit_array(struct submitReq *submit_array, int count)
{
    if (!submit_array) {
        return;
    }

    int i;
    for (i = 0; i < count; i++) {
        /* 清理字符串字段 */
        if (submit_array[i].command) {
            free(submit_array[i].command);
            submit_array[i].command = NULL;
        }
        if (submit_array[i].jobName) {
            free(submit_array[i].jobName);
            submit_array[i].jobName = NULL;
        }
        if (submit_array[i].queue) {
            free(submit_array[i].queue);
            submit_array[i].queue = NULL;
        }
        if (submit_array[i].resReq) {
            free(submit_array[i].resReq);
            submit_array[i].resReq = NULL;
        }
        /* 其他字段的清理... */
    }
}
