/**
 * @file test_batch_basic.c
 * @brief 批量作业提交基础功能测试
 */

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>

#include "batch_test_framework.h"
#include "../lsbatch.h"
#include "../lib/batch_memory.h"

/* 测试数据结构初始化 */
DEFINE_TEST_CASE(data_structures_init)
{
    /* 测试流式消息头初始化 */
    struct streamHeader header;
    memset(&header, 0, sizeof(header));
    
    header.messageType = STREAM_MSG_JOB_RESULT;
    header.sequenceId = 1;
    header.batchId = 12345;
    header.flags = STREAM_FLAG_MORE_MESSAGES;
    
    ASSERT_EQUAL(STREAM_MSG_JOB_RESULT, header.messageType, "消息类型设置错误");
    ASSERT_EQUAL(1, header.sequenceId, "序列号设置错误");
    ASSERT_EQUAL(12345, header.batchId, "批量ID设置错误");
    ASSERT_EQUAL(STREAM_FLAG_MORE_MESSAGES, header.flags, "标志位设置错误");
    
    /* 测试批量作业结果初始化 */
    struct batchJobResult result;
    memset(&result, 0, sizeof(result));
    
    result.jobId = 67890;
    result.resultCode = 0;
    result.jobIndex = 5;
    
    ASSERT_EQUAL(67890, result.jobId, "作业ID设置错误");
    ASSERT_EQUAL(0, result.resultCode, "结果代码设置错误");
    ASSERT_EQUAL(5, result.jobIndex, "作业索引设置错误");
    
    TEST_CASE_END_SUCCESS();
    return 0;
}

/* 测试内存池基础功能 */
DEFINE_TEST_CASE(memory_pool_basic)
{
    struct batch_memory_pool pool;
    
    /* 测试内存池初始化 */
    int result = batch_memory_pool_init(&pool, 1024 * 1024); /* 1MB限制 */
    ASSERT_EQUAL(0, result, "内存池初始化失败");
    ASSERT_NOT_NULL(pool.blocks, "内存池blocks数组为空");
    ASSERT_NOT_NULL(pool.block_sizes, "内存池block_sizes数组为空");
    ASSERT_EQUAL(BATCH_MEMORY_POOL_INITIAL_SIZE, pool.capacity, "内存池初始容量错误");
    
    /* 测试内存分配 */
    void *ptr1 = batch_memory_pool_alloc(&pool, 100);
    ASSERT_NOT_NULL(ptr1, "内存分配失败");
    ASSERT_EQUAL(1, pool.block_count, "内存块计数错误");
    
    void *ptr2 = batch_memory_pool_alloc(&pool, 200);
    ASSERT_NOT_NULL(ptr2, "第二次内存分配失败");
    ASSERT_EQUAL(2, pool.block_count, "内存块计数错误");
    
    /* 测试内存限制 */
    void *ptr_large = batch_memory_pool_alloc(&pool, 2 * 1024 * 1024); /* 超过限制 */
    ASSERT_NULL(ptr_large, "内存限制检查失败");
    
    /* 清理内存池 */
    batch_memory_pool_cleanup(&pool);
    ASSERT_EQUAL(0, pool.block_count, "内存池清理后块计数应为0");
    ASSERT_EQUAL(0, pool.total_allocated, "内存池清理后总分配应为0");
    
    TEST_CASE_END_SUCCESS();
    return 0;
}

/* 测试批量提交请求结构 */
DEFINE_TEST_CASE(batch_submit_request)
{
    struct batchSubmitReq request;
    memset(&request, 0, sizeof(request));
    
    /* 设置基本信息 */
    request.jobCount = 5;
    request.options = BATCH_SUBMIT_STREAM_RESPONSE | BATCH_SUBMIT_CONTINUE_ON_ERROR;
    request.maxConcurrency = 10;
    request.clientTimestamp = time(NULL);
    
    ASSERT_EQUAL(5, request.jobCount, "作业数量设置错误");
    ASSERT_TRUE(request.options & BATCH_SUBMIT_STREAM_RESPONSE, "流式响应选项未设置");
    ASSERT_TRUE(request.options & BATCH_SUBMIT_CONTINUE_ON_ERROR, "错误继续选项未设置");
    ASSERT_EQUAL(10, request.maxConcurrency, "最大并发数设置错误");
    
    /* 分配作业数组 */
    request.jobs = (struct submitReq*)calloc(request.jobCount, sizeof(struct submitReq));
    ASSERT_NOT_NULL(request.jobs, "作业数组分配失败");
    
    /* 初始化第一个作业 */
    request.jobs[0].options = 0;
    request.jobs[0].numProcessors = 1;
    request.jobs[0].command = strdup("sleep 10");
    
    ASSERT_NOT_NULL(request.jobs[0].command, "作业命令设置失败");
    ASSERT_STRING_EQUAL("sleep 10", request.jobs[0].command, "作业命令内容错误");
    
    /* 清理 */
    if (request.jobs) {
        int i;
        for (i = 0; i < request.jobCount; i++) {
            if (request.jobs[i].command) {
                free(request.jobs[i].command);
            }
        }
        free(request.jobs);
    }
    
    TEST_CASE_END_SUCCESS();
    return 0;
}

/* 测试批量提交响应结构 */
DEFINE_TEST_CASE(batch_submit_response)
{
    struct batchSubmitReply reply;
    memset(&reply, 0, sizeof(reply));
    
    /* 设置基本信息 */
    reply.batchId = 12345;
    reply.successCount = 3;
    reply.failureCount = 2;
    reply.startTime = time(NULL) - 100;
    reply.completionTime = time(NULL);
    
    ASSERT_EQUAL(12345, reply.batchId, "批量ID设置错误");
    ASSERT_EQUAL(3, reply.successCount, "成功数量设置错误");
    ASSERT_EQUAL(2, reply.failureCount, "失败数量设置错误");
    ASSERT_TRUE(reply.completionTime >= reply.startTime, "完成时间应大于等于开始时间");
    
    /* 分配结果数组 */
    int totalResults = reply.successCount + reply.failureCount;
    reply.results = (struct batchJobResult*)calloc(totalResults, sizeof(struct batchJobResult));
    ASSERT_NOT_NULL(reply.results, "结果数组分配失败");
    
    /* 设置第一个结果 */
    reply.results[0].jobId = 100001;
    reply.results[0].resultCode = 0;
    reply.results[0].jobIndex = 0;
    reply.results[0].queue = strdup("normal");
    
    ASSERT_EQUAL(100001, reply.results[0].jobId, "结果作业ID设置错误");
    ASSERT_EQUAL(0, reply.results[0].resultCode, "结果代码设置错误");
    ASSERT_STRING_EQUAL("normal", reply.results[0].queue, "队列名称设置错误");
    
    /* 清理 */
    if (reply.results) {
        int i;
        for (i = 0; i < totalResults; i++) {
            if (reply.results[i].queue) {
                free(reply.results[i].queue);
            }
            if (reply.results[i].errorMsg) {
                free(reply.results[i].errorMsg);
            }
        }
        free(reply.results);
    }
    
    if (reply.errorSummary) {
        free(reply.errorSummary);
    }
    
    TEST_CASE_END_SUCCESS();
    return 0;
}

/* 测试临时文件操作 */
DEFINE_TEST_CASE(temp_file_operations)
{
    const char *test_content = "-J \"test1\" sleep 5\n-q normal sleep 10\nsleep 15\n";
    
    /* 创建临时文件 */
    char *temp_file = create_temp_test_file(test_content);
    ASSERT_NOT_NULL(temp_file, "临时文件创建失败");
    
    /* 验证文件存在 */
    FILE *fp = fopen(temp_file, "r");
    ASSERT_NOT_NULL(fp, "临时文件无法打开");
    
    /* 读取并验证内容 */
    char buffer[256];
    size_t bytes_read = fread(buffer, 1, sizeof(buffer) - 1, fp);
    buffer[bytes_read] = '\0';
    fclose(fp);
    
    ASSERT_STRING_EQUAL(test_content, buffer, "临时文件内容不匹配");
    
    /* 删除临时文件 */
    remove_temp_test_file(temp_file);
    
    /* 验证文件已删除 */
    fp = fopen(temp_file, "r");
    ASSERT_NULL(fp, "临时文件删除失败");
    
    free(temp_file);
    
    TEST_CASE_END_SUCCESS();
    return 0;
}

/* 测试套件设置函数 */
int basic_test_setup(void)
{
    printf("🔧 设置基础测试环境...\n");
    srand(time(NULL));
    return 0;
}

/* 测试套件清理函数 */
int basic_test_teardown(void)
{
    printf("🧹 清理基础测试环境...\n");
    return 0;
}

/* 定义测试用例数组 */
struct test_case basic_test_cases[] = {
    {"数据结构初始化", test_data_structures_init, 1},
    {"内存池基础功能", test_memory_pool_basic, 1},
    {"批量提交请求", test_batch_submit_request, 1},
    {"批量提交响应", test_batch_submit_response, 1},
    {"临时文件操作", test_temp_file_operations, 1}
};

/* 定义测试套件 */
DEFINE_TEST_SUITE(basic, basic_test_cases, basic_test_setup, basic_test_teardown);

/* 主函数 */
int main(void)
{
    struct test_suite *suites[] = {
        &test_suite_basic
    };
    
    RUN_ALL_TESTS(suites);
}
