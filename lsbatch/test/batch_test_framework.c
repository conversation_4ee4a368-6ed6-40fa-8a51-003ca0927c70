/**
 * @file batch_test_framework.c
 * @brief 批量作业提交单元测试框架实现
 */

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <time.h>
#include <unistd.h>
#include <sys/stat.h>
#include <sys/time.h>

#include "batch_test_framework.h"

/* 全局测试统计 */
struct test_stats g_test_stats = {0};

/**
 * @brief 初始化测试框架
 */
void test_framework_init(void)
{
    memset(&g_test_stats, 0, sizeof(g_test_stats));
    
    printf("🧪 批量作业提交测试框架初始化\n");
    printf("时间: %s", ctime(&(time_t){time(NULL)}));
    printf("========================================\n");
}

/**
 * @brief 运行单个测试用例
 */
int run_test_case(struct test_case *test_case)
{
    if (!test_case || !test_case->func) {
        printf("❌ 无效的测试用例\n");
        return -1;
    }
    
    if (!test_case->enabled) {
        TEST_CASE_SKIP("测试用例被禁用");
        return 0;
    }
    
    TEST_CASE_BEGIN(test_case->name);
    
    struct timeval start, end;
    gettimeofday(&start, NULL);
    
    int result = test_case->func();
    
    gettimeofday(&end, NULL);
    double elapsed = (end.tv_sec - start.tv_sec) + 
                    (end.tv_usec - start.tv_usec) / 1000000.0;
    g_test_stats.total_time += elapsed;
    
    if (result == 0) {
        TEST_CASE_END_SUCCESS();
    }
    /* 失败情况已在测试函数中处理 */
    
    return result;
}

/**
 * @brief 运行测试套件
 */
int run_test_suite(struct test_suite *suite)
{
    if (!suite) {
        printf("❌ 无效的测试套件\n");
        return -1;
    }
    
    TEST_SUITE_BEGIN(suite->name);
    
    int suite_failures = 0;
    
    /* 执行设置函数 */
    if (suite->setup && suite->setup() != 0) {
        printf("❌ 测试套件设置失败\n");
        return -1;
    }
    
    /* 运行所有测试用例 */
    int i;
    for (i = 0; i < suite->case_count; i++) {
        if (run_test_case(&suite->cases[i]) != 0) {
            suite_failures++;
        }
    }
    
    /* 执行清理函数 */
    if (suite->teardown && suite->teardown() != 0) {
        printf("⚠️  测试套件清理失败\n");
    }
    
    TEST_SUITE_END();
    
    printf("套件统计: 总计 %d, 失败 %d\n", suite->case_count, suite_failures);
    
    return suite_failures > 0 ? -1 : 0;
}

/**
 * @brief 打印测试统计信息
 */
void print_test_stats(void)
{
    printf("\n========================================\n");
    printf("🏁 测试完成统计\n");
    printf("========================================\n");
    printf("总测试数:   %d\n", g_test_stats.total_tests);
    printf("通过测试:   %d ✅\n", g_test_stats.passed_tests);
    printf("失败测试:   %d ❌\n", g_test_stats.failed_tests);
    printf("跳过测试:   %d ⏭️\n", g_test_stats.skipped_tests);
    printf("总耗时:     %.6f 秒\n", g_test_stats.total_time);
    
    if (g_test_stats.total_tests > 0) {
        double pass_rate = (double)g_test_stats.passed_tests / g_test_stats.total_tests * 100.0;
        printf("通过率:     %.2f%%\n", pass_rate);
    }
    
    printf("========================================\n");
    
    if (g_test_stats.failed_tests == 0) {
        printf("🎉 所有测试通过！\n");
    } else {
        printf("💥 有 %d 个测试失败\n", g_test_stats.failed_tests);
    }
}

/**
 * @brief 获取内存使用量
 */
size_t get_memory_usage(void)
{
    FILE *fp;
    char line[256];
    size_t vm_size = 0;
    
    fp = fopen("/proc/self/status", "r");
    if (!fp) {
        return 0;
    }
    
    while (fgets(line, sizeof(line), fp)) {
        if (strncmp(line, "VmSize:", 7) == 0) {
            sscanf(line, "VmSize: %zu kB", &vm_size);
            vm_size *= 1024; /* 转换为字节 */
            break;
        }
    }
    
    fclose(fp);
    return vm_size;
}

/**
 * @brief 创建临时测试文件
 */
char* create_temp_test_file(const char *content)
{
    static int temp_file_counter = 0;
    char *filepath = malloc(256);
    
    if (!filepath) {
        return NULL;
    }
    
    snprintf(filepath, 256, "/tmp/batch_test_%d_%d.tmp", 
             getpid(), ++temp_file_counter);
    
    FILE *fp = fopen(filepath, "w");
    if (!fp) {
        free(filepath);
        return NULL;
    }
    
    if (content) {
        fprintf(fp, "%s", content);
    }
    
    fclose(fp);
    return filepath;
}

/**
 * @brief 删除临时测试文件
 */
void remove_temp_test_file(const char *filepath)
{
    if (filepath) {
        unlink(filepath);
    }
}

/**
 * @brief 比较两个文件内容
 */
int compare_files(const char *file1, const char *file2)
{
    FILE *fp1, *fp2;
    int ch1, ch2;
    
    fp1 = fopen(file1, "r");
    fp2 = fopen(file2, "r");
    
    if (!fp1 || !fp2) {
        if (fp1) fclose(fp1);
        if (fp2) fclose(fp2);
        return -1;
    }
    
    do {
        ch1 = fgetc(fp1);
        ch2 = fgetc(fp2);
        
        if (ch1 != ch2) {
            fclose(fp1);
            fclose(fp2);
            return -1;
        }
    } while (ch1 != EOF && ch2 != EOF);
    
    fclose(fp1);
    fclose(fp2);
    
    return 0;
}

/**
 * @brief 生成测试用的pack文件内容
 */
char* generate_test_pack_content(int job_count)
{
    size_t buffer_size = job_count * 100 + 1024; /* 每个作业大约100字符 */
    char *content = malloc(buffer_size);
    
    if (!content) {
        return NULL;
    }
    
    content[0] = '\0';
    
    int i;
    for (i = 0; i < job_count; i++) {
        char job_line[128];
        
        /* 生成不同类型的测试作业 */
        switch (i % 4) {
            case 0:
                snprintf(job_line, sizeof(job_line), 
                         "sleep %d\n", i + 1);
                break;
            case 1:
                snprintf(job_line, sizeof(job_line), 
                         "-J \"test_job_%d\" sleep %d\n", i, i + 1);
                break;
            case 2:
                snprintf(job_line, sizeof(job_line), 
                         "-q normal -n 2 sleep %d\n", i + 1);
                break;
            case 3:
                snprintf(job_line, sizeof(job_line), 
                         "-R \"select[mem>100]\" -o /tmp/out_%d.log sleep %d\n", 
                         i, i + 1);
                break;
        }
        
        strncat(content, job_line, buffer_size - strlen(content) - 1);
    }
    
    return content;
}

/**
 * @brief 生成随机字符串
 */
char* generate_random_string(int length)
{
    const char charset[] = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
    char *str = malloc(length + 1);
    
    if (!str) {
        return NULL;
    }
    
    int i;
    for (i = 0; i < length; i++) {
        str[i] = charset[rand() % (sizeof(charset) - 1)];
    }
    str[length] = '\0';
    
    return str;
}
