/**
 * @file batch_test_framework.h
 * @brief 批量作业提交单元测试框架
 * 
 * 提供简单但功能完整的单元测试框架，
 * 专门用于批量作业提交功能的测试。
 */

#ifndef _BATCH_TEST_FRAMEWORK_H_
#define _BATCH_TEST_FRAMEWORK_H_

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <time.h>
#include <sys/time.h>

/* 测试结果统计 */
struct test_stats {
    int total_tests;        /* 总测试数 */
    int passed_tests;       /* 通过测试数 */
    int failed_tests;       /* 失败测试数 */
    int skipped_tests;      /* 跳过测试数 */
    double total_time;      /* 总耗时（秒） */
    char *current_suite;    /* 当前测试套件名 */
};

/* 全局测试统计 */
extern struct test_stats g_test_stats;

/* 测试宏定义 */
#define TEST_SUITE_BEGIN(suite_name) \
    do { \
        printf("\n=== 测试套件: %s ===\n", suite_name); \
        g_test_stats.current_suite = suite_name; \
    } while(0)

#define TEST_SUITE_END() \
    do { \
        printf("=== 测试套件完成 ===\n"); \
        g_test_stats.current_suite = NULL; \
    } while(0)

#define TEST_CASE_BEGIN(test_name) \
    do { \
        printf("运行测试: %s ... ", test_name); \
        fflush(stdout); \
        g_test_stats.total_tests++; \
    } while(0)

#define TEST_CASE_END_SUCCESS() \
    do { \
        printf("✅ 通过\n"); \
        g_test_stats.passed_tests++; \
    } while(0)

#define TEST_CASE_END_FAILURE(msg) \
    do { \
        printf("❌ 失败: %s\n", msg); \
        g_test_stats.failed_tests++; \
    } while(0)

#define TEST_CASE_SKIP(reason) \
    do { \
        printf("⏭️  跳过: %s\n", reason); \
        g_test_stats.skipped_tests++; \
    } while(0)

/* 断言宏 */
#define ASSERT_TRUE(condition, msg) \
    do { \
        if (!(condition)) { \
            TEST_CASE_END_FAILURE(msg); \
            return -1; \
        } \
    } while(0)

#define ASSERT_FALSE(condition, msg) \
    do { \
        if (condition) { \
            TEST_CASE_END_FAILURE(msg); \
            return -1; \
        } \
    } while(0)

#define ASSERT_EQUAL(expected, actual, msg) \
    do { \
        if ((expected) != (actual)) { \
            char error_msg[256]; \
            snprintf(error_msg, sizeof(error_msg), "%s (expected: %ld, actual: %ld)", \
                     msg, (long)(expected), (long)(actual)); \
            TEST_CASE_END_FAILURE(error_msg); \
            return -1; \
        } \
    } while(0)

#define ASSERT_NOT_NULL(ptr, msg) \
    do { \
        if ((ptr) == NULL) { \
            TEST_CASE_END_FAILURE(msg); \
            return -1; \
        } \
    } while(0)

#define ASSERT_NULL(ptr, msg) \
    do { \
        if ((ptr) != NULL) { \
            TEST_CASE_END_FAILURE(msg); \
            return -1; \
        } \
    } while(0)

#define ASSERT_STRING_EQUAL(expected, actual, msg) \
    do { \
        if (strcmp((expected), (actual)) != 0) { \
            char error_msg[512]; \
            snprintf(error_msg, sizeof(error_msg), "%s (expected: '%s', actual: '%s')", \
                     msg, (expected), (actual)); \
            TEST_CASE_END_FAILURE(error_msg); \
            return -1; \
        } \
    } while(0)

/* 性能测试宏 */
#define PERF_TEST_BEGIN() \
    struct timeval perf_start, perf_end; \
    gettimeofday(&perf_start, NULL)

#define PERF_TEST_END(operation_name) \
    do { \
        gettimeofday(&perf_end, NULL); \
        double elapsed = (perf_end.tv_sec - perf_start.tv_sec) + \
                        (perf_end.tv_usec - perf_start.tv_usec) / 1000000.0; \
        printf("⏱️  %s 耗时: %.6f 秒\n", operation_name, elapsed); \
        g_test_stats.total_time += elapsed; \
    } while(0)

/* 内存泄漏检测宏 */
#define MEMORY_LEAK_TEST_BEGIN() \
    size_t mem_start = get_memory_usage()

#define MEMORY_LEAK_TEST_END(operation_name) \
    do { \
        size_t mem_end = get_memory_usage(); \
        if (mem_end > mem_start) { \
            printf("⚠️  %s 可能存在内存泄漏: %zu bytes\n", \
                   operation_name, mem_end - mem_start); \
        } else { \
            printf("✅ %s 内存检查通过\n", operation_name); \
        } \
    } while(0)

/* 测试函数类型定义 */
typedef int (*test_function_t)(void);

/* 测试用例结构 */
struct test_case {
    const char *name;
    test_function_t func;
    int enabled;
};

/* 测试套件结构 */
struct test_suite {
    const char *name;
    struct test_case *cases;
    int case_count;
    int (*setup)(void);     /* 测试前设置 */
    int (*teardown)(void);  /* 测试后清理 */
};

/* 框架函数声明 */
#ifdef __cplusplus
extern "C" {
#endif

/**
 * @brief 初始化测试框架
 */
void test_framework_init(void);

/**
 * @brief 运行单个测试用例
 * @param test_case 测试用例
 * @return 0成功，-1失败
 */
int run_test_case(struct test_case *test_case);

/**
 * @brief 运行测试套件
 * @param suite 测试套件
 * @return 0成功，-1失败
 */
int run_test_suite(struct test_suite *suite);

/**
 * @brief 打印测试统计信息
 */
void print_test_stats(void);

/**
 * @brief 获取内存使用量（用于内存泄漏检测）
 * @return 当前内存使用量（字节）
 */
size_t get_memory_usage(void);

/**
 * @brief 创建临时测试文件
 * @param content 文件内容
 * @return 临时文件路径，需要调用者释放
 */
char* create_temp_test_file(const char *content);

/**
 * @brief 删除临时测试文件
 * @param filepath 文件路径
 */
void remove_temp_test_file(const char *filepath);

/**
 * @brief 比较两个文件内容
 * @param file1 文件1路径
 * @param file2 文件2路径
 * @return 0相同，-1不同
 */
int compare_files(const char *file1, const char *file2);

/* 测试数据生成函数 */
/**
 * @brief 生成测试用的pack文件内容
 * @param job_count 作业数量
 * @return 生成的内容字符串，需要调用者释放
 */
char* generate_test_pack_content(int job_count);

/**
 * @brief 生成随机字符串
 * @param length 字符串长度
 * @return 生成的字符串，需要调用者释放
 */
char* generate_random_string(int length);

#ifdef __cplusplus
}
#endif

/* 便利宏：定义测试用例 */
#define DEFINE_TEST_CASE(name) \
    int test_##name(void); \
    struct test_case test_case_##name = {#name, test_##name, 1}; \
    int test_##name(void)

/* 便利宏：定义测试套件 */
#define DEFINE_TEST_SUITE(name, cases, setup_func, teardown_func) \
    struct test_suite test_suite_##name = { \
        #name, \
        cases, \
        sizeof(cases) / sizeof(cases[0]), \
        setup_func, \
        teardown_func \
    }

/* 便利宏：运行所有测试 */
#define RUN_ALL_TESTS(suites) \
    do { \
        test_framework_init(); \
        int i; \
        for (i = 0; i < sizeof(suites) / sizeof(suites[0]); i++) { \
            run_test_suite(&suites[i]); \
        } \
        print_test_stats(); \
        return g_test_stats.failed_tests > 0 ? 1 : 0; \
    } while(0)

#endif /* _BATCH_TEST_FRAMEWORK_H_ */
