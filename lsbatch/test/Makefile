# Makefile for batch job submission tests
# 批量作业提交测试编译文件

CC = gcc
CFLAGS = -Wall -Wextra -g -O0 -std=c99
INCLUDES = -I.. -I../lib -I../../lsf
LIBS = -lm

# 源文件
TEST_FRAMEWORK_SRCS = batch_test_framework.c
MEMORY_SRCS = ../lib/batch_memory.c
TEST_BASIC_SRCS = test_batch_basic.c

# 目标文件
TEST_FRAMEWORK_OBJS = $(TEST_FRAMEWORK_SRCS:.c=.o)
MEMORY_OBJS = $(MEMORY_SRCS:.c=.o)
TEST_BASIC_OBJS = $(TEST_BASIC_SRCS:.c=.o)

# 可执行文件
TEST_BASIC_BIN = test_batch_basic
TEST_XDR_BIN = test_batch_xdr
TEST_MEMORY_BIN = test_batch_memory

# 默认目标
all: $(TEST_BASIC_BIN)

# 基础测试
$(TEST_BASIC_BIN): $(TEST_FRAMEWORK_OBJS) $(MEMORY_OBJS) $(TEST_BASIC_OBJS)
	$(CC) $(CFLAGS) -o $@ $^ $(LIBS)

# XDR测试（待实现）
$(TEST_XDR_BIN): $(TEST_FRAMEWORK_OBJS) test_batch_xdr.o
	$(CC) $(CFLAGS) -o $@ $^ $(LIBS)

# 内存测试（待实现）
$(TEST_MEMORY_BIN): $(TEST_FRAMEWORK_OBJS) $(MEMORY_OBJS) test_batch_memory.o
	$(CC) $(CFLAGS) -o $@ $^ $(LIBS)

# 编译规则
%.o: %.c
	$(CC) $(CFLAGS) $(INCLUDES) -c $< -o $@

# 运行测试
test: $(TEST_BASIC_BIN)
	@echo "🧪 运行基础功能测试..."
	./$(TEST_BASIC_BIN)

test-basic: $(TEST_BASIC_BIN)
	@echo "🧪 运行基础功能测试..."
	./$(TEST_BASIC_BIN)

test-xdr: $(TEST_XDR_BIN)
	@echo "🧪 运行XDR协议测试..."
	./$(TEST_XDR_BIN)

test-memory: $(TEST_MEMORY_BIN)
	@echo "🧪 运行内存管理测试..."
	./$(TEST_MEMORY_BIN)

test-all: test-basic
	@echo "✅ 所有测试完成"

# 清理
clean:
	rm -f *.o ../lib/*.o
	rm -f $(TEST_BASIC_BIN) $(TEST_XDR_BIN) $(TEST_MEMORY_BIN)
	rm -f /tmp/batch_test_*.tmp

# 安装测试（复制到系统测试目录）
install-test: $(TEST_BASIC_BIN)
	@mkdir -p /opt/lsf/test
	cp $(TEST_BASIC_BIN) /opt/lsf/test/
	@echo "测试程序已安装到 /opt/lsf/test/"

# 代码覆盖率测试
coverage: CFLAGS += --coverage
coverage: clean $(TEST_BASIC_BIN)
	./$(TEST_BASIC_BIN)
	gcov *.c ../lib/*.c
	@echo "代码覆盖率报告已生成"

# 内存泄漏检测
valgrind: $(TEST_BASIC_BIN)
	valgrind --leak-check=full --show-leak-kinds=all --track-origins=yes ./$(TEST_BASIC_BIN)

# 静态代码分析
static-analysis:
	@echo "🔍 运行静态代码分析..."
	cppcheck --enable=all --std=c99 $(TEST_FRAMEWORK_SRCS) $(TEST_BASIC_SRCS) $(MEMORY_SRCS)

# 格式化代码
format:
	@echo "🎨 格式化代码..."
	clang-format -i *.c *.h ../lib/batch_memory.c ../lib/batch_memory.h

# 帮助信息
help:
	@echo "可用的make目标:"
	@echo "  all          - 编译所有测试程序"
	@echo "  test         - 运行基础测试"
	@echo "  test-basic   - 运行基础功能测试"
	@echo "  test-xdr     - 运行XDR协议测试"
	@echo "  test-memory  - 运行内存管理测试"
	@echo "  test-all     - 运行所有测试"
	@echo "  clean        - 清理编译文件"
	@echo "  coverage     - 生成代码覆盖率报告"
	@echo "  valgrind     - 运行内存泄漏检测"
	@echo "  static-analysis - 运行静态代码分析"
	@echo "  format       - 格式化代码"
	@echo "  help         - 显示此帮助信息"

# 声明伪目标
.PHONY: all test test-basic test-xdr test-memory test-all clean install-test coverage valgrind static-analysis format help
