/**
 * @file batch_memory.h
 * @brief 批量作业提交内存管理模块头文件
 */

#ifndef _BATCH_MEMORY_H_
#define _BATCH_MEMORY_H_

#include <stddef.h>
#include <time.h>

/* 前向声明 */
struct batch_memory_pool;

/* 内存管理函数声明 */
#ifdef __cplusplus
extern "C" {
#endif

/**
 * @brief 初始化内存池
 * @param pool 内存池指针
 * @param max_limit 最大内存限制（字节）
 * @return 0成功，-1失败
 */
int batch_memory_pool_init(struct batch_memory_pool *pool, size_t max_limit);

/**
 * @brief 从内存池分配内存
 * @param pool 内存池指针
 * @param size 请求的内存大小
 * @return 分配的内存指针，失败返回NULL
 */
void* batch_memory_pool_alloc(struct batch_memory_pool *pool, size_t size);

/**
 * @brief 清理内存池
 * @param pool 内存池指针
 */
void batch_memory_pool_cleanup(struct batch_memory_pool *pool);

/**
 * @brief 获取内存池统计信息
 * @param pool 内存池指针
 * @param total_allocated 输出总分配内存
 * @param block_count 输出块数量
 * @return 0成功，-1失败
 */
int batch_memory_pool_get_stats(struct batch_memory_pool *pool, 
                                size_t *total_allocated, 
                                int *block_count);

/**
 * @brief 检查内存池完整性
 * @param pool 内存池指针
 * @return 0完整，-1损坏
 */
int batch_memory_pool_validate(struct batch_memory_pool *pool);

/**
 * @brief 内存池垃圾回收
 * @param pool 内存池指针
 * @param force_cleanup 是否强制清理
 * @return 回收的内存字节数
 */
size_t batch_memory_pool_gc(struct batch_memory_pool *pool, int force_cleanup);

/* 便利宏定义 */
#define BATCH_MALLOC(pool, size) batch_memory_pool_alloc(pool, size)
#define BATCH_FREE(pool, ptr) /* 内存池统一管理，无需单独释放 */

/* 内存对齐宏 */
#define BATCH_ALIGN_SIZE(size, alignment) \
    (((size) + (alignment) - 1) & ~((alignment) - 1))

/* 内存安全检查宏 */
#define BATCH_CHECK_PTR(ptr) \
    do { \
        if (!(ptr)) { \
            ls_syslog(LOG_ERR, "NULL pointer detected at %s:%d", __FILE__, __LINE__); \
            return -1; \
        } \
    } while(0)

#define BATCH_CHECK_PTR_RETURN_NULL(ptr) \
    do { \
        if (!(ptr)) { \
            ls_syslog(LOG_ERR, "NULL pointer detected at %s:%d", __FILE__, __LINE__); \
            return NULL; \
        } \
    } while(0)

#ifdef __cplusplus
}
#endif

#endif /* _BATCH_MEMORY_H_ */
