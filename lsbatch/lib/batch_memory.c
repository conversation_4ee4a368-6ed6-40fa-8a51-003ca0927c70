/**
 * @file batch_memory.c
 * @brief 批量作业提交内存管理模块
 * 
 * 提供内存池管理、安全检查和资源清理功能，
 * 专门用于批量作业提交的内存优化。
 */

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <errno.h>
#include <time.h>

#include "../lsbatch.h"
#include "../../lsf/lsf.h"

/* 内部常量定义 */
#define BATCH_MEMORY_ALIGNMENT      8       /* 内存对齐字节数 */
#define BATCH_MEMORY_MAGIC_NUMBER   0xDEADBEEF  /* 魔数，用于检测内存损坏 */
#define BATCH_MEMORY_GUARD_SIZE     16      /* 保护区大小 */

/* 内存块头部结构 */
struct memory_block_header {
    size_t size;                    /* 块大小 */
    unsigned int magic;             /* 魔数 */
    time_t alloc_time;             /* 分配时间 */
    struct memory_block_header *next; /* 下一个块 */
    struct memory_block_header *prev; /* 前一个块 */
};

/* 内存池统计信息 */
struct memory_pool_stats {
    size_t total_allocated;         /* 总分配内存 */
    size_t peak_usage;             /* 峰值使用量 */
    int active_blocks;             /* 活跃块数 */
    int total_allocations;         /* 总分配次数 */
    int total_frees;               /* 总释放次数 */
    time_t last_cleanup;           /* 最后清理时间 */
};

/* 内部函数声明 */
static void* align_pointer(void *ptr, size_t alignment);
static int validate_memory_block(struct memory_block_header *header);
static void add_guard_bytes(void *ptr, size_t size);
static int check_guard_bytes(void *ptr, size_t size);
static void update_pool_stats(struct batch_memory_pool *pool, size_t size, int is_alloc);

/**
 * @brief 初始化内存池
 * @param pool 内存池指针
 * @param max_limit 最大内存限制（字节）
 * @return 0成功，-1失败
 */
int batch_memory_pool_init(struct batch_memory_pool *pool, size_t max_limit)
{
    if (!pool) {
        ls_syslog(LOG_ERR, "batch_memory_pool_init: pool is NULL");
        return -1;
    }

    memset(pool, 0, sizeof(struct batch_memory_pool));
    
    pool->capacity = BATCH_MEMORY_POOL_INITIAL_SIZE;
    pool->max_memory_limit = max_limit > 0 ? max_limit : (100 * 1024 * 1024); /* 默认100MB */
    
    /* 分配块指针数组 */
    pool->blocks = (void**)calloc(pool->capacity, sizeof(void*));
    if (!pool->blocks) {
        ls_syslog(LOG_ERR, "batch_memory_pool_init: failed to allocate blocks array: %s", 
                  strerror(errno));
        return -1;
    }
    
    /* 分配块大小数组 */
    pool->block_sizes = (size_t*)calloc(pool->capacity, sizeof(size_t));
    if (!pool->block_sizes) {
        free(pool->blocks);
        pool->blocks = NULL;
        ls_syslog(LOG_ERR, "batch_memory_pool_init: failed to allocate block_sizes array: %s", 
                  strerror(errno));
        return -1;
    }
    
    ls_syslog(LOG_INFO, "batch_memory_pool_init: initialized pool with max_limit=%zu bytes", 
              max_limit);
    
    return 0;
}

/**
 * @brief 从内存池分配内存
 * @param pool 内存池指针
 * @param size 请求的内存大小
 * @return 分配的内存指针，失败返回NULL
 */
void* batch_memory_pool_alloc(struct batch_memory_pool *pool, size_t size)
{
    void *ptr = NULL;
    struct memory_block_header *header = NULL;
    size_t total_size;
    
    if (!pool || size == 0) {
        ls_syslog(LOG_ERR, "batch_memory_pool_alloc: invalid parameters");
        return NULL;
    }
    
    /* 检查内存限制 */
    if (pool->total_allocated + size > pool->max_memory_limit) {
        ls_syslog(LOG_WARNING, "batch_memory_pool_alloc: memory limit exceeded, "
                  "requested=%zu, current=%zu, limit=%zu", 
                  size, pool->total_allocated, pool->max_memory_limit);
        return NULL;
    }
    
    /* 扩展池容量（如果需要） */
    if (pool->block_count >= pool->capacity) {
        int new_capacity = pool->capacity * BATCH_MEMORY_POOL_GROWTH_FACTOR;
        void **new_blocks = (void**)realloc(pool->blocks, new_capacity * sizeof(void*));
        size_t *new_sizes = (size_t*)realloc(pool->block_sizes, new_capacity * sizeof(size_t));
        
        if (!new_blocks || !new_sizes) {
            ls_syslog(LOG_ERR, "batch_memory_pool_alloc: failed to expand pool capacity");
            return NULL;
        }
        
        pool->blocks = new_blocks;
        pool->block_sizes = new_sizes;
        pool->capacity = new_capacity;
        
        /* 初始化新分配的空间 */
        memset(&pool->blocks[pool->block_count], 0, 
               (new_capacity - pool->capacity) * sizeof(void*));
        memset(&pool->block_sizes[pool->block_count], 0, 
               (new_capacity - pool->capacity) * sizeof(size_t));
    }
    
    /* 计算总大小：头部 + 用户数据 + 保护区 */
    total_size = sizeof(struct memory_block_header) + size + BATCH_MEMORY_GUARD_SIZE;
    
    /* 分配内存 */
    ptr = malloc(total_size);
    if (!ptr) {
        ls_syslog(LOG_ERR, "batch_memory_pool_alloc: malloc failed for size %zu: %s", 
                  total_size, strerror(errno));
        return NULL;
    }
    
    /* 初始化头部 */
    header = (struct memory_block_header*)ptr;
    header->size = size;
    header->magic = BATCH_MEMORY_MAGIC_NUMBER;
    header->alloc_time = time(NULL);
    header->next = NULL;
    header->prev = NULL;
    
    /* 用户数据指针 */
    void *user_ptr = (char*)ptr + sizeof(struct memory_block_header);
    
    /* 添加保护字节 */
    add_guard_bytes(user_ptr, size);
    
    /* 记录到池中 */
    pool->blocks[pool->block_count] = ptr;
    pool->block_sizes[pool->block_count] = total_size;
    pool->block_count++;
    pool->total_allocated += total_size;
    
    /* 更新统计信息 */
    update_pool_stats(pool, size, 1);
    
    ls_syslog(LOG_DEBUG, "batch_memory_pool_alloc: allocated %zu bytes, "
              "total_allocated=%zu, block_count=%d", 
              size, pool->total_allocated, pool->block_count);
    
    return user_ptr;
}

/**
 * @brief 清理内存池
 * @param pool 内存池指针
 */
void batch_memory_pool_cleanup(struct batch_memory_pool *pool)
{
    int i;
    
    if (!pool) {
        return;
    }
    
    ls_syslog(LOG_INFO, "batch_memory_pool_cleanup: cleaning up pool, "
              "block_count=%d, total_allocated=%zu", 
              pool->block_count, pool->total_allocated);
    
    /* 释放所有内存块 */
    for (i = 0; i < pool->block_count; i++) {
        if (pool->blocks[i]) {
            struct memory_block_header *header = (struct memory_block_header*)pool->blocks[i];
            
            /* 验证内存块完整性 */
            if (validate_memory_block(header) != 0) {
                ls_syslog(LOG_WARNING, "batch_memory_pool_cleanup: "
                          "corrupted memory block detected at index %d", i);
            }
            
            free(pool->blocks[i]);
            pool->blocks[i] = NULL;
        }
    }
    
    /* 释放池结构 */
    if (pool->blocks) {
        free(pool->blocks);
        pool->blocks = NULL;
    }
    
    if (pool->block_sizes) {
        free(pool->block_sizes);
        pool->block_sizes = NULL;
    }
    
    /* 重置池状态 */
    pool->block_count = 0;
    pool->capacity = 0;
    pool->total_allocated = 0;
    
    ls_syslog(LOG_INFO, "batch_memory_pool_cleanup: cleanup completed");
}

/* ==================== 内部函数实现 ==================== */

/**
 * @brief 对齐指针
 */
static void* align_pointer(void *ptr, size_t alignment)
{
    uintptr_t addr = (uintptr_t)ptr;
    uintptr_t aligned = (addr + alignment - 1) & ~(alignment - 1);
    return (void*)aligned;
}

/**
 * @brief 验证内存块完整性
 */
static int validate_memory_block(struct memory_block_header *header)
{
    if (!header) {
        return -1;
    }
    
    if (header->magic != BATCH_MEMORY_MAGIC_NUMBER) {
        ls_syslog(LOG_ERR, "validate_memory_block: invalid magic number 0x%x", 
                  header->magic);
        return -1;
    }
    
    /* 检查保护字节 */
    void *user_ptr = (char*)header + sizeof(struct memory_block_header);
    if (check_guard_bytes(user_ptr, header->size) != 0) {
        ls_syslog(LOG_ERR, "validate_memory_block: guard bytes corrupted");
        return -1;
    }
    
    return 0;
}

/**
 * @brief 添加保护字节
 */
static void add_guard_bytes(void *ptr, size_t size)
{
    unsigned char *guard_start = (unsigned char*)ptr + size;
    int i;
    
    for (i = 0; i < BATCH_MEMORY_GUARD_SIZE; i++) {
        guard_start[i] = 0xAA + (i % 16);
    }
}

/**
 * @brief 检查保护字节
 */
static int check_guard_bytes(void *ptr, size_t size)
{
    unsigned char *guard_start = (unsigned char*)ptr + size;
    int i;
    
    for (i = 0; i < BATCH_MEMORY_GUARD_SIZE; i++) {
        if (guard_start[i] != (unsigned char)(0xAA + (i % 16))) {
            return -1;
        }
    }
    
    return 0;
}

/**
 * @brief 更新池统计信息
 */
static void update_pool_stats(struct batch_memory_pool *pool, size_t size, int is_alloc)
{
    /* 这里可以添加详细的统计信息更新逻辑 */
    /* 为了简化，暂时只记录基本信息 */
    if (is_alloc) {
        /* 分配操作 */
        if (pool->total_allocated > pool->total_allocated) {
            /* 更新峰值使用量（这里需要额外的统计结构） */
        }
    }
}
