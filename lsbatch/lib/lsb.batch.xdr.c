/**
 * @file lsb.batch.xdr.c
 * @brief 批量作业处理的XDR编解码函数
 * 
 * 实现批量作业相关数据结构的XDR序列化和反序列化
 */

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <rpc/rpc.h>

#include "../lsbatch.h"
#include "../daemons/mbd.batch.h"

/**
 * @brief 批量作业结果的XDR编解码
 */
bool_t
xdr_batchJobResult(XDR *xdrs, struct batchJobResult *objp, struct LSFHeader *hdr)
{
    if (!xdr_int(xdrs, &objp->jobIndex))
        return FALSE;
    
    if (!xdr_LS_LONG_INT(xdrs, &objp->jobId))
        return FALSE;
    
    if (!xdr_int(xdrs, &objp->status))
        return FALSE;
    
    if (!xdr_string(xdrs, &objp->queue, MAXLINELEN))
        return FALSE;
    
    if (!xdr_time_t(xdrs, &objp->submitTime))
        return FALSE;
    
    if (!xdr_int(xdrs, &objp->errorCode))
        return FALSE;
    
    if (!xdr_string(xdrs, &objp->errorMsg, MAXLINELEN))
        return FALSE;
    
    return TRUE;
}

/**
 * @brief 批量提交请求的XDR编解码
 */
bool_t
xdr_batchSubmitReq(XDR *xdrs, struct batchSubmitReq *objp, struct LSFHeader *hdr)
{
    int i;
    
    if (!xdr_int(xdrs, &objp->jobCount))
        return FALSE;
    
    if (!xdr_int(xdrs, &objp->options))
        return FALSE;
    
    if (!xdr_int(xdrs, &objp->priority))
        return FALSE;
    
    if (!xdr_string(xdrs, &objp->batchName, MAXLINELEN))
        return FALSE;
    
    if (!xdr_int(xdrs, &objp->maxConcurrentJobs))
        return FALSE;
    
    if (!xdr_int(xdrs, &objp->timeoutSeconds))
        return FALSE;
    
    /* 处理作业数组 */
    if (xdrs->x_op == XDR_DECODE) {
        if (objp->jobCount > 0) {
            objp->jobs = calloc(objp->jobCount, sizeof(struct submitReq));
            if (!objp->jobs)
                return FALSE;
        }
    }
    
    if (objp->jobs) {
        for (i = 0; i < objp->jobCount; i++) {
            if (!xdr_submitReq(xdrs, &objp->jobs[i], hdr))
                return FALSE;
        }
    }
    
    return TRUE;
}

/**
 * @brief 批量提交响应的XDR编解码
 */
bool_t
xdr_batchSubmitReply(XDR *xdrs, struct batchSubmitReply *objp, struct LSFHeader *hdr)
{
    int i;
    
    if (!xdr_int(xdrs, &objp->batchId))
        return FALSE;
    
    if (!xdr_time_t(xdrs, &objp->startTime))
        return FALSE;
    
    if (!xdr_time_t(xdrs, &objp->completionTime))
        return FALSE;
    
    if (!xdr_int(xdrs, &objp->successCount))
        return FALSE;
    
    if (!xdr_int(xdrs, &objp->failureCount))
        return FALSE;
    
    /* 处理结果数组 */
    int resultCount = objp->successCount + objp->failureCount;
    if (!xdr_int(xdrs, &resultCount))
        return FALSE;
    
    if (xdrs->x_op == XDR_DECODE) {
        if (resultCount > 0) {
            objp->results = calloc(resultCount, sizeof(struct batchJobResult));
            if (!objp->results)
                return FALSE;
        }
    }
    
    if (objp->results) {
        for (i = 0; i < resultCount; i++) {
            if (!xdr_batchJobResult(xdrs, &objp->results[i], hdr))
                return FALSE;
        }
    }
    
    return TRUE;
}

/**
 * @brief 流式消息头的XDR编解码
 */
bool_t
xdr_streamHeader(XDR *xdrs, struct streamHeader *objp, struct LSFHeader *hdr)
{
    if (!xdr_enum(xdrs, (enum_t *)&objp->messageType))
        return FALSE;
    
    if (!xdr_int(xdrs, &objp->sequenceId))
        return FALSE;
    
    if (!xdr_int(xdrs, &objp->batchId))
        return FALSE;
    
    if (!xdr_int(xdrs, &objp->flags))
        return FALSE;
    
    if (!xdr_time_t(xdrs, &objp->timestamp))
        return FALSE;
    
    return TRUE;
}

/**
 * @brief 流式作业结果的XDR编解码
 */
bool_t
xdr_streamJobResult(XDR *xdrs, struct streamJobResult *objp, struct LSFHeader *hdr)
{
    if (!xdr_int(xdrs, &objp->batchId))
        return FALSE;
    
    if (!xdr_int(xdrs, &objp->jobIndex))
        return FALSE;
    
    if (!xdr_LS_LONG_INT(xdrs, &objp->jobId))
        return FALSE;
    
    if (!xdr_int(xdrs, &objp->status))
        return FALSE;
    
    if (!xdr_string(xdrs, &objp->queue, MAXLINELEN))
        return FALSE;
    
    if (!xdr_int(xdrs, &objp->resultCode))
        return FALSE;
    
    if (!xdr_time_t(xdrs, &objp->timestamp))
        return FALSE;
    
    return TRUE;
}

/**
 * @brief 批量完成信号的XDR编解码
 */
bool_t
xdr_batchCompleteSignal(XDR *xdrs, struct batchCompleteSignal *objp, struct LSFHeader *hdr)
{
    if (!xdr_int(xdrs, &objp->batchId))
        return FALSE;
    
    if (!xdr_int(xdrs, &objp->totalJobs))
        return FALSE;
    
    if (!xdr_int(xdrs, &objp->successCount))
        return FALSE;
    
    if (!xdr_int(xdrs, &objp->failureCount))
        return FALSE;
    
    if (!xdr_time_t(xdrs, &objp->completionTime))
        return FALSE;
    
    return TRUE;
}

/**
 * @brief 批量状态查询请求的XDR编解码
 */
bool_t
xdr_batchStatusReq(XDR *xdrs, struct batchStatusReq *objp, struct LSFHeader *hdr)
{
    if (!xdr_int(xdrs, &objp->batchId))
        return FALSE;
    
    if (!xdr_int(xdrs, &objp->options))
        return FALSE;
    
    return TRUE;
}

/**
 * @brief 批量状态查询响应的XDR编解码
 */
bool_t
xdr_batchStatusReply(XDR *xdrs, struct batchStatusReply *objp, struct LSFHeader *hdr)
{
    int i;
    
    if (!xdr_int(xdrs, &objp->batchId))
        return FALSE;
    
    if (!xdr_int(xdrs, &objp->totalJobs))
        return FALSE;
    
    if (!xdr_int(xdrs, &objp->submittedJobs))
        return FALSE;
    
    if (!xdr_int(xdrs, &objp->completedJobs))
        return FALSE;
    
    if (!xdr_int(xdrs, &objp->failedJobs))
        return FALSE;
    
    if (!xdr_int(xdrs, &objp->runningJobs))
        return FALSE;
    
    if (!xdr_int(xdrs, &objp->pendingJobs))
        return FALSE;
    
    if (!xdr_time_t(xdrs, &objp->startTime))
        return FALSE;
    
    if (!xdr_time_t(xdrs, &objp->lastUpdateTime))
        return FALSE;
    
    /* 处理详细结果数组（可选） */
    int hasResults = (objp->results != NULL) ? 1 : 0;
    if (!xdr_int(xdrs, &hasResults))
        return FALSE;
    
    if (hasResults) {
        if (xdrs->x_op == XDR_DECODE) {
            if (objp->totalJobs > 0) {
                objp->results = calloc(objp->totalJobs, sizeof(struct batchJobResult));
                if (!objp->results)
                    return FALSE;
            }
        }
        
        if (objp->results) {
            for (i = 0; i < objp->totalJobs; i++) {
                if (!xdr_batchJobResult(xdrs, &objp->results[i], hdr))
                    return FALSE;
            }
        }
    }
    
    return TRUE;
}

/**
 * @brief 辅助函数：编解码time_t类型
 */
bool_t
xdr_time_t(XDR *xdrs, time_t *objp)
{
    long temp;
    
    if (xdrs->x_op == XDR_ENCODE) {
        temp = (long)*objp;
        return xdr_long(xdrs, &temp);
    } else if (xdrs->x_op == XDR_DECODE) {
        if (!xdr_long(xdrs, &temp))
            return FALSE;
        *objp = (time_t)temp;
        return TRUE;
    } else { /* XDR_FREE */
        return TRUE;
    }
}
