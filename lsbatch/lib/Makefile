# Makefile.in generated by automake 1.16.1 from Makefile.am.
# lsbatch/lib/Makefile.  Generated from Makefile.in by configure.

# Copyright (C) 1994-2018 Free Software Foundation, Inc.

# This Makefile.in is free software; the Free Software Foundation
# gives unlimited permission to copy and/or distribute it,
# with or without modifications, as long as this notice is preserved.

# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY, to the extent permitted by law; without
# even the implied warranty of MERCHANTABILITY or FITNESS FOR A
# PARTICULAR PURPOSE.




am__is_gnu_make = { \
  if test -z '$(MAKELEVEL)'; then \
    false; \
  elif test -n '$(MAKE_HOST)'; then \
    true; \
  elif test -n '$(MAKE_VERSION)' && test -n '$(CURDIR)'; then \
    true; \
  else \
    false; \
  fi; \
}
am__make_running_with_option = \
  case $${target_option-} in \
      ?) ;; \
      *) echo "am__make_running_with_option: internal error: invalid" \
              "target option '$${target_option-}' specified" >&2; \
         exit 1;; \
  esac; \
  has_opt=no; \
  sane_makeflags=$$MAKEFLAGS; \
  if $(am__is_gnu_make); then \
    sane_makeflags=$$MFLAGS; \
  else \
    case $$MAKEFLAGS in \
      *\\[\ \	]*) \
        bs=\\; \
        sane_makeflags=`printf '%s\n' "$$MAKEFLAGS" \
          | sed "s/$$bs$$bs[$$bs $$bs	]*//g"`;; \
    esac; \
  fi; \
  skip_next=no; \
  strip_trailopt () \
  { \
    flg=`printf '%s\n' "$$flg" | sed "s/$$1.*$$//"`; \
  }; \
  for flg in $$sane_makeflags; do \
    test $$skip_next = yes && { skip_next=no; continue; }; \
    case $$flg in \
      *=*|--*) continue;; \
        -*I) strip_trailopt 'I'; skip_next=yes;; \
      -*I?*) strip_trailopt 'I';; \
        -*O) strip_trailopt 'O'; skip_next=yes;; \
      -*O?*) strip_trailopt 'O';; \
        -*l) strip_trailopt 'l'; skip_next=yes;; \
      -*l?*) strip_trailopt 'l';; \
      -[dEDm]) skip_next=yes;; \
      -[JT]) skip_next=yes;; \
    esac; \
    case $$flg in \
      *$$target_option*) has_opt=yes; break;; \
    esac; \
  done; \
  test $$has_opt = yes
am__make_dryrun = (target_option=n; $(am__make_running_with_option))
am__make_keepgoing = (target_option=k; $(am__make_running_with_option))
pkgdatadir = $(datadir)/volclava
pkgincludedir = $(includedir)/volclava
pkglibdir = $(libdir)/volclava
pkglibexecdir = $(libexecdir)/volclava
am__cd = CDPATH="$${ZSH_VERSION+.}$(PATH_SEPARATOR)" && cd
install_sh_DATA = $(install_sh) -c -m 644
install_sh_PROGRAM = $(install_sh) -c
install_sh_SCRIPT = $(install_sh) -c
INSTALL_HEADER = $(INSTALL_DATA)
transform = $(program_transform_name)
NORMAL_INSTALL = :
PRE_INSTALL = :
POST_INSTALL = :
NORMAL_UNINSTALL = :
PRE_UNINSTALL = :
POST_UNINSTALL = :
build_triplet = x86_64-pc-linux-gnu
host_triplet = x86_64-pc-linux-gnu
target_triplet = x86_64-pc-linux-gnu
subdir = lsbatch/lib
ACLOCAL_M4 = $(top_srcdir)/aclocal.m4
am__aclocal_m4_deps = $(top_srcdir)/configure.ac
am__configure_deps = $(am__aclocal_m4_deps) $(CONFIGURE_DEPENDENCIES) \
	$(ACLOCAL_M4)
DIST_COMMON = $(srcdir)/Makefile.am $(am__DIST_COMMON)
mkinstalldirs = $(install_sh) -d
CONFIG_HEADER = $(top_builddir)/config.h
CONFIG_CLEAN_FILES =
CONFIG_CLEAN_VPATH_FILES =
am__vpath_adj_setup = srcdirstrip=`echo "$(srcdir)" | sed 's|.|.|g'`;
am__vpath_adj = case $$p in \
    $(srcdir)/*) f=`echo "$$p" | sed "s|^$$srcdirstrip/||"`;; \
    *) f=$$p;; \
  esac;
am__strip_dir = f=`echo $$p | sed -e 's|^.*/||'`;
am__install_max = 40
am__nobase_strip_setup = \
  srcdirstrip=`echo "$(srcdir)" | sed 's/[].[^$$\\*|]/\\\\&/g'`
am__nobase_strip = \
  for p in $$list; do echo "$$p"; done | sed -e "s|$$srcdirstrip/||"
am__nobase_list = $(am__nobase_strip_setup); \
  for p in $$list; do echo "$$p $$p"; done | \
  sed "s| $$srcdirstrip/| |;"' / .*\//!s/ .*/ ./; s,\( .*\)/[^/]*$$,\1,' | \
  $(AWK) 'BEGIN { files["."] = "" } { files[$$2] = files[$$2] " " $$1; \
    if (++n[$$2] == $(am__install_max)) \
      { print $$2, files[$$2]; n[$$2] = 0; files[$$2] = "" } } \
    END { for (dir in files) print dir, files[dir] }'
am__base_list = \
  sed '$$!N;$$!N;$$!N;$$!N;$$!N;$$!N;$$!N;s/\n/ /g' | \
  sed '$$!N;$$!N;$$!N;$$!N;s/\n/ /g'
am__uninstall_files_from_dir = { \
  test -z "$$files" \
    || { test ! -d "$$dir" && test ! -f "$$dir" && test ! -r "$$dir"; } \
    || { echo " ( cd '$$dir' && rm -f" $$files ")"; \
         $(am__cd) "$$dir" && rm -f $$files; }; \
  }
am__installdirs = "$(DESTDIR)$(libdir)"
LIBRARIES = $(lib_LIBRARIES)
AR = ar
ARFLAGS = cru
AM_V_AR = $(am__v_AR_$(V))
am__v_AR_ = $(am__v_AR_$(AM_DEFAULT_VERBOSITY))
am__v_AR_0 = @echo "  AR      " $@;
am__v_AR_1 = 
liblsbatch_a_AR = $(AR) $(ARFLAGS)
liblsbatch_a_LIBADD =
am_liblsbatch_a_OBJECTS = lsb.comm.$(OBJEXT) lsb.groups.$(OBJEXT) \
	lsb.jobs.$(OBJEXT) lsb.modify.$(OBJEXT) lsb.peek.$(OBJEXT) \
	lsb.reconfig.$(OBJEXT) lsb.signals.$(OBJEXT) \
	lsb.users.$(OBJEXT) lsb.conf.$(OBJEXT) lsb.hc.$(OBJEXT) \
	lsb.log.$(OBJEXT) lsb.move.$(OBJEXT) lsb.qc.$(OBJEXT) \
	lsb.resource.$(OBJEXT) lsb.spool.$(OBJEXT) lsb.xdr.$(OBJEXT) \
	lsb.debug.$(OBJEXT) lsb.hosts.$(OBJEXT) lsb.mig.$(OBJEXT) \
	lsb.msg.$(OBJEXT) lsb.queues.$(OBJEXT) lsb.rexecv.$(OBJEXT) \
	lsb.sub.$(OBJEXT) lsb.err.$(OBJEXT) lsb.init.$(OBJEXT) \
	lsb.misc.$(OBJEXT) lsb.params.$(OBJEXT) lsb.reason.$(OBJEXT) \
	lsb.sig.$(OBJEXT) lsb.switch.$(OBJEXT) batch_memory.$(OBJEXT)
liblsbatch_a_OBJECTS = $(am_liblsbatch_a_OBJECTS)
AM_V_P = $(am__v_P_$(V))
am__v_P_ = $(am__v_P_$(AM_DEFAULT_VERBOSITY))
am__v_P_0 = false
am__v_P_1 = :
AM_V_GEN = $(am__v_GEN_$(V))
am__v_GEN_ = $(am__v_GEN_$(AM_DEFAULT_VERBOSITY))
am__v_GEN_0 = @echo "  GEN     " $@;
am__v_GEN_1 = 
AM_V_at = $(am__v_at_$(V))
am__v_at_ = $(am__v_at_$(AM_DEFAULT_VERBOSITY))
am__v_at_0 = @
am__v_at_1 = 
DEFAULT_INCLUDES = -I. -I$(top_builddir)
depcomp = $(SHELL) $(top_srcdir)/depcomp
am__maybe_remake_depfiles = depfiles
am__depfiles_remade = ./$(DEPDIR)/batch_memory.Po \
	./$(DEPDIR)/lsb.comm.Po ./$(DEPDIR)/lsb.conf.Po \
	./$(DEPDIR)/lsb.debug.Po ./$(DEPDIR)/lsb.err.Po \
	./$(DEPDIR)/lsb.groups.Po ./$(DEPDIR)/lsb.hc.Po \
	./$(DEPDIR)/lsb.hosts.Po ./$(DEPDIR)/lsb.init.Po \
	./$(DEPDIR)/lsb.jobs.Po ./$(DEPDIR)/lsb.log.Po \
	./$(DEPDIR)/lsb.mig.Po ./$(DEPDIR)/lsb.misc.Po \
	./$(DEPDIR)/lsb.modify.Po ./$(DEPDIR)/lsb.move.Po \
	./$(DEPDIR)/lsb.msg.Po ./$(DEPDIR)/lsb.params.Po \
	./$(DEPDIR)/lsb.peek.Po ./$(DEPDIR)/lsb.qc.Po \
	./$(DEPDIR)/lsb.queues.Po ./$(DEPDIR)/lsb.reason.Po \
	./$(DEPDIR)/lsb.reconfig.Po ./$(DEPDIR)/lsb.resource.Po \
	./$(DEPDIR)/lsb.rexecv.Po ./$(DEPDIR)/lsb.sig.Po \
	./$(DEPDIR)/lsb.signals.Po ./$(DEPDIR)/lsb.spool.Po \
	./$(DEPDIR)/lsb.sub.Po ./$(DEPDIR)/lsb.switch.Po \
	./$(DEPDIR)/lsb.users.Po ./$(DEPDIR)/lsb.xdr.Po
am__mv = mv -f
COMPILE = $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) \
	$(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS)
AM_V_CC = $(am__v_CC_$(V))
am__v_CC_ = $(am__v_CC_$(AM_DEFAULT_VERBOSITY))
am__v_CC_0 = @echo "  CC      " $@;
am__v_CC_1 = 
CCLD = $(CC)
LINK = $(CCLD) $(AM_CFLAGS) $(CFLAGS) $(AM_LDFLAGS) $(LDFLAGS) -o $@
AM_V_CCLD = $(am__v_CCLD_$(V))
am__v_CCLD_ = $(am__v_CCLD_$(AM_DEFAULT_VERBOSITY))
am__v_CCLD_0 = @echo "  CCLD    " $@;
am__v_CCLD_1 = 
SOURCES = $(liblsbatch_a_SOURCES)
DIST_SOURCES = $(liblsbatch_a_SOURCES)
am__can_run_installinfo = \
  case $$AM_UPDATE_INFO_DIR in \
    n|no|NO) false;; \
    *) (install-info --version) >/dev/null 2>&1;; \
  esac
am__tagged_files = $(HEADERS) $(SOURCES) $(TAGS_FILES) $(LISP)
# Read a list of newline-separated strings from the standard input,
# and print each of them once, without duplicates.  Input order is
# *not* preserved.
am__uniquify_input = $(AWK) '\
  BEGIN { nonempty = 0; } \
  { items[$$0] = 1; nonempty = 1; } \
  END { if (nonempty) { for (i in items) print i; }; } \
'
# Make sure the list of sources is unique.  This is necessary because,
# e.g., the same source file might be shared among _SOURCES variables
# for different programs/libraries.
am__define_uniq_tagged_files = \
  list='$(am__tagged_files)'; \
  unique=`for i in $$list; do \
    if test -f "$$i"; then echo $$i; else echo $(srcdir)/$$i; fi; \
  done | $(am__uniquify_input)`
ETAGS = etags
CTAGS = ctags
am__DIST_COMMON = $(srcdir)/Makefile.in $(top_srcdir)/depcomp \
	ChangeLog
DISTFILES = $(DIST_COMMON) $(DIST_SOURCES) $(TEXINFOS) $(EXTRA_DIST)
ACLOCAL = ${SHELL} /mnt/hgfs/ubuntu_share/volclava/missing aclocal-1.16
AMTAR = $${TAR-tar}
AM_DEFAULT_VERBOSITY = 0
AUTOCONF = ${SHELL} /mnt/hgfs/ubuntu_share/volclava/missing autoconf
AUTOHEADER = ${SHELL} /mnt/hgfs/ubuntu_share/volclava/missing autoheader
AUTOMAKE = ${SHELL} /mnt/hgfs/ubuntu_share/volclava/missing automake-1.16
AWK = mawk
CC = gcc
CCDEPMODE = depmode=gcc3
CFLAGS = -g -O2 -Wall -fPIC -Wno-error=format-security -I/usr/include/tirpc
CPP = gcc -E
CPPFLAGS = 
CYGPATH_W = echo
DEFS = -DHAVE_CONFIG_H
DEPDIR = .deps
ECHO_C = 
ECHO_N = -n
ECHO_T = 
EGREP = /usr/bin/grep -E
EXEEXT = 
GREP = /usr/bin/grep
INSTALL = /usr/bin/install -c
INSTALL_DATA = ${INSTALL} -m 644
INSTALL_PROGRAM = ${INSTALL}
INSTALL_SCRIPT = ${INSTALL}
INSTALL_STRIP_PROGRAM = $(install_sh) -c -s
LDFLAGS = 
LEX = :
LEXLIB = 
LEX_OUTPUT_ROOT = 
LIBOBJS = 
LIBS = -ltcl 
LN_S = cp -pR
LTLIBOBJS = 
MAKEINFO = ${SHELL} /mnt/hgfs/ubuntu_share/volclava/missing makeinfo
MKDIR_P = /usr/bin/mkdir -p
OBJEXT = o
PACKAGE = volclava
PACKAGE_BUGREPORT = 
PACKAGE_NAME = volclava
PACKAGE_STRING = volclava 2.0
PACKAGE_TARNAME = volclava
PACKAGE_URL = 
PACKAGE_VERSION = 2.0
PATH_SEPARATOR = :
RANLIB = ranlib
SET_MAKE = 
SHELL = /bin/bash
STRIP = 
VERSION = 2.0
YACC = yacc
YFLAGS = 
abs_builddir = /mnt/hgfs/ubuntu_share/volclava/lsbatch/lib
abs_srcdir = /mnt/hgfs/ubuntu_share/volclava/lsbatch/lib
abs_top_builddir = /mnt/hgfs/ubuntu_share/volclava
abs_top_srcdir = /mnt/hgfs/ubuntu_share/volclava
ac_ct_CC = gcc
am__include = include
am__leading_dot = .
am__quote = 
am__tar = $${TAR-tar} chof - "$$tardir"
am__untar = $${TAR-tar} xf -
bindir = ${exec_prefix}/bin
build = x86_64-pc-linux-gnu
build_alias = 
build_cpu = x86_64
build_os = linux-gnu
build_vendor = pc
builddir = .
datadir = ${datarootdir}
datarootdir = ${prefix}/share
docdir = ${datarootdir}/doc/${PACKAGE_TARNAME}
dvidir = ${docdir}
exec_prefix = ${prefix}
host = x86_64-pc-linux-gnu
host_alias = 
host_cpu = x86_64
host_os = linux-gnu
host_vendor = pc
htmldir = ${docdir}
includedir = ${prefix}/include
infodir = ${datarootdir}/info
install_sh = ${SHELL} /mnt/hgfs/ubuntu_share/volclava/install-sh
libdir = ${exec_prefix}/lib
libexecdir = ${exec_prefix}/libexec
localedir = ${datarootdir}/locale
localstatedir = ${prefix}/var
mandir = ${datarootdir}/man
mkdir_p = $(MKDIR_P)
oldincludedir = /usr/include
pdfdir = ${docdir}
prefix = /opt/volclava-2.0
program_transform_name = s,x,x,
psdir = ${docdir}
runstatedir = ${localstatedir}/run
sbindir = ${exec_prefix}/sbin
sharedstatedir = ${prefix}/com
srcdir = .
sysconfdir = ${prefix}/etc
target = x86_64-pc-linux-gnu
target_alias = 
target_cpu = x86_64
target_os = linux-gnu
target_vendor = pc
top_build_prefix = ../../
top_builddir = ../..
top_srcdir = ../..
volclavaadmin = 
volclavacluster = volclava
volclavadmin = volclava

#
# Copyright (C) 2011 openlava foundation
#
INCLUDES = -I$(top_srcdir)/lsf -I$(top_srcdir)/lsf/lib \
           -I$(top_srcdir)/lsbatch -I./

lib_LIBRARIES = liblsbatch.a
liblsbatch_a_SOURCES = \
lsb.comm.c lsb.groups.c lsb.jobs.c lsb.modify.c lsb.peek.c lsb.reconfig.c \
lsb.signals.c lsb.users.c lsb.conf.c lsb.hc.c lsb.log.c lsb.move.c \
lsb.qc.c lsb.resource.c lsb.spool.c lsb.xdr.c lsb.debug.c lsb.hosts.c \
lsb.mig.c lsb.msg.c lsb.queues.c lsb.rexecv.c \
lsb.sub.c lsb.err.c lsb.init.c lsb.misc.c lsb.params.c lsb.reason.c \
lsb.sig.c lsb.switch.c batch_memory.c \
lsb.conf.h  lsb.h  lsb.log.h  lsb.sig.h  lsb.spool.h  lsb.xdr.h batch_memory.h

all: all-am

.SUFFIXES:
.SUFFIXES: .c .o .obj
$(srcdir)/Makefile.in:  $(srcdir)/Makefile.am  $(am__configure_deps)
	@for dep in $?; do \
	  case '$(am__configure_deps)' in \
	    *$$dep*) \
	      ( cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh ) \
	        && { if test -f $@; then exit 0; else break; fi; }; \
	      exit 1;; \
	  esac; \
	done; \
	echo ' cd $(top_srcdir) && $(AUTOMAKE) --gnu lsbatch/lib/Makefile'; \
	$(am__cd) $(top_srcdir) && \
	  $(AUTOMAKE) --gnu lsbatch/lib/Makefile
Makefile: $(srcdir)/Makefile.in $(top_builddir)/config.status
	@case '$?' in \
	  *config.status*) \
	    cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh;; \
	  *) \
	    echo ' cd $(top_builddir) && $(SHELL) ./config.status $(subdir)/$@ $(am__maybe_remake_depfiles)'; \
	    cd $(top_builddir) && $(SHELL) ./config.status $(subdir)/$@ $(am__maybe_remake_depfiles);; \
	esac;

$(top_builddir)/config.status: $(top_srcdir)/configure $(CONFIG_STATUS_DEPENDENCIES)
	cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh

$(top_srcdir)/configure:  $(am__configure_deps)
	cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh
$(ACLOCAL_M4):  $(am__aclocal_m4_deps)
	cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh
$(am__aclocal_m4_deps):
install-libLIBRARIES: $(lib_LIBRARIES)
	@$(NORMAL_INSTALL)
	@list='$(lib_LIBRARIES)'; test -n "$(libdir)" || list=; \
	list2=; for p in $$list; do \
	  if test -f $$p; then \
	    list2="$$list2 $$p"; \
	  else :; fi; \
	done; \
	test -z "$$list2" || { \
	  echo " $(MKDIR_P) '$(DESTDIR)$(libdir)'"; \
	  $(MKDIR_P) "$(DESTDIR)$(libdir)" || exit 1; \
	  echo " $(INSTALL_DATA) $$list2 '$(DESTDIR)$(libdir)'"; \
	  $(INSTALL_DATA) $$list2 "$(DESTDIR)$(libdir)" || exit $$?; }
	@$(POST_INSTALL)
	@list='$(lib_LIBRARIES)'; test -n "$(libdir)" || list=; \
	for p in $$list; do \
	  if test -f $$p; then \
	    $(am__strip_dir) \
	    echo " ( cd '$(DESTDIR)$(libdir)' && $(RANLIB) $$f )"; \
	    ( cd "$(DESTDIR)$(libdir)" && $(RANLIB) $$f ) || exit $$?; \
	  else :; fi; \
	done

uninstall-libLIBRARIES:
	@$(NORMAL_UNINSTALL)
	@list='$(lib_LIBRARIES)'; test -n "$(libdir)" || list=; \
	files=`for p in $$list; do echo $$p; done | sed -e 's|^.*/||'`; \
	dir='$(DESTDIR)$(libdir)'; $(am__uninstall_files_from_dir)

clean-libLIBRARIES:
	-test -z "$(lib_LIBRARIES)" || rm -f $(lib_LIBRARIES)

liblsbatch.a: $(liblsbatch_a_OBJECTS) $(liblsbatch_a_DEPENDENCIES) $(EXTRA_liblsbatch_a_DEPENDENCIES) 
	$(AM_V_at)-rm -f liblsbatch.a
	$(AM_V_AR)$(liblsbatch_a_AR) liblsbatch.a $(liblsbatch_a_OBJECTS) $(liblsbatch_a_LIBADD)
	$(AM_V_at)$(RANLIB) liblsbatch.a

mostlyclean-compile:
	-rm -f *.$(OBJEXT)

distclean-compile:
	-rm -f *.tab.c

include ./$(DEPDIR)/batch_memory.Po # am--include-marker
include ./$(DEPDIR)/lsb.comm.Po # am--include-marker
include ./$(DEPDIR)/lsb.conf.Po # am--include-marker
include ./$(DEPDIR)/lsb.debug.Po # am--include-marker
include ./$(DEPDIR)/lsb.err.Po # am--include-marker
include ./$(DEPDIR)/lsb.groups.Po # am--include-marker
include ./$(DEPDIR)/lsb.hc.Po # am--include-marker
include ./$(DEPDIR)/lsb.hosts.Po # am--include-marker
include ./$(DEPDIR)/lsb.init.Po # am--include-marker
include ./$(DEPDIR)/lsb.jobs.Po # am--include-marker
include ./$(DEPDIR)/lsb.log.Po # am--include-marker
include ./$(DEPDIR)/lsb.mig.Po # am--include-marker
include ./$(DEPDIR)/lsb.misc.Po # am--include-marker
include ./$(DEPDIR)/lsb.modify.Po # am--include-marker
include ./$(DEPDIR)/lsb.move.Po # am--include-marker
include ./$(DEPDIR)/lsb.msg.Po # am--include-marker
include ./$(DEPDIR)/lsb.params.Po # am--include-marker
include ./$(DEPDIR)/lsb.peek.Po # am--include-marker
include ./$(DEPDIR)/lsb.qc.Po # am--include-marker
include ./$(DEPDIR)/lsb.queues.Po # am--include-marker
include ./$(DEPDIR)/lsb.reason.Po # am--include-marker
include ./$(DEPDIR)/lsb.reconfig.Po # am--include-marker
include ./$(DEPDIR)/lsb.resource.Po # am--include-marker
include ./$(DEPDIR)/lsb.rexecv.Po # am--include-marker
include ./$(DEPDIR)/lsb.sig.Po # am--include-marker
include ./$(DEPDIR)/lsb.signals.Po # am--include-marker
include ./$(DEPDIR)/lsb.spool.Po # am--include-marker
include ./$(DEPDIR)/lsb.sub.Po # am--include-marker
include ./$(DEPDIR)/lsb.switch.Po # am--include-marker
include ./$(DEPDIR)/lsb.users.Po # am--include-marker
include ./$(DEPDIR)/lsb.xdr.Po # am--include-marker

$(am__depfiles_remade):
	@$(MKDIR_P) $(@D)
	@echo '# dummy' >$@-t && $(am__mv) $@-t $@

am--depfiles: $(am__depfiles_remade)

.c.o:
	$(AM_V_CC)$(COMPILE) -MT $@ -MD -MP -MF $(DEPDIR)/$*.Tpo -c -o $@ $<
	$(AM_V_at)$(am__mv) $(DEPDIR)/$*.Tpo $(DEPDIR)/$*.Po
#	$(AM_V_CC)source='$<' object='$@' libtool=no \
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) \
#	$(AM_V_CC_no)$(COMPILE) -c -o $@ $<

.c.obj:
	$(AM_V_CC)$(COMPILE) -MT $@ -MD -MP -MF $(DEPDIR)/$*.Tpo -c -o $@ `$(CYGPATH_W) '$<'`
	$(AM_V_at)$(am__mv) $(DEPDIR)/$*.Tpo $(DEPDIR)/$*.Po
#	$(AM_V_CC)source='$<' object='$@' libtool=no \
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) \
#	$(AM_V_CC_no)$(COMPILE) -c -o $@ `$(CYGPATH_W) '$<'`

ID: $(am__tagged_files)
	$(am__define_uniq_tagged_files); mkid -fID $$unique
tags: tags-am
TAGS: tags

tags-am: $(TAGS_DEPENDENCIES) $(am__tagged_files)
	set x; \
	here=`pwd`; \
	$(am__define_uniq_tagged_files); \
	shift; \
	if test -z "$(ETAGS_ARGS)$$*$$unique"; then :; else \
	  test -n "$$unique" || unique=$$empty_fix; \
	  if test $$# -gt 0; then \
	    $(ETAGS) $(ETAGSFLAGS) $(AM_ETAGSFLAGS) $(ETAGS_ARGS) \
	      "$$@" $$unique; \
	  else \
	    $(ETAGS) $(ETAGSFLAGS) $(AM_ETAGSFLAGS) $(ETAGS_ARGS) \
	      $$unique; \
	  fi; \
	fi
ctags: ctags-am

CTAGS: ctags
ctags-am: $(TAGS_DEPENDENCIES) $(am__tagged_files)
	$(am__define_uniq_tagged_files); \
	test -z "$(CTAGS_ARGS)$$unique" \
	  || $(CTAGS) $(CTAGSFLAGS) $(AM_CTAGSFLAGS) $(CTAGS_ARGS) \
	     $$unique

GTAGS:
	here=`$(am__cd) $(top_builddir) && pwd` \
	  && $(am__cd) $(top_srcdir) \
	  && gtags -i $(GTAGS_ARGS) "$$here"
cscopelist: cscopelist-am

cscopelist-am: $(am__tagged_files)
	list='$(am__tagged_files)'; \
	case "$(srcdir)" in \
	  [\\/]* | ?:[\\/]*) sdir="$(srcdir)" ;; \
	  *) sdir=$(subdir)/$(srcdir) ;; \
	esac; \
	for i in $$list; do \
	  if test -f "$$i"; then \
	    echo "$(subdir)/$$i"; \
	  else \
	    echo "$$sdir/$$i"; \
	  fi; \
	done >> $(top_builddir)/cscope.files

distclean-tags:
	-rm -f TAGS ID GTAGS GRTAGS GSYMS GPATH tags

distdir: $(BUILT_SOURCES)
	$(MAKE) $(AM_MAKEFLAGS) distdir-am

distdir-am: $(DISTFILES)
	@srcdirstrip=`echo "$(srcdir)" | sed 's/[].[^$$\\*]/\\\\&/g'`; \
	topsrcdirstrip=`echo "$(top_srcdir)" | sed 's/[].[^$$\\*]/\\\\&/g'`; \
	list='$(DISTFILES)'; \
	  dist_files=`for file in $$list; do echo $$file; done | \
	  sed -e "s|^$$srcdirstrip/||;t" \
	      -e "s|^$$topsrcdirstrip/|$(top_builddir)/|;t"`; \
	case $$dist_files in \
	  */*) $(MKDIR_P) `echo "$$dist_files" | \
			   sed '/\//!d;s|^|$(distdir)/|;s,/[^/]*$$,,' | \
			   sort -u` ;; \
	esac; \
	for file in $$dist_files; do \
	  if test -f $$file || test -d $$file; then d=.; else d=$(srcdir); fi; \
	  if test -d $$d/$$file; then \
	    dir=`echo "/$$file" | sed -e 's,/[^/]*$$,,'`; \
	    if test -d "$(distdir)/$$file"; then \
	      find "$(distdir)/$$file" -type d ! -perm -700 -exec chmod u+rwx {} \;; \
	    fi; \
	    if test -d $(srcdir)/$$file && test $$d != $(srcdir); then \
	      cp -fpR $(srcdir)/$$file "$(distdir)$$dir" || exit 1; \
	      find "$(distdir)/$$file" -type d ! -perm -700 -exec chmod u+rwx {} \;; \
	    fi; \
	    cp -fpR $$d/$$file "$(distdir)$$dir" || exit 1; \
	  else \
	    test -f "$(distdir)/$$file" \
	    || cp -p $$d/$$file "$(distdir)/$$file" \
	    || exit 1; \
	  fi; \
	done
check-am: all-am
check: check-am
all-am: Makefile $(LIBRARIES)
installdirs:
	for dir in "$(DESTDIR)$(libdir)"; do \
	  test -z "$$dir" || $(MKDIR_P) "$$dir"; \
	done
install: install-am
install-exec: install-exec-am
install-data: install-data-am
uninstall: uninstall-am

install-am: all-am
	@$(MAKE) $(AM_MAKEFLAGS) install-exec-am install-data-am

installcheck: installcheck-am
install-strip:
	if test -z '$(STRIP)'; then \
	  $(MAKE) $(AM_MAKEFLAGS) INSTALL_PROGRAM="$(INSTALL_STRIP_PROGRAM)" \
	    install_sh_PROGRAM="$(INSTALL_STRIP_PROGRAM)" INSTALL_STRIP_FLAG=-s \
	      install; \
	else \
	  $(MAKE) $(AM_MAKEFLAGS) INSTALL_PROGRAM="$(INSTALL_STRIP_PROGRAM)" \
	    install_sh_PROGRAM="$(INSTALL_STRIP_PROGRAM)" INSTALL_STRIP_FLAG=-s \
	    "INSTALL_PROGRAM_ENV=STRIPPROG='$(STRIP)'" install; \
	fi
mostlyclean-generic:

clean-generic:

distclean-generic:
	-test -z "$(CONFIG_CLEAN_FILES)" || rm -f $(CONFIG_CLEAN_FILES)
	-test . = "$(srcdir)" || test -z "$(CONFIG_CLEAN_VPATH_FILES)" || rm -f $(CONFIG_CLEAN_VPATH_FILES)

maintainer-clean-generic:
	@echo "This command is intended for maintainers to use"
	@echo "it deletes files that may require special tools to rebuild."
clean: clean-am

clean-am: clean-generic clean-libLIBRARIES mostlyclean-am

distclean: distclean-am
		-rm -f ./$(DEPDIR)/batch_memory.Po
	-rm -f ./$(DEPDIR)/lsb.comm.Po
	-rm -f ./$(DEPDIR)/lsb.conf.Po
	-rm -f ./$(DEPDIR)/lsb.debug.Po
	-rm -f ./$(DEPDIR)/lsb.err.Po
	-rm -f ./$(DEPDIR)/lsb.groups.Po
	-rm -f ./$(DEPDIR)/lsb.hc.Po
	-rm -f ./$(DEPDIR)/lsb.hosts.Po
	-rm -f ./$(DEPDIR)/lsb.init.Po
	-rm -f ./$(DEPDIR)/lsb.jobs.Po
	-rm -f ./$(DEPDIR)/lsb.log.Po
	-rm -f ./$(DEPDIR)/lsb.mig.Po
	-rm -f ./$(DEPDIR)/lsb.misc.Po
	-rm -f ./$(DEPDIR)/lsb.modify.Po
	-rm -f ./$(DEPDIR)/lsb.move.Po
	-rm -f ./$(DEPDIR)/lsb.msg.Po
	-rm -f ./$(DEPDIR)/lsb.params.Po
	-rm -f ./$(DEPDIR)/lsb.peek.Po
	-rm -f ./$(DEPDIR)/lsb.qc.Po
	-rm -f ./$(DEPDIR)/lsb.queues.Po
	-rm -f ./$(DEPDIR)/lsb.reason.Po
	-rm -f ./$(DEPDIR)/lsb.reconfig.Po
	-rm -f ./$(DEPDIR)/lsb.resource.Po
	-rm -f ./$(DEPDIR)/lsb.rexecv.Po
	-rm -f ./$(DEPDIR)/lsb.sig.Po
	-rm -f ./$(DEPDIR)/lsb.signals.Po
	-rm -f ./$(DEPDIR)/lsb.spool.Po
	-rm -f ./$(DEPDIR)/lsb.sub.Po
	-rm -f ./$(DEPDIR)/lsb.switch.Po
	-rm -f ./$(DEPDIR)/lsb.users.Po
	-rm -f ./$(DEPDIR)/lsb.xdr.Po
	-rm -f Makefile
distclean-am: clean-am distclean-compile distclean-generic \
	distclean-tags

dvi: dvi-am

dvi-am:

html: html-am

html-am:

info: info-am

info-am:

install-data-am:

install-dvi: install-dvi-am

install-dvi-am:

install-exec-am: install-libLIBRARIES

install-html: install-html-am

install-html-am:

install-info: install-info-am

install-info-am:

install-man:

install-pdf: install-pdf-am

install-pdf-am:

install-ps: install-ps-am

install-ps-am:

installcheck-am:

maintainer-clean: maintainer-clean-am
		-rm -f ./$(DEPDIR)/batch_memory.Po
	-rm -f ./$(DEPDIR)/lsb.comm.Po
	-rm -f ./$(DEPDIR)/lsb.conf.Po
	-rm -f ./$(DEPDIR)/lsb.debug.Po
	-rm -f ./$(DEPDIR)/lsb.err.Po
	-rm -f ./$(DEPDIR)/lsb.groups.Po
	-rm -f ./$(DEPDIR)/lsb.hc.Po
	-rm -f ./$(DEPDIR)/lsb.hosts.Po
	-rm -f ./$(DEPDIR)/lsb.init.Po
	-rm -f ./$(DEPDIR)/lsb.jobs.Po
	-rm -f ./$(DEPDIR)/lsb.log.Po
	-rm -f ./$(DEPDIR)/lsb.mig.Po
	-rm -f ./$(DEPDIR)/lsb.misc.Po
	-rm -f ./$(DEPDIR)/lsb.modify.Po
	-rm -f ./$(DEPDIR)/lsb.move.Po
	-rm -f ./$(DEPDIR)/lsb.msg.Po
	-rm -f ./$(DEPDIR)/lsb.params.Po
	-rm -f ./$(DEPDIR)/lsb.peek.Po
	-rm -f ./$(DEPDIR)/lsb.qc.Po
	-rm -f ./$(DEPDIR)/lsb.queues.Po
	-rm -f ./$(DEPDIR)/lsb.reason.Po
	-rm -f ./$(DEPDIR)/lsb.reconfig.Po
	-rm -f ./$(DEPDIR)/lsb.resource.Po
	-rm -f ./$(DEPDIR)/lsb.rexecv.Po
	-rm -f ./$(DEPDIR)/lsb.sig.Po
	-rm -f ./$(DEPDIR)/lsb.signals.Po
	-rm -f ./$(DEPDIR)/lsb.spool.Po
	-rm -f ./$(DEPDIR)/lsb.sub.Po
	-rm -f ./$(DEPDIR)/lsb.switch.Po
	-rm -f ./$(DEPDIR)/lsb.users.Po
	-rm -f ./$(DEPDIR)/lsb.xdr.Po
	-rm -f Makefile
maintainer-clean-am: distclean-am maintainer-clean-generic

mostlyclean: mostlyclean-am

mostlyclean-am: mostlyclean-compile mostlyclean-generic

pdf: pdf-am

pdf-am:

ps: ps-am

ps-am:

uninstall-am: uninstall-libLIBRARIES

.MAKE: install-am install-strip

.PHONY: CTAGS GTAGS TAGS all all-am am--depfiles check check-am clean \
	clean-generic clean-libLIBRARIES cscopelist-am ctags ctags-am \
	distclean distclean-compile distclean-generic distclean-tags \
	distdir dvi dvi-am html html-am info info-am install \
	install-am install-data install-data-am install-dvi \
	install-dvi-am install-exec install-exec-am install-html \
	install-html-am install-info install-info-am \
	install-libLIBRARIES install-man install-pdf install-pdf-am \
	install-ps install-ps-am install-strip installcheck \
	installcheck-am installdirs maintainer-clean \
	maintainer-clean-generic mostlyclean mostlyclean-compile \
	mostlyclean-generic pdf pdf-am ps ps-am tags tags-am uninstall \
	uninstall-am uninstall-libLIBRARIES

.PRECIOUS: Makefile


etags :
	etags *.[hc] ../*.h ../lib/*.[hc] ../../lsf/*.h ../../lsf/intlib/*.[hc]

# Tell versions [3.59,3.63) of GNU make to not export all variables.
# Otherwise a system limit (for SysV at least) may be exceeded.
.NOEXPORT:
