/*
 * Copyright (C) 2021-2025 Bytedance Ltd. and/or its affiliates
 * $Id: mbd.profcnt.def,v 1.1 2007/07/18 23:17:39 cchen Exp $
 ***************************************************************************
 *
 * Load Sharing Facility
 * 
 * Master Batch Daemon internal Profiling Counters
 *
 * These counters monitor of a number of computational intensive
 * routines by keeping track of the actual iterations attributable
 * to certain loops with potentially large iteration space.
 *
 **************************************************************************
 *
 */

MBD_PROF_COUNTER(getLsbUsable)
MBD_PROF_COUNTER(checkSlotReserve)
MBD_PROF_COUNTER(getQUsable)
MBD_PROF_COUNTER(readyToDisp)
MBD_PROF_COUNTER(userJobLimitOk)
MBD_PROF_COUNTER(cntUserJobs)
MBD_PROF_COUNTER(loopcntUserJobs)
MBD_PROF_COUNTER(innerLoopcntUserJobs)
MBD_PROF_COUNTER(getCandHosts)
MBD_PROF_COUNTER(getPeerCand)
MBD_PROF_COUNTER(getPeerCandFound)
MBD_PROF_COUNTER(getPeerCandNoFound)
MBD_PROF_COUNTER(getPeerCandQuick)
MBD_PROF_COUNTER(getJUsable)
MBD_PROF_COUNTER(firstLoopgetJUsable)
MBD_PROF_COUNTER(secondLoopGetJUsable)
MBD_PROF_COUNTER(getHostsByResReq)
MBD_PROF_COUNTER(loopgetHostsByResReq)
MBD_PROF_COUNTER(thirdLoopgetJUsable)
MBD_PROF_COUNTER(innerLoopgetJUsable)
MBD_PROF_COUNTER(hpartHmember)
MBD_PROF_COUNTER(loophpartHMember)
MBD_PROF_COUNTER(hostJobLimitOk)
MBD_PROF_COUNTER(hostJobLimitOk1)
MBD_PROF_COUNTER(hostSlots)
MBD_PROF_COUNTER(cntHostJobs)
MBD_PROF_COUNTER(loopcntHostJobs)
MBD_PROF_COUNTER(innerLoopcntHostJobs)
MBD_PROF_COUNTER(uJobLimitOk)
MBD_PROF_COUNTER(loopuJobLimitOk)
MBD_PROF_COUNTER(pJobLimitOk)
MBD_PROF_COUNTER(looppJobLimitOk)
MBD_PROF_COUNTER(pJobLimitUser)
MBD_PROF_COUNTER(looppJobLimitUser)
MBD_PROF_COUNTER(hJobLimitOk)
MBD_PROF_COUNTER(loophJobLimitOk)
MBD_PROF_COUNTER(ckResReserve)
MBD_PROF_COUNTER(loopckResReserve)
MBD_PROF_COUNTER(findBestHost)
MBD_PROF_COUNTER(hostPreference)
MBD_PROF_COUNTER(gMember)
MBD_PROF_COUNTER(loopdispatchAJob)
MBD_PROF_COUNTER(firstlooporderCandHosts)
MBD_PROF_COUNTER(secondlooporderCandHosts)
MBD_PROF_COUNTER(firstloopallocHosts)
MBD_PROF_COUNTER(secondloopallocHosts)
MBD_PROF_COUNTER(updQaccount)
MBD_PROF_COUNTER(loopupdUAcct)
MBD_PROF_COUNTER(loopupdHAcct)
MBD_PROF_COUNTER(loopupdUserData)
MBD_PROF_COUNTER(loopupdHostData)
MBD_PROF_COUNTER(loopdeallocHosts)
MBD_PROF_COUNTER(firstloopadjLsbLoad)
MBD_PROF_COUNTER(secondloopadjLsbLoad)
MBD_PROF_COUNTER(firstlooporderByPreempt)
MBD_PROF_COUNTER(secondlooporderByPreempt)
MBD_PROF_COUNTER(thirdlooporderByPreempt)
MBD_PROF_COUNTER(forthlooporderByPreempt)
MBD_PROF_COUNTER(fifthlooporderByPreempt)
MBD_PROF_COUNTER(getLsfHostData)
MBD_PROF_COUNTER(getHostByType)
MBD_PROF_COUNTER(ckSchedHost)
MBD_PROF_COUNTER(getUAcct)
MBD_PROF_COUNTER(getHostJobSlots)
MBD_PROF_COUNTER(getHostJobSlots1)
MBD_PROF_COUNTER(ckPerHULimits)
MBD_PROF_COUNTER(cntNumPrmptSlots)
MBD_PROF_COUNTER(loopcntNumPrmptSlots)
MBD_PROF_COUNTER(secloopcntNumPrmptSlots)
MBD_PROF_COUNTER(shouldPreemptJob1)
MBD_PROF_COUNTER(candHostOk)
MBD_PROF_COUNTER(looppickAJob)
MBD_PROF_COUNTER(loopjobIsOnFSQ)
MBD_PROF_COUNTER(cntUQSlots)
MBD_PROF_COUNTER(shareProviderPolicyElectJob)
MBD_PROF_COUNTER(iterator_shareProviderPolicyElectJob)
MBD_PROF_COUNTER(userElectFSPendJob)
MBD_PROF_COUNTER(userOthersBrokerNoPENDJob)
MBD_PROF_COUNTER(userNoPENDJob)
MBD_PROF_COUNTER(userNoPENDJobInProvider)
MBD_PROF_COUNTER(leafUser_userElectFSPendJob)
MBD_PROF_COUNTER(iterator_onJData_userElectFSPendJob)
MBD_PROF_COUNTER(iterator_ugrp_child_user)
MBD_PROF_COUNTER(iterator_onsAcct_userElectFSpendJob)
MBD_PROF_COUNTER(numSkippedStartedZero)
MBD_PROF_COUNTER(numSkippedStarted)
MBD_PROF_COUNTER(numSkippedSharedAccts)
MBD_PROF_COUNTER(numEndOfQueue)
MBD_PROF_COUNTER(numNoPendInQueue)
MBD_PROF_COUNTER(numPeekedSharedAccts)
MBD_PROF_COUNTER(numPeekedZeroStarted)
MBD_PROF_COUNTER(numPeekedSomeStarted)
MBD_PROF_COUNTER(numPendInQueue)
MBD_PROF_COUNTER(numStartedJobsPerSession)
MBD_PROF_COUNTER(numReadyJobsPerSession)
MBD_PROF_COUNTER(numJobsWithCandHostsPerSession)
MBD_PROF_COUNTER(numFinishedJobs)
MBD_PROF_COUNTER(numStartedJobs)
MBD_PROF_COUNTER(collectJobPendReason)
MBD_PROF_COUNTER(checkJobPendReason)
MBD_PROF_COUNTER(copyJobPendReason)
MBD_PROF_COUNTER(firstLoopresigJobs)
MBD_PROF_COUNTER(jobLoopresigJobs)
MBD_PROF_COUNTER(hostLoopresigJobs)
MBD_PROF_COUNTER(nqsLoopresigJobs)
MBD_PROF_COUNTER(fourthLoopresigJobs)
MBD_PROF_COUNTER(resigJobs1)
#ifdef HPART_ENHANCEMENT
MBD_PROF_COUNTER(numNonSchedulableJobsInHpart)
MBD_PROF_COUNTER(numSchedulableJobsInHpart)
MBD_PROF_COUNTER(numCandHostsBelongToHpart)
MBD_PROF_COUNTER(numCandHostNotInHpart)
#endif
MBD_PROF_COUNTER(sch_fs_electJob)
