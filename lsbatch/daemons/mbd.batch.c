/**
 * @file mbd.batch.c
 * @brief MBD批量作业处理模块
 * 
 * 主要功能：
 * 1. 批量作业请求接收和解析
 * 2. 批量作业调度和队列管理
 * 3. 流式响应和状态推送
 * 4. 批量作业状态跟踪
 * 5. 错误处理和恢复机制
 */

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <time.h>
#include <errno.h>

#include "mbd.h"
#include "../lsbatch.h"
#include "../lib/lsb.xdr.h"

/* 批量作业管理结构 */
struct batchJobManager {
    int batchId;                        /* 批量ID */
    int totalJobs;                      /* 总作业数 */
    int submittedJobs;                  /* 已提交作业数 */
    int completedJobs;                  /* 已完成作业数 */
    int failedJobs;                     /* 失败作业数 */
    time_t startTime;                   /* 开始时间 */
    time_t lastUpdateTime;              /* 最后更新时间 */
    int clientChannelFd;                /* 客户端channel文件描述符 */
    int enableStreaming;                /* 是否启用流式响应 */
    struct jData **jobArray;            /* 作业数组 */
    struct batchJobResult *results;     /* 结果数组 */
    struct batchJobManager *next;       /* 下一个批量管理器 */
};

/* 全局批量管理器链表 */
static struct batchJobManager *batchManagerList = NULL;
static int nextBatchId = 1;

/* 内部函数声明 */
static struct batchJobManager* create_batch_manager(int totalJobs, int clientFd, int enableStreaming);
static void destroy_batch_manager(struct batchJobManager *manager);
static struct batchJobManager* find_batch_manager(int batchId);
static int process_batch_job_submission(struct batchJobManager *manager,
                                       struct submitReq *jobs, int jobCount);
static int send_stream_job_result(struct batchJobManager *manager, int jobIndex,
                                 struct jData *jobData, int resultCode);
static int send_batch_complete_signal(struct batchJobManager *manager);
static int send_stream_header(int chfd, struct streamHeader *header);
static int send_batch_reply(int chfd, struct batchSubmitReply *reply, int errorCode);
static int send_batch_status_reply(int chfd, struct batchStatusReply *reply, int errorCode);

/**
 * @brief 处理批量作业提交请求
 * 这是MBD中处理BATCH_SUBMIT_REQ消息的主要函数
 */
int do_batchSubmitReq(XDR *xdrs,
                      int chfd,
                      struct sockaddr_in *from,
                      char *hostName,
                      struct LSFHeader *reqHdr,
                      struct sockaddr_in *laddr,
                      struct lsfAuth *auth,
                      int *schedule,
                      int dispatch)
{
    static char fname[] = "do_batchSubmitReq";
    struct batchSubmitReq batchReq;
    struct batchSubmitReply batchReply;
    struct batchJobManager *manager = NULL;
    int reply = LSBE_NO_ERROR;
    int i;

    if (logclass & (LC_TRACE | LC_EXEC | LC_COMM)) {
        ls_syslog(LOG_DEBUG, "%s: 接收批量作业提交请求，host=%s, socket=%d", 
                  fname, hostName, chanSock_(chfd));
    }

    /* 初始化结构 */
    memset(&batchReq, 0, sizeof(batchReq));
    memset(&batchReply, 0, sizeof(batchReply));

    /* 解码批量请求 */
    if (!xdr_batchSubmitReq(xdrs, &batchReq, reqHdr)) {
        reply = LSBE_XDR;
        ls_syslog(LOG_ERR, "%s: xdr_batchSubmitReq 失败", fname);
        goto sendback;
    }

    /* 验证批量请求 */
    if (batchReq.jobCount <= 0 || batchReq.jobCount > BATCH_MAX_JOBS_DEFAULT) {
        reply = LSBE_BAD_ARG;
        ls_syslog(LOG_ERR, "%s: 无效的作业数量: %d", fname, batchReq.jobCount);
        goto sendback;
    }

    if (!batchReq.jobs) {
        reply = LSBE_BAD_ARG;
        ls_syslog(LOG_ERR, "%s: 作业数组为空", fname);
        goto sendback;
    }

    /* 创建批量管理器 */
    int enableStreaming = (batchReq.options & BATCH_SUBMIT_STREAM_RESPONSE) ? 1 : 0;
    manager = create_batch_manager(batchReq.jobCount, chfd, enableStreaming);
    if (!manager) {
        reply = LSBE_NO_MEM;
        ls_syslog(LOG_ERR, "%s: 创建批量管理器失败", fname);
        goto sendback;
    }

    ls_syslog(LOG_INFO, "%s: 创建批量管理器，batchId=%d, 作业数=%d, 流式=%s", 
              fname, manager->batchId, batchReq.jobCount, 
              enableStreaming ? "启用" : "禁用");

    /* 设置批量响应基本信息 */
    batchReply.batchId = manager->batchId;
    batchReply.startTime = manager->startTime;
    batchReply.successCount = 0;
    batchReply.failureCount = 0;

    /* 如果启用流式响应，立即发送初始响应 */
    if (enableStreaming) {
        /* 发送流式消息头 */
        struct streamHeader header;
        header.messageType = STREAM_MSG_JOB_RESULT;
        header.sequenceId = 0;
        header.batchId = manager->batchId;
        header.flags = STREAM_FLAG_MORE_MESSAGES;

        if (send_stream_header(chfd, &header) != 0) {
            ls_syslog(LOG_ERR, "%s: 发送流式消息头失败", fname);
            reply = LSBE_PROTOCOL;
            goto sendback;
        }

        /* 开始异步处理作业提交 */
        if (process_batch_job_submission(manager, batchReq.jobs, batchReq.jobCount) != 0) {
            reply = LSBE_SYSTEM;
            ls_syslog(LOG_ERR, "%s: 批量作业提交处理失败", fname);
            goto sendback;
        }

        /* 流式响应模式下，不需要立即发送完整响应 */
        return 0;
    } else {
        /* 非流式模式：同步处理所有作业 */
        if (process_batch_job_submission(manager, batchReq.jobs, batchReq.jobCount) != 0) {
            reply = LSBE_SYSTEM;
            ls_syslog(LOG_ERR, "%s: 批量作业提交处理失败", fname);
            goto sendback;
        }

        /* 等待所有作业处理完成 */
        while (manager->completedJobs + manager->failedJobs < manager->totalJobs) {
            /* 简单的轮询等待，实际实现中应该使用更高效的机制 */
            usleep(10000); /* 10ms */
        }

        /* 设置最终结果 */
        batchReply.successCount = manager->completedJobs;
        batchReply.failureCount = manager->failedJobs;
        batchReply.completionTime = time(NULL);

        /* 复制结果数组 */
        if (manager->results) {
            batchReply.results = malloc(manager->totalJobs * sizeof(struct batchJobResult));
            if (batchReply.results) {
                memcpy(batchReply.results, manager->results, 
                       manager->totalJobs * sizeof(struct batchJobResult));
            }
        }
    }

sendback:
    /* 发送响应 */
    if (send_batch_reply(chfd, &batchReply, reply) < 0) {
        ls_syslog(LOG_ERR, "%s: 发送批量响应失败", fname);
        if (manager) {
            destroy_batch_manager(manager);
        }
        return -1;
    }

    /* 清理资源 */
    if (!enableStreaming && manager) {
        destroy_batch_manager(manager);
    }

    /* 清理XDR数据 */
    XDR freeXdr;
    xdrmem_create(&freeXdr, NULL, 0, XDR_FREE);
    xdr_batchSubmitReq(&freeXdr, &batchReq, reqHdr);
    xdr_destroy(&freeXdr);

    if (batchReply.results) {
        free(batchReply.results);
    }

    ls_syslog(LOG_DEBUG, "%s: 批量作业提交请求处理完成，reply=%d", fname, reply);
    return 0;
}

/**
 * @brief 创建批量管理器
 */
static struct batchJobManager* create_batch_manager(int totalJobs, int clientFd, int enableStreaming)
{
    struct batchJobManager *manager = malloc(sizeof(struct batchJobManager));
    if (!manager) {
        return NULL;
    }

    memset(manager, 0, sizeof(struct batchJobManager));

    manager->batchId = nextBatchId++;
    manager->totalJobs = totalJobs;
    manager->clientChannelFd = clientFd;
    manager->enableStreaming = enableStreaming;
    manager->startTime = time(NULL);
    manager->lastUpdateTime = manager->startTime;

    /* 分配作业和结果数组 */
    manager->jobArray = calloc(totalJobs, sizeof(struct jData*));
    manager->results = calloc(totalJobs, sizeof(struct batchJobResult));

    if (!manager->jobArray || !manager->results) {
        if (manager->jobArray) free(manager->jobArray);
        if (manager->results) free(manager->results);
        free(manager);
        return NULL;
    }

    /* 添加到全局链表 */
    manager->next = batchManagerList;
    batchManagerList = manager;

    return manager;
}

/**
 * @brief 销毁批量管理器
 */
static void destroy_batch_manager(struct batchJobManager *manager)
{
    if (!manager) {
        return;
    }

    /* 从全局链表中移除 */
    struct batchJobManager **current = &batchManagerList;
    while (*current) {
        if (*current == manager) {
            *current = manager->next;
            break;
        }
        current = &(*current)->next;
    }

    /* 清理资源 */
    if (manager->jobArray) {
        free(manager->jobArray);
    }
    if (manager->results) {
        int i;
        for (i = 0; i < manager->totalJobs; i++) {
            if (manager->results[i].queue) {
                free(manager->results[i].queue);
            }
            if (manager->results[i].errorMsg) {
                free(manager->results[i].errorMsg);
            }
        }
        free(manager->results);
    }

    free(manager);
}

/**
 * @brief 查找批量管理器
 */
static struct batchJobManager* find_batch_manager(int batchId)
{
    struct batchJobManager *current = batchManagerList;
    while (current) {
        if (current->batchId == batchId) {
            return current;
        }
        current = current->next;
    }
    return NULL;
}

/**
 * @brief 处理批量作业提交
 */
static int process_batch_job_submission(struct batchJobManager *manager,
                                       struct submitReq *jobs, int jobCount)
{
    static char fname[] = "process_batch_job_submission";
    int i;
    int successCount = 0;
    int failureCount = 0;

    if (!manager || !jobs || jobCount <= 0) {
        return -1;
    }

    ls_syslog(LOG_DEBUG, "%s: 开始处理 %d 个批量作业", fname, jobCount);

    /* 逐个提交作业 */
    for (i = 0; i < jobCount; i++) {
        struct submitMbdReply submitReply;
        struct jData *jobData = NULL;
        int schedule = 0;
        int reply;

        /* 初始化提交响应 */
        memset(&submitReply, 0, sizeof(submitReply));

        /* 调用现有的作业提交逻辑 */
        reply = newJob(&jobs[i], &submitReply, manager->clientChannelFd,
                      NULL, &schedule, 0, &jobData);

        /* 记录结果 */
        manager->results[i].jobIndex = i;
        manager->results[i].submitTime = time(NULL);
        manager->results[i].processTime = time(NULL);

        if (reply == LSBE_NO_ERROR && jobData) {
            /* 提交成功 */
            manager->results[i].jobId = submitReply.jobId;
            manager->results[i].resultCode = 0;
            manager->results[i].queue = strdup(submitReply.queue ? submitReply.queue : "default");
            manager->results[i].errorMsg = NULL;

            manager->jobArray[i] = jobData;
            manager->submittedJobs++;
            successCount++;

            ls_syslog(LOG_DEBUG, "%s: 作业 %d 提交成功，jobId=%s",
                      fname, i, lsb_jobid2str(submitReply.jobId));

            /* 如果启用流式响应，立即发送结果 */
            if (manager->enableStreaming) {
                send_stream_job_result(manager, i, jobData, LSBE_NO_ERROR);
            }
        } else {
            /* 提交失败 */
            manager->results[i].jobId = 0;
            manager->results[i].resultCode = reply;
            manager->results[i].queue = NULL;
            manager->results[i].errorMsg = strdup(lsb_sysmsg());

            manager->failedJobs++;
            failureCount++;

            ls_syslog(LOG_ERR, "%s: 作业 %d 提交失败，错误=%d, 消息=%s",
                      fname, i, reply, lsb_sysmsg());

            /* 如果启用流式响应，发送错误结果 */
            if (manager->enableStreaming) {
                send_stream_job_result(manager, i, NULL, reply);
            }
        }

        manager->lastUpdateTime = time(NULL);
    }

    ls_syslog(LOG_INFO, "%s: 批量作业提交完成，成功=%d, 失败=%d",
              fname, successCount, failureCount);

    /* 如果启用流式响应，发送完成信号 */
    if (manager->enableStreaming) {
        send_batch_complete_signal(manager);
    }

    return 0;
}

/**
 * @brief 发送流式作业结果
 */
static int send_stream_job_result(struct batchJobManager *manager, int jobIndex,
                                 struct jData *jobData, int resultCode)
{
    static char fname[] = "send_stream_job_result";
    struct streamJobResult result;
    XDR xdrs;
    char *buffer = NULL;
    int bufferSize = 1024;
    struct LSFHeader header;

    if (!manager) {
        return -1;
    }

    /* 准备流式结果 */
    memset(&result, 0, sizeof(result));
    result.batchId = manager->batchId;
    result.jobIndex = jobIndex;
    result.resultCode = resultCode;
    result.submitTime = time(NULL);
    result.processTime = time(NULL);

    if (resultCode == LSBE_NO_ERROR && jobData) {
        result.jobId = jobData->jobId;
    } else {
        result.jobId = 0;
    }

    /* 序列化结果 */
    buffer = malloc(bufferSize);
    if (!buffer) {
        ls_syslog(LOG_ERR, "%s: 内存分配失败", fname);
        return -1;
    }

    xdrmem_create(&xdrs, buffer, bufferSize, XDR_ENCODE);
    if (!xdr_streamJobResult(&xdrs, &result, NULL)) {
        ls_syslog(LOG_ERR, "%s: XDR编码失败", fname);
        xdr_destroy(&xdrs);
        free(buffer);
        return -1;
    }

    int actualSize = xdr_getpos(&xdrs);
    xdr_destroy(&xdrs);

    /* 准备LSF消息头 */
    header.opCode = STREAM_JOB_RESULT;
    header.length = actualSize;
    header.version = VOLCLAVA_VERSION;

    /* 发送消息 */
    if (write(manager->clientChannelFd, &header, sizeof(header)) != sizeof(header) ||
        write(manager->clientChannelFd, buffer, actualSize) != actualSize) {
        ls_syslog(LOG_ERR, "%s: 发送流式结果失败: %m", fname);
        free(buffer);
        return -1;
    }

    free(buffer);

    ls_syslog(LOG_DEBUG, "%s: 发送流式结果，jobIndex=%d, resultCode=%d",
              fname, jobIndex, resultCode);

    return 0;
}

/**
 * @brief 发送批量完成信号
 */
static int send_batch_complete_signal(struct batchJobManager *manager)
{
    static char fname[] = "send_batch_complete_signal";
    struct batchCompleteSignal signal;
    XDR xdrs;
    char *buffer = NULL;
    int bufferSize = 512;
    struct LSFHeader header;

    if (!manager) {
        return -1;
    }

    /* 准备完成信号 */
    memset(&signal, 0, sizeof(signal));
    signal.batchId = manager->batchId;
    signal.totalJobs = manager->totalJobs;
    signal.successCount = manager->submittedJobs;
    signal.failureCount = manager->failedJobs;
    signal.completionTime = time(NULL);

    /* 序列化信号 */
    buffer = malloc(bufferSize);
    if (!buffer) {
        ls_syslog(LOG_ERR, "%s: 内存分配失败", fname);
        return -1;
    }

    xdrmem_create(&xdrs, buffer, bufferSize, XDR_ENCODE);
    if (!xdr_batchCompleteSignal(&xdrs, &signal, NULL)) {
        ls_syslog(LOG_ERR, "%s: XDR编码失败", fname);
        xdr_destroy(&xdrs);
        free(buffer);
        return -1;
    }

    int actualSize = xdr_getpos(&xdrs);
    xdr_destroy(&xdrs);

    /* 准备LSF消息头 */
    header.opCode = BATCH_COMPLETE_SIGNAL;
    header.length = actualSize;
    header.version = VOLCLAVA_VERSION;

    /* 发送消息 */
    if (write(manager->clientChannelFd, &header, sizeof(header)) != sizeof(header) ||
        write(manager->clientChannelFd, buffer, actualSize) != actualSize) {
        ls_syslog(LOG_ERR, "%s: 发送完成信号失败: %m", fname);
        free(buffer);
        return -1;
    }

    free(buffer);

    ls_syslog(LOG_INFO, "%s: 发送批量完成信号，batchId=%d, 成功=%d, 失败=%d",
              fname, manager->batchId, signal.successCount, signal.failureCount);

    return 0;
}

/**
 * @brief 发送流式消息头
 */
static int send_stream_header(int chfd, struct streamHeader *header)
{
    static char fname[] = "send_stream_header";
    XDR xdrs;
    char buffer[256];
    struct LSFHeader lsfHeader;

    if (!header) {
        return -1;
    }

    /* 序列化流式头 */
    xdrmem_create(&xdrs, buffer, sizeof(buffer), XDR_ENCODE);
    if (!xdr_streamHeader(&xdrs, header, NULL)) {
        ls_syslog(LOG_ERR, "%s: XDR编码失败", fname);
        xdr_destroy(&xdrs);
        return -1;
    }

    int actualSize = xdr_getpos(&xdrs);
    xdr_destroy(&xdrs);

    /* 准备LSF消息头 */
    lsfHeader.opCode = STREAM_HEADER;
    lsfHeader.length = actualSize;
    lsfHeader.version = VOLCLAVA_VERSION;

    /* 发送消息 */
    if (write(chfd, &lsfHeader, sizeof(lsfHeader)) != sizeof(lsfHeader) ||
        write(chfd, buffer, actualSize) != actualSize) {
        ls_syslog(LOG_ERR, "%s: 发送流式头失败: %m", fname);
        return -1;
    }

    return 0;
}

/**
 * @brief 发送批量响应
 */
static int send_batch_reply(int chfd, struct batchSubmitReply *reply, int errorCode)
{
    static char fname[] = "send_batch_reply";
    XDR xdrs;
    char *buffer = NULL;
    int bufferSize = 4096;
    struct LSFHeader header;
    int result = 0;

    if (!reply) {
        return -1;
    }

    /* 分配缓冲区 */
    buffer = malloc(bufferSize);
    if (!buffer) {
        ls_syslog(LOG_ERR, "%s: 内存分配失败", fname);
        return -1;
    }

    /* 序列化响应 */
    xdrmem_create(&xdrs, buffer, bufferSize, XDR_ENCODE);
    if (!xdr_batchSubmitReply(&xdrs, reply, NULL)) {
        ls_syslog(LOG_ERR, "%s: XDR编码失败", fname);
        xdr_destroy(&xdrs);
        free(buffer);
        return -1;
    }

    int actualSize = xdr_getpos(&xdrs);
    xdr_destroy(&xdrs);

    /* 准备LSF消息头 */
    header.opCode = BATCH_SUBMIT_REPLY;
    header.length = actualSize;
    header.version = VOLCLAVA_VERSION;
    header.reserved = errorCode;

    /* 发送消息 */
    if (write(chfd, &header, sizeof(header)) != sizeof(header) ||
        write(chfd, buffer, actualSize) != actualSize) {
        ls_syslog(LOG_ERR, "%s: 发送批量响应失败: %m", fname);
        result = -1;
    }

    free(buffer);
    return result;
}

/**
 * @brief 批量管理器清理函数
 * 定期清理已完成的批量管理器
 */
void batch_manager_cleanup(void)
{
    static char fname[] = "batch_manager_cleanup";
    static time_t lastCleanupTime = 0;
    time_t now = time(NULL);

    /* 每5分钟清理一次 */
    if (now - lastCleanupTime < 300) {
        return;
    }

    lastCleanupTime = now;

    struct batchJobManager **current = &batchManagerList;
    int cleanedCount = 0;

    while (*current) {
        struct batchJobManager *manager = *current;

        /* 清理条件：
         * 1. 非流式模式且已完成超过1小时
         * 2. 流式模式且已完成超过10分钟
         * 3. 所有作业都已处理完成
         */
        int isComplete = (manager->completedJobs + manager->failedJobs >= manager->totalJobs);
        int timeThreshold = manager->enableStreaming ? 600 : 3600; /* 10分钟或1小时 */

        if (isComplete && (now - manager->lastUpdateTime > timeThreshold)) {
            *current = manager->next;

            ls_syslog(LOG_DEBUG, "%s: 清理批量管理器 batchId=%d", fname, manager->batchId);

            /* 清理资源 */
            if (manager->jobArray) {
                free(manager->jobArray);
            }
            if (manager->results) {
                int i;
                for (i = 0; i < manager->totalJobs; i++) {
                    if (manager->results[i].queue) {
                        free(manager->results[i].queue);
                    }
                    if (manager->results[i].errorMsg) {
                        free(manager->results[i].errorMsg);
                    }
                }
                free(manager->results);
            }

            free(manager);
            cleanedCount++;
        } else {
            current = &manager->next;
        }
    }

    if (cleanedCount > 0) {
        ls_syslog(LOG_INFO, "%s: 清理了 %d 个批量管理器", fname, cleanedCount);
    }
}

/**
 * @brief 获取批量管理器统计信息
 */
void batch_manager_statistics(int *totalBatches, int *activeBatches, int *totalJobs)
{
    struct batchJobManager *current = batchManagerList;
    int batches = 0;
    int active = 0;
    int jobs = 0;

    while (current) {
        batches++;
        jobs += current->totalJobs;

        if (current->completedJobs + current->failedJobs < current->totalJobs) {
            active++;
        }

        current = current->next;
    }

    if (totalBatches) *totalBatches = batches;
    if (activeBatches) *activeBatches = active;
    if (totalJobs) *totalJobs = jobs;
}

/**
 * @brief 处理批量状态查询请求
 */
int do_batchStatusReq(XDR *xdrs,
                     int chfd,
                     struct sockaddr_in *from,
                     char *hostName,
                     struct LSFHeader *reqHdr,
                     struct lsfAuth *auth)
{
    static char fname[] = "do_batchStatusReq";
    struct batchStatusReq statusReq;
    struct batchStatusReply statusReply;
    struct batchJobManager *manager = NULL;
    int reply = LSBE_NO_ERROR;

    if (logclass & (LC_TRACE | LC_EXEC | LC_COMM)) {
        ls_syslog(LOG_DEBUG, "%s: 接收批量状态查询请求，host=%s, socket=%d",
                  fname, hostName, chanSock_(chfd));
    }

    /* 初始化结构 */
    memset(&statusReq, 0, sizeof(statusReq));
    memset(&statusReply, 0, sizeof(statusReply));

    /* 解码状态查询请求 */
    if (!xdr_batchStatusReq(xdrs, &statusReq, reqHdr)) {
        reply = LSBE_XDR;
        ls_syslog(LOG_ERR, "%s: xdr_batchStatusReq 失败", fname);
        goto sendback;
    }

    /* 查找批量管理器 */
    manager = find_batch_manager(statusReq.batchId);
    if (!manager) {
        reply = LSBE_BATCH_NOT_FOUND;
        ls_syslog(LOG_ERR, "%s: 批量管理器不存在，batchId=%d", fname, statusReq.batchId);
        goto sendback;
    }

    /* 填充状态响应 */
    statusReply.batchId = manager->batchId;
    statusReply.totalJobs = manager->totalJobs;
    statusReply.submittedJobs = manager->submittedJobs;
    statusReply.completedJobs = manager->completedJobs;
    statusReply.failedJobs = manager->failedJobs;
    statusReply.startTime = manager->startTime;
    statusReply.lastUpdateTime = manager->lastUpdateTime;

    /* 计算运行中和等待中的作业数 */
    statusReply.runningJobs = 0;
    statusReply.pendingJobs = 0;

    if (manager->jobArray) {
        int i;
        for (i = 0; i < manager->totalJobs; i++) {
            if (manager->jobArray[i]) {
                if (IS_RUN(manager->jobArray[i]->jStatus)) {
                    statusReply.runningJobs++;
                } else if (IS_PEND(manager->jobArray[i]->jStatus)) {
                    statusReply.pendingJobs++;
                }
            }
        }
    }

    /* 如果请求详细结果，复制结果数组 */
    if ((statusReq.options & BATCH_STATUS_DETAILED) && manager->results) {
        statusReply.results = malloc(manager->totalJobs * sizeof(struct batchJobResult));
        if (statusReply.results) {
            memcpy(statusReply.results, manager->results,
                   manager->totalJobs * sizeof(struct batchJobResult));
        }
    }

    ls_syslog(LOG_DEBUG, "%s: 批量状态查询，batchId=%d, 总数=%d, 已提交=%d, 已完成=%d, 失败=%d",
              fname, statusReply.batchId, statusReply.totalJobs,
              statusReply.submittedJobs, statusReply.completedJobs, statusReply.failedJobs);

sendback:
    /* 发送响应 */
    if (send_batch_status_reply(chfd, &statusReply, reply) < 0) {
        ls_syslog(LOG_ERR, "%s: 发送批量状态响应失败", fname);
        if (statusReply.results) {
            free(statusReply.results);
        }
        return -1;
    }

    /* 清理资源 */
    if (statusReply.results) {
        free(statusReply.results);
    }

    /* 清理XDR数据 */
    XDR freeXdr;
    xdrmem_create(&freeXdr, NULL, 0, XDR_FREE);
    xdr_batchStatusReq(&freeXdr, &statusReq, reqHdr);
    xdr_destroy(&freeXdr);

    ls_syslog(LOG_DEBUG, "%s: 批量状态查询请求处理完成，reply=%d", fname, reply);
    return 0;
}

/**
 * @brief 发送批量状态响应
 */
static int send_batch_status_reply(int chfd, struct batchStatusReply *reply, int errorCode)
{
    static char fname[] = "send_batch_status_reply";
    XDR xdrs;
    char *buffer = NULL;
    int bufferSize = 4096;
    struct LSFHeader header;
    int result = 0;

    if (!reply) {
        return -1;
    }

    /* 分配缓冲区 */
    buffer = malloc(bufferSize);
    if (!buffer) {
        ls_syslog(LOG_ERR, "%s: 内存分配失败", fname);
        return -1;
    }

    /* 序列化响应 */
    xdrmem_create(&xdrs, buffer, bufferSize, XDR_ENCODE);
    if (!xdr_batchStatusReply(&xdrs, reply, NULL)) {
        ls_syslog(LOG_ERR, "%s: XDR编码失败", fname);
        xdr_destroy(&xdrs);
        free(buffer);
        return -1;
    }

    int actualSize = xdr_getpos(&xdrs);
    xdr_destroy(&xdrs);

    /* 准备LSF消息头 */
    header.opCode = BATCH_STATUS_REPLY;
    header.length = actualSize;
    header.version = VOLCLAVA_VERSION;
    header.reserved = errorCode;

    /* 发送消息 */
    if (write(chfd, &header, sizeof(header)) != sizeof(header) ||
        write(chfd, buffer, actualSize) != actualSize) {
        ls_syslog(LOG_ERR, "%s: 发送批量状态响应失败: %m", fname);
        result = -1;
    }

    free(buffer);
    return result;
}
