# Makefile.in generated by automake 1.16.1 from Makefile.am.
# lsbatch/daemons/Makefile.  Generated from Makefile.in by configure.

# Copyright (C) 1994-2018 Free Software Foundation, Inc.

# This Makefile.in is free software; the Free Software Foundation
# gives unlimited permission to copy and/or distribute it,
# with or without modifications, as long as this notice is preserved.

# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY, to the extent permitted by law; without
# even the implied warranty of MERCHANTABILITY or FITNESS FOR A
# PARTICULAR PURPOSE.




am__is_gnu_make = { \
  if test -z '$(MAKELEVEL)'; then \
    false; \
  elif test -n '$(MAKE_HOST)'; then \
    true; \
  elif test -n '$(MAKE_VERSION)' && test -n '$(CURDIR)'; then \
    true; \
  else \
    false; \
  fi; \
}
am__make_running_with_option = \
  case $${target_option-} in \
      ?) ;; \
      *) echo "am__make_running_with_option: internal error: invalid" \
              "target option '$${target_option-}' specified" >&2; \
         exit 1;; \
  esac; \
  has_opt=no; \
  sane_makeflags=$$MAKEFLAGS; \
  if $(am__is_gnu_make); then \
    sane_makeflags=$$MFLAGS; \
  else \
    case $$MAKEFLAGS in \
      *\\[\ \	]*) \
        bs=\\; \
        sane_makeflags=`printf '%s\n' "$$MAKEFLAGS" \
          | sed "s/$$bs$$bs[$$bs $$bs	]*//g"`;; \
    esac; \
  fi; \
  skip_next=no; \
  strip_trailopt () \
  { \
    flg=`printf '%s\n' "$$flg" | sed "s/$$1.*$$//"`; \
  }; \
  for flg in $$sane_makeflags; do \
    test $$skip_next = yes && { skip_next=no; continue; }; \
    case $$flg in \
      *=*|--*) continue;; \
        -*I) strip_trailopt 'I'; skip_next=yes;; \
      -*I?*) strip_trailopt 'I';; \
        -*O) strip_trailopt 'O'; skip_next=yes;; \
      -*O?*) strip_trailopt 'O';; \
        -*l) strip_trailopt 'l'; skip_next=yes;; \
      -*l?*) strip_trailopt 'l';; \
      -[dEDm]) skip_next=yes;; \
      -[JT]) skip_next=yes;; \
    esac; \
    case $$flg in \
      *$$target_option*) has_opt=yes; break;; \
    esac; \
  done; \
  test $$has_opt = yes
am__make_dryrun = (target_option=n; $(am__make_running_with_option))
am__make_keepgoing = (target_option=k; $(am__make_running_with_option))
pkgdatadir = $(datadir)/volclava
pkgincludedir = $(includedir)/volclava
pkglibdir = $(libdir)/volclava
pkglibexecdir = $(libexecdir)/volclava
am__cd = CDPATH="$${ZSH_VERSION+.}$(PATH_SEPARATOR)" && cd
install_sh_DATA = $(install_sh) -c -m 644
install_sh_PROGRAM = $(install_sh) -c
install_sh_SCRIPT = $(install_sh) -c
INSTALL_HEADER = $(INSTALL_DATA)
transform = $(program_transform_name)
NORMAL_INSTALL = :
PRE_INSTALL = :
POST_INSTALL = :
NORMAL_UNINSTALL = :
PRE_UNINSTALL = :
POST_UNINSTALL = :
build_triplet = x86_64-pc-linux-gnu
host_triplet = x86_64-pc-linux-gnu
target_triplet = x86_64-pc-linux-gnu
sbin_PROGRAMS = mbatchd$(EXEEXT) sbatchd$(EXEEXT)
#am__append_1 = mbd.epolicy.c
am__append_2 = mbd.policy.c
am__append_3 = -lnsl
am__append_4 = -lnsl
subdir = lsbatch/daemons
ACLOCAL_M4 = $(top_srcdir)/aclocal.m4
am__aclocal_m4_deps = $(top_srcdir)/configure.ac
am__configure_deps = $(am__aclocal_m4_deps) $(CONFIGURE_DEPENDENCIES) \
	$(ACLOCAL_M4)
DIST_COMMON = $(srcdir)/Makefile.am $(am__DIST_COMMON)
mkinstalldirs = $(install_sh) -d
CONFIG_HEADER = $(top_builddir)/config.h
CONFIG_CLEAN_FILES =
CONFIG_CLEAN_VPATH_FILES =
am__installdirs = "$(DESTDIR)$(sbindir)"
PROGRAMS = $(sbin_PROGRAMS)
am__mbatchd_SOURCES_DIST = mbd.comm.c mbd.host.c mbd.jgrp.c mbd.main.c \
	mbd.proxy.c mbd.resource.c mbd.dep.c mbd.init.c mbd.job.c \
	mbd.misc.c mbd.queue.c mbd.serv.c mbd.grp.c mbd.jarray.c \
	mbd.log.c mbd.requeue.c mbd.window.c mbd.fairshare.c \
	mbd.batch.c elock.c misc.c mail.c daemons.c daemons.xdr.c \
	mbd.h daemonout.h daemons.h jgrp.h proxy.h mbd.profcnt.def \
	mbd.fairshare.h mbd.batch.h mbd.epolicy.c mbd.policy.c
#am__objects_1 = mbd.epolicy.$(OBJEXT)
am__objects_2 = mbd.policy.$(OBJEXT)
am_mbatchd_OBJECTS = mbd.comm.$(OBJEXT) mbd.host.$(OBJEXT) \
	mbd.jgrp.$(OBJEXT) mbd.main.$(OBJEXT) mbd.proxy.$(OBJEXT) \
	mbd.resource.$(OBJEXT) mbd.dep.$(OBJEXT) mbd.init.$(OBJEXT) \
	mbd.job.$(OBJEXT) mbd.misc.$(OBJEXT) mbd.queue.$(OBJEXT) \
	mbd.serv.$(OBJEXT) mbd.grp.$(OBJEXT) mbd.jarray.$(OBJEXT) \
	mbd.log.$(OBJEXT) mbd.requeue.$(OBJEXT) mbd.window.$(OBJEXT) \
	mbd.fairshare.$(OBJEXT) mbd.batch.$(OBJEXT) elock.$(OBJEXT) \
	misc.$(OBJEXT) mail.$(OBJEXT) daemons.$(OBJEXT) \
	daemons.xdr.$(OBJEXT) $(am__objects_1) $(am__objects_2)
mbatchd_OBJECTS = $(am_mbatchd_OBJECTS)
am__DEPENDENCIES_1 =
mbatchd_DEPENDENCIES = ../lib/liblsbatch.a ../../lsf/lib/liblsf.a \
	../../lsf/intlib/liblsfint.a $(am__DEPENDENCIES_1)
am_sbatchd_OBJECTS = sbd.comm.$(OBJEXT) sbd.file.$(OBJEXT) \
	sbd.job.$(OBJEXT) sbd.main.$(OBJEXT) sbd.misc.$(OBJEXT) \
	sbd.policy.$(OBJEXT) sbd.serv.$(OBJEXT) sbd.sig.$(OBJEXT) \
	sbd.xdr.$(OBJEXT) elock.$(OBJEXT) mail.$(OBJEXT) \
	misc.$(OBJEXT) daemons.$(OBJEXT) daemons.xdr.$(OBJEXT)
sbatchd_OBJECTS = $(am_sbatchd_OBJECTS)
sbatchd_DEPENDENCIES = ../lib/liblsbatch.a ../../lsf/lib/liblsf.a \
	../../lsf/intlib/liblsfint.a $(am__DEPENDENCIES_1)
AM_V_P = $(am__v_P_$(V))
am__v_P_ = $(am__v_P_$(AM_DEFAULT_VERBOSITY))
am__v_P_0 = false
am__v_P_1 = :
AM_V_GEN = $(am__v_GEN_$(V))
am__v_GEN_ = $(am__v_GEN_$(AM_DEFAULT_VERBOSITY))
am__v_GEN_0 = @echo "  GEN     " $@;
am__v_GEN_1 = 
AM_V_at = $(am__v_at_$(V))
am__v_at_ = $(am__v_at_$(AM_DEFAULT_VERBOSITY))
am__v_at_0 = @
am__v_at_1 = 
DEFAULT_INCLUDES = -I. -I$(top_builddir)
depcomp = $(SHELL) $(top_srcdir)/depcomp
am__maybe_remake_depfiles = depfiles
am__depfiles_remade = ./$(DEPDIR)/daemons.Po \
	./$(DEPDIR)/daemons.xdr.Po ./$(DEPDIR)/elock.Po \
	./$(DEPDIR)/mail.Po ./$(DEPDIR)/mbd.batch.Po \
	./$(DEPDIR)/mbd.comm.Po ./$(DEPDIR)/mbd.dep.Po \
	./$(DEPDIR)/mbd.epolicy.Po ./$(DEPDIR)/mbd.fairshare.Po \
	./$(DEPDIR)/mbd.grp.Po ./$(DEPDIR)/mbd.host.Po \
	./$(DEPDIR)/mbd.init.Po ./$(DEPDIR)/mbd.jarray.Po \
	./$(DEPDIR)/mbd.jgrp.Po ./$(DEPDIR)/mbd.job.Po \
	./$(DEPDIR)/mbd.log.Po ./$(DEPDIR)/mbd.main.Po \
	./$(DEPDIR)/mbd.misc.Po ./$(DEPDIR)/mbd.policy.Po \
	./$(DEPDIR)/mbd.proxy.Po ./$(DEPDIR)/mbd.queue.Po \
	./$(DEPDIR)/mbd.requeue.Po ./$(DEPDIR)/mbd.resource.Po \
	./$(DEPDIR)/mbd.serv.Po ./$(DEPDIR)/mbd.window.Po \
	./$(DEPDIR)/misc.Po ./$(DEPDIR)/sbd.comm.Po \
	./$(DEPDIR)/sbd.file.Po ./$(DEPDIR)/sbd.job.Po \
	./$(DEPDIR)/sbd.main.Po ./$(DEPDIR)/sbd.misc.Po \
	./$(DEPDIR)/sbd.policy.Po ./$(DEPDIR)/sbd.serv.Po \
	./$(DEPDIR)/sbd.sig.Po ./$(DEPDIR)/sbd.xdr.Po
am__mv = mv -f
COMPILE = $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) \
	$(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS)
AM_V_CC = $(am__v_CC_$(V))
am__v_CC_ = $(am__v_CC_$(AM_DEFAULT_VERBOSITY))
am__v_CC_0 = @echo "  CC      " $@;
am__v_CC_1 = 
CCLD = $(CC)
LINK = $(CCLD) $(AM_CFLAGS) $(CFLAGS) $(AM_LDFLAGS) $(LDFLAGS) -o $@
AM_V_CCLD = $(am__v_CCLD_$(V))
am__v_CCLD_ = $(am__v_CCLD_$(AM_DEFAULT_VERBOSITY))
am__v_CCLD_0 = @echo "  CCLD    " $@;
am__v_CCLD_1 = 
SOURCES = $(mbatchd_SOURCES) $(sbatchd_SOURCES)
DIST_SOURCES = $(am__mbatchd_SOURCES_DIST) $(sbatchd_SOURCES)
am__can_run_installinfo = \
  case $$AM_UPDATE_INFO_DIR in \
    n|no|NO) false;; \
    *) (install-info --version) >/dev/null 2>&1;; \
  esac
am__tagged_files = $(HEADERS) $(SOURCES) $(TAGS_FILES) $(LISP)
# Read a list of newline-separated strings from the standard input,
# and print each of them once, without duplicates.  Input order is
# *not* preserved.
am__uniquify_input = $(AWK) '\
  BEGIN { nonempty = 0; } \
  { items[$$0] = 1; nonempty = 1; } \
  END { if (nonempty) { for (i in items) print i; }; } \
'
# Make sure the list of sources is unique.  This is necessary because,
# e.g., the same source file might be shared among _SOURCES variables
# for different programs/libraries.
am__define_uniq_tagged_files = \
  list='$(am__tagged_files)'; \
  unique=`for i in $$list; do \
    if test -f "$$i"; then echo $$i; else echo $(srcdir)/$$i; fi; \
  done | $(am__uniquify_input)`
ETAGS = etags
CTAGS = ctags
am__DIST_COMMON = $(srcdir)/Makefile.in $(top_srcdir)/depcomp
DISTFILES = $(DIST_COMMON) $(DIST_SOURCES) $(TEXINFOS) $(EXTRA_DIST)
ACLOCAL = ${SHELL} /mnt/hgfs/ubuntu_share/volclava/missing aclocal-1.16
AMTAR = $${TAR-tar}
AM_DEFAULT_VERBOSITY = 0
AUTOCONF = ${SHELL} /mnt/hgfs/ubuntu_share/volclava/missing autoconf
AUTOHEADER = ${SHELL} /mnt/hgfs/ubuntu_share/volclava/missing autoheader
AUTOMAKE = ${SHELL} /mnt/hgfs/ubuntu_share/volclava/missing automake-1.16
AWK = mawk
CC = gcc
CCDEPMODE = depmode=gcc3
CFLAGS = -g -O2 -Wall -fPIC -Wno-error=format-security -I/usr/include/tirpc
CPP = gcc -E
CPPFLAGS = 
CYGPATH_W = echo
DEFS = -DHAVE_CONFIG_H
DEPDIR = .deps
ECHO_C = 
ECHO_N = -n
ECHO_T = 
EGREP = /usr/bin/grep -E
EXEEXT = 
GREP = /usr/bin/grep
INSTALL = /usr/bin/install -c
INSTALL_DATA = ${INSTALL} -m 644
INSTALL_PROGRAM = ${INSTALL}
INSTALL_SCRIPT = ${INSTALL}
INSTALL_STRIP_PROGRAM = $(install_sh) -c -s
LDFLAGS = 
LEX = :
LEXLIB = 
LEX_OUTPUT_ROOT = 
LIBOBJS = 
LIBS = -ltcl 
LN_S = cp -pR
LTLIBOBJS = 
MAKEINFO = ${SHELL} /mnt/hgfs/ubuntu_share/volclava/missing makeinfo
MKDIR_P = /usr/bin/mkdir -p
OBJEXT = o
PACKAGE = volclava
PACKAGE_BUGREPORT = 
PACKAGE_NAME = volclava
PACKAGE_STRING = volclava 2.0
PACKAGE_TARNAME = volclava
PACKAGE_URL = 
PACKAGE_VERSION = 2.0
PATH_SEPARATOR = :
RANLIB = ranlib
SET_MAKE = 
SHELL = /bin/bash
STRIP = 
VERSION = 2.0
YACC = yacc
YFLAGS = 
abs_builddir = /mnt/hgfs/ubuntu_share/volclava/lsbatch/daemons
abs_srcdir = /mnt/hgfs/ubuntu_share/volclava/lsbatch/daemons
abs_top_builddir = /mnt/hgfs/ubuntu_share/volclava
abs_top_srcdir = /mnt/hgfs/ubuntu_share/volclava
ac_ct_CC = gcc
am__include = include
am__leading_dot = .
am__quote = 
am__tar = $${TAR-tar} chof - "$$tardir"
am__untar = $${TAR-tar} xf -
bindir = ${exec_prefix}/bin
build = x86_64-pc-linux-gnu
build_alias = 
build_cpu = x86_64
build_os = linux-gnu
build_vendor = pc
builddir = .
datadir = ${datarootdir}
datarootdir = ${prefix}/share
docdir = ${datarootdir}/doc/${PACKAGE_TARNAME}
dvidir = ${docdir}
exec_prefix = ${prefix}
host = x86_64-pc-linux-gnu
host_alias = 
host_cpu = x86_64
host_os = linux-gnu
host_vendor = pc
htmldir = ${docdir}
includedir = ${prefix}/include
infodir = ${datarootdir}/info
install_sh = ${SHELL} /mnt/hgfs/ubuntu_share/volclava/install-sh
libdir = ${exec_prefix}/lib
libexecdir = ${exec_prefix}/libexec
localedir = ${datarootdir}/locale
localstatedir = ${prefix}/var
mandir = ${datarootdir}/man
mkdir_p = $(MKDIR_P)
oldincludedir = /usr/include
pdfdir = ${docdir}
prefix = /opt/volclava-2.0
program_transform_name = s,x,x,
psdir = ${docdir}
runstatedir = ${localstatedir}/run
sbindir = ${exec_prefix}/sbin
sharedstatedir = ${prefix}/com
srcdir = .
sysconfdir = ${prefix}/etc
target = x86_64-pc-linux-gnu
target_alias = 
target_cpu = x86_64
target_os = linux-gnu
target_vendor = pc
top_build_prefix = ../../
top_builddir = ../..
top_srcdir = ../..
volclavaadmin = 
volclavacluster = volclava
volclavadmin = volclava

#
# Copyright (C) 2021-2025 Bytedance Ltd. and/or its affiliates
# Copyright (C) openlava foundation
#
INCLUDES = -I$(top_srcdir)/lsf  -I$(top_srcdir)/lsf/lib \
           -I$(top_srcdir)/lsbatch  -I$(top_srcdir)/lsbatch/lib -I./

mbatchd_SOURCES = mbd.comm.c mbd.host.c mbd.jgrp.c mbd.main.c \
	mbd.proxy.c mbd.resource.c mbd.dep.c mbd.init.c mbd.job.c \
	mbd.misc.c mbd.queue.c mbd.serv.c mbd.grp.c mbd.jarray.c \
	mbd.log.c mbd.requeue.c mbd.window.c mbd.fairshare.c \
	mbd.batch.c elock.c misc.c mail.c daemons.c daemons.xdr.c \
	mbd.h daemonout.h daemons.h jgrp.h proxy.h mbd.profcnt.def \
	mbd.fairshare.h mbd.batch.h $(am__append_1) $(am__append_2)
mbatchd_LDADD = ../lib/liblsbatch.a ../../lsf/lib/liblsf.a \
	../../lsf/intlib/liblsfint.a -lm $(am__append_3)
sbatchd_SOURCES = sbd.comm.c sbd.file.c sbd.job.c sbd.main.c \
                  sbd.misc.c sbd.policy.c sbd.serv.c sbd.sig.c sbd.xdr.c \
                  elock.c mail.c misc.c daemons.c daemons.xdr.c \
                  sbd.h daemonout.h daemons.h 

sbatchd_LDADD = ../lib/liblsbatch.a ../../lsf/lib/liblsf.a \
	../../lsf/intlib/liblsfint.a -lm $(am__append_4)
all: all-am

.SUFFIXES:
.SUFFIXES: .c .o .obj
$(srcdir)/Makefile.in:  $(srcdir)/Makefile.am  $(am__configure_deps)
	@for dep in $?; do \
	  case '$(am__configure_deps)' in \
	    *$$dep*) \
	      ( cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh ) \
	        && { if test -f $@; then exit 0; else break; fi; }; \
	      exit 1;; \
	  esac; \
	done; \
	echo ' cd $(top_srcdir) && $(AUTOMAKE) --gnu lsbatch/daemons/Makefile'; \
	$(am__cd) $(top_srcdir) && \
	  $(AUTOMAKE) --gnu lsbatch/daemons/Makefile
Makefile: $(srcdir)/Makefile.in $(top_builddir)/config.status
	@case '$?' in \
	  *config.status*) \
	    cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh;; \
	  *) \
	    echo ' cd $(top_builddir) && $(SHELL) ./config.status $(subdir)/$@ $(am__maybe_remake_depfiles)'; \
	    cd $(top_builddir) && $(SHELL) ./config.status $(subdir)/$@ $(am__maybe_remake_depfiles);; \
	esac;

$(top_builddir)/config.status: $(top_srcdir)/configure $(CONFIG_STATUS_DEPENDENCIES)
	cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh

$(top_srcdir)/configure:  $(am__configure_deps)
	cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh
$(ACLOCAL_M4):  $(am__aclocal_m4_deps)
	cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh
$(am__aclocal_m4_deps):
install-sbinPROGRAMS: $(sbin_PROGRAMS)
	@$(NORMAL_INSTALL)
	@list='$(sbin_PROGRAMS)'; test -n "$(sbindir)" || list=; \
	if test -n "$$list"; then \
	  echo " $(MKDIR_P) '$(DESTDIR)$(sbindir)'"; \
	  $(MKDIR_P) "$(DESTDIR)$(sbindir)" || exit 1; \
	fi; \
	for p in $$list; do echo "$$p $$p"; done | \
	sed 's/$(EXEEXT)$$//' | \
	while read p p1; do if test -f $$p \
	  ; then echo "$$p"; echo "$$p"; else :; fi; \
	done | \
	sed -e 'p;s,.*/,,;n;h' \
	    -e 's|.*|.|' \
	    -e 'p;x;s,.*/,,;s/$(EXEEXT)$$//;$(transform);s/$$/$(EXEEXT)/' | \
	sed 'N;N;N;s,\n, ,g' | \
	$(AWK) 'BEGIN { files["."] = ""; dirs["."] = 1 } \
	  { d=$$3; if (dirs[d] != 1) { print "d", d; dirs[d] = 1 } \
	    if ($$2 == $$4) files[d] = files[d] " " $$1; \
	    else { print "f", $$3 "/" $$4, $$1; } } \
	  END { for (d in files) print "f", d, files[d] }' | \
	while read type dir files; do \
	    if test "$$dir" = .; then dir=; else dir=/$$dir; fi; \
	    test -z "$$files" || { \
	      echo " $(INSTALL_PROGRAM_ENV) $(INSTALL_PROGRAM) $$files '$(DESTDIR)$(sbindir)$$dir'"; \
	      $(INSTALL_PROGRAM_ENV) $(INSTALL_PROGRAM) $$files "$(DESTDIR)$(sbindir)$$dir" || exit $$?; \
	    } \
	; done

uninstall-sbinPROGRAMS:
	@$(NORMAL_UNINSTALL)
	@list='$(sbin_PROGRAMS)'; test -n "$(sbindir)" || list=; \
	files=`for p in $$list; do echo "$$p"; done | \
	  sed -e 'h;s,^.*/,,;s/$(EXEEXT)$$//;$(transform)' \
	      -e 's/$$/$(EXEEXT)/' \
	`; \
	test -n "$$list" || exit 0; \
	echo " ( cd '$(DESTDIR)$(sbindir)' && rm -f" $$files ")"; \
	cd "$(DESTDIR)$(sbindir)" && rm -f $$files

clean-sbinPROGRAMS:
	-test -z "$(sbin_PROGRAMS)" || rm -f $(sbin_PROGRAMS)

mbatchd$(EXEEXT): $(mbatchd_OBJECTS) $(mbatchd_DEPENDENCIES) $(EXTRA_mbatchd_DEPENDENCIES) 
	@rm -f mbatchd$(EXEEXT)
	$(AM_V_CCLD)$(LINK) $(mbatchd_OBJECTS) $(mbatchd_LDADD) $(LIBS)

sbatchd$(EXEEXT): $(sbatchd_OBJECTS) $(sbatchd_DEPENDENCIES) $(EXTRA_sbatchd_DEPENDENCIES) 
	@rm -f sbatchd$(EXEEXT)
	$(AM_V_CCLD)$(LINK) $(sbatchd_OBJECTS) $(sbatchd_LDADD) $(LIBS)

mostlyclean-compile:
	-rm -f *.$(OBJEXT)

distclean-compile:
	-rm -f *.tab.c

include ./$(DEPDIR)/daemons.Po # am--include-marker
include ./$(DEPDIR)/daemons.xdr.Po # am--include-marker
include ./$(DEPDIR)/elock.Po # am--include-marker
include ./$(DEPDIR)/mail.Po # am--include-marker
include ./$(DEPDIR)/mbd.batch.Po # am--include-marker
include ./$(DEPDIR)/mbd.comm.Po # am--include-marker
include ./$(DEPDIR)/mbd.dep.Po # am--include-marker
include ./$(DEPDIR)/mbd.epolicy.Po # am--include-marker
include ./$(DEPDIR)/mbd.fairshare.Po # am--include-marker
include ./$(DEPDIR)/mbd.grp.Po # am--include-marker
include ./$(DEPDIR)/mbd.host.Po # am--include-marker
include ./$(DEPDIR)/mbd.init.Po # am--include-marker
include ./$(DEPDIR)/mbd.jarray.Po # am--include-marker
include ./$(DEPDIR)/mbd.jgrp.Po # am--include-marker
include ./$(DEPDIR)/mbd.job.Po # am--include-marker
include ./$(DEPDIR)/mbd.log.Po # am--include-marker
include ./$(DEPDIR)/mbd.main.Po # am--include-marker
include ./$(DEPDIR)/mbd.misc.Po # am--include-marker
include ./$(DEPDIR)/mbd.policy.Po # am--include-marker
include ./$(DEPDIR)/mbd.proxy.Po # am--include-marker
include ./$(DEPDIR)/mbd.queue.Po # am--include-marker
include ./$(DEPDIR)/mbd.requeue.Po # am--include-marker
include ./$(DEPDIR)/mbd.resource.Po # am--include-marker
include ./$(DEPDIR)/mbd.serv.Po # am--include-marker
include ./$(DEPDIR)/mbd.window.Po # am--include-marker
include ./$(DEPDIR)/misc.Po # am--include-marker
include ./$(DEPDIR)/sbd.comm.Po # am--include-marker
include ./$(DEPDIR)/sbd.file.Po # am--include-marker
include ./$(DEPDIR)/sbd.job.Po # am--include-marker
include ./$(DEPDIR)/sbd.main.Po # am--include-marker
include ./$(DEPDIR)/sbd.misc.Po # am--include-marker
include ./$(DEPDIR)/sbd.policy.Po # am--include-marker
include ./$(DEPDIR)/sbd.serv.Po # am--include-marker
include ./$(DEPDIR)/sbd.sig.Po # am--include-marker
include ./$(DEPDIR)/sbd.xdr.Po # am--include-marker

$(am__depfiles_remade):
	@$(MKDIR_P) $(@D)
	@echo '# dummy' >$@-t && $(am__mv) $@-t $@

am--depfiles: $(am__depfiles_remade)

.c.o:
	$(AM_V_CC)$(COMPILE) -MT $@ -MD -MP -MF $(DEPDIR)/$*.Tpo -c -o $@ $<
	$(AM_V_at)$(am__mv) $(DEPDIR)/$*.Tpo $(DEPDIR)/$*.Po
#	$(AM_V_CC)source='$<' object='$@' libtool=no \
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) \
#	$(AM_V_CC_no)$(COMPILE) -c -o $@ $<

.c.obj:
	$(AM_V_CC)$(COMPILE) -MT $@ -MD -MP -MF $(DEPDIR)/$*.Tpo -c -o $@ `$(CYGPATH_W) '$<'`
	$(AM_V_at)$(am__mv) $(DEPDIR)/$*.Tpo $(DEPDIR)/$*.Po
#	$(AM_V_CC)source='$<' object='$@' libtool=no \
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) \
#	$(AM_V_CC_no)$(COMPILE) -c -o $@ `$(CYGPATH_W) '$<'`

ID: $(am__tagged_files)
	$(am__define_uniq_tagged_files); mkid -fID $$unique
tags: tags-am
TAGS: tags

tags-am: $(TAGS_DEPENDENCIES) $(am__tagged_files)
	set x; \
	here=`pwd`; \
	$(am__define_uniq_tagged_files); \
	shift; \
	if test -z "$(ETAGS_ARGS)$$*$$unique"; then :; else \
	  test -n "$$unique" || unique=$$empty_fix; \
	  if test $$# -gt 0; then \
	    $(ETAGS) $(ETAGSFLAGS) $(AM_ETAGSFLAGS) $(ETAGS_ARGS) \
	      "$$@" $$unique; \
	  else \
	    $(ETAGS) $(ETAGSFLAGS) $(AM_ETAGSFLAGS) $(ETAGS_ARGS) \
	      $$unique; \
	  fi; \
	fi
ctags: ctags-am

CTAGS: ctags
ctags-am: $(TAGS_DEPENDENCIES) $(am__tagged_files)
	$(am__define_uniq_tagged_files); \
	test -z "$(CTAGS_ARGS)$$unique" \
	  || $(CTAGS) $(CTAGSFLAGS) $(AM_CTAGSFLAGS) $(CTAGS_ARGS) \
	     $$unique

GTAGS:
	here=`$(am__cd) $(top_builddir) && pwd` \
	  && $(am__cd) $(top_srcdir) \
	  && gtags -i $(GTAGS_ARGS) "$$here"
cscopelist: cscopelist-am

cscopelist-am: $(am__tagged_files)
	list='$(am__tagged_files)'; \
	case "$(srcdir)" in \
	  [\\/]* | ?:[\\/]*) sdir="$(srcdir)" ;; \
	  *) sdir=$(subdir)/$(srcdir) ;; \
	esac; \
	for i in $$list; do \
	  if test -f "$$i"; then \
	    echo "$(subdir)/$$i"; \
	  else \
	    echo "$$sdir/$$i"; \
	  fi; \
	done >> $(top_builddir)/cscope.files

distclean-tags:
	-rm -f TAGS ID GTAGS GRTAGS GSYMS GPATH tags

distdir: $(BUILT_SOURCES)
	$(MAKE) $(AM_MAKEFLAGS) distdir-am

distdir-am: $(DISTFILES)
	@srcdirstrip=`echo "$(srcdir)" | sed 's/[].[^$$\\*]/\\\\&/g'`; \
	topsrcdirstrip=`echo "$(top_srcdir)" | sed 's/[].[^$$\\*]/\\\\&/g'`; \
	list='$(DISTFILES)'; \
	  dist_files=`for file in $$list; do echo $$file; done | \
	  sed -e "s|^$$srcdirstrip/||;t" \
	      -e "s|^$$topsrcdirstrip/|$(top_builddir)/|;t"`; \
	case $$dist_files in \
	  */*) $(MKDIR_P) `echo "$$dist_files" | \
			   sed '/\//!d;s|^|$(distdir)/|;s,/[^/]*$$,,' | \
			   sort -u` ;; \
	esac; \
	for file in $$dist_files; do \
	  if test -f $$file || test -d $$file; then d=.; else d=$(srcdir); fi; \
	  if test -d $$d/$$file; then \
	    dir=`echo "/$$file" | sed -e 's,/[^/]*$$,,'`; \
	    if test -d "$(distdir)/$$file"; then \
	      find "$(distdir)/$$file" -type d ! -perm -700 -exec chmod u+rwx {} \;; \
	    fi; \
	    if test -d $(srcdir)/$$file && test $$d != $(srcdir); then \
	      cp -fpR $(srcdir)/$$file "$(distdir)$$dir" || exit 1; \
	      find "$(distdir)/$$file" -type d ! -perm -700 -exec chmod u+rwx {} \;; \
	    fi; \
	    cp -fpR $$d/$$file "$(distdir)$$dir" || exit 1; \
	  else \
	    test -f "$(distdir)/$$file" \
	    || cp -p $$d/$$file "$(distdir)/$$file" \
	    || exit 1; \
	  fi; \
	done
check-am: all-am
check: check-am
all-am: Makefile $(PROGRAMS)
installdirs:
	for dir in "$(DESTDIR)$(sbindir)"; do \
	  test -z "$$dir" || $(MKDIR_P) "$$dir"; \
	done
install: install-am
install-exec: install-exec-am
install-data: install-data-am
uninstall: uninstall-am

install-am: all-am
	@$(MAKE) $(AM_MAKEFLAGS) install-exec-am install-data-am

installcheck: installcheck-am
install-strip:
	if test -z '$(STRIP)'; then \
	  $(MAKE) $(AM_MAKEFLAGS) INSTALL_PROGRAM="$(INSTALL_STRIP_PROGRAM)" \
	    install_sh_PROGRAM="$(INSTALL_STRIP_PROGRAM)" INSTALL_STRIP_FLAG=-s \
	      install; \
	else \
	  $(MAKE) $(AM_MAKEFLAGS) INSTALL_PROGRAM="$(INSTALL_STRIP_PROGRAM)" \
	    install_sh_PROGRAM="$(INSTALL_STRIP_PROGRAM)" INSTALL_STRIP_FLAG=-s \
	    "INSTALL_PROGRAM_ENV=STRIPPROG='$(STRIP)'" install; \
	fi
mostlyclean-generic:

clean-generic:

distclean-generic:
	-test -z "$(CONFIG_CLEAN_FILES)" || rm -f $(CONFIG_CLEAN_FILES)
	-test . = "$(srcdir)" || test -z "$(CONFIG_CLEAN_VPATH_FILES)" || rm -f $(CONFIG_CLEAN_VPATH_FILES)

maintainer-clean-generic:
	@echo "This command is intended for maintainers to use"
	@echo "it deletes files that may require special tools to rebuild."
clean: clean-am

clean-am: clean-generic clean-sbinPROGRAMS mostlyclean-am

distclean: distclean-am
		-rm -f ./$(DEPDIR)/daemons.Po
	-rm -f ./$(DEPDIR)/daemons.xdr.Po
	-rm -f ./$(DEPDIR)/elock.Po
	-rm -f ./$(DEPDIR)/mail.Po
	-rm -f ./$(DEPDIR)/mbd.batch.Po
	-rm -f ./$(DEPDIR)/mbd.comm.Po
	-rm -f ./$(DEPDIR)/mbd.dep.Po
	-rm -f ./$(DEPDIR)/mbd.epolicy.Po
	-rm -f ./$(DEPDIR)/mbd.fairshare.Po
	-rm -f ./$(DEPDIR)/mbd.grp.Po
	-rm -f ./$(DEPDIR)/mbd.host.Po
	-rm -f ./$(DEPDIR)/mbd.init.Po
	-rm -f ./$(DEPDIR)/mbd.jarray.Po
	-rm -f ./$(DEPDIR)/mbd.jgrp.Po
	-rm -f ./$(DEPDIR)/mbd.job.Po
	-rm -f ./$(DEPDIR)/mbd.log.Po
	-rm -f ./$(DEPDIR)/mbd.main.Po
	-rm -f ./$(DEPDIR)/mbd.misc.Po
	-rm -f ./$(DEPDIR)/mbd.policy.Po
	-rm -f ./$(DEPDIR)/mbd.proxy.Po
	-rm -f ./$(DEPDIR)/mbd.queue.Po
	-rm -f ./$(DEPDIR)/mbd.requeue.Po
	-rm -f ./$(DEPDIR)/mbd.resource.Po
	-rm -f ./$(DEPDIR)/mbd.serv.Po
	-rm -f ./$(DEPDIR)/mbd.window.Po
	-rm -f ./$(DEPDIR)/misc.Po
	-rm -f ./$(DEPDIR)/sbd.comm.Po
	-rm -f ./$(DEPDIR)/sbd.file.Po
	-rm -f ./$(DEPDIR)/sbd.job.Po
	-rm -f ./$(DEPDIR)/sbd.main.Po
	-rm -f ./$(DEPDIR)/sbd.misc.Po
	-rm -f ./$(DEPDIR)/sbd.policy.Po
	-rm -f ./$(DEPDIR)/sbd.serv.Po
	-rm -f ./$(DEPDIR)/sbd.sig.Po
	-rm -f ./$(DEPDIR)/sbd.xdr.Po
	-rm -f Makefile
distclean-am: clean-am distclean-compile distclean-generic \
	distclean-tags

dvi: dvi-am

dvi-am:

html: html-am

html-am:

info: info-am

info-am:

install-data-am:

install-dvi: install-dvi-am

install-dvi-am:

install-exec-am: install-sbinPROGRAMS

install-html: install-html-am

install-html-am:

install-info: install-info-am

install-info-am:

install-man:

install-pdf: install-pdf-am

install-pdf-am:

install-ps: install-ps-am

install-ps-am:

installcheck-am:

maintainer-clean: maintainer-clean-am
		-rm -f ./$(DEPDIR)/daemons.Po
	-rm -f ./$(DEPDIR)/daemons.xdr.Po
	-rm -f ./$(DEPDIR)/elock.Po
	-rm -f ./$(DEPDIR)/mail.Po
	-rm -f ./$(DEPDIR)/mbd.batch.Po
	-rm -f ./$(DEPDIR)/mbd.comm.Po
	-rm -f ./$(DEPDIR)/mbd.dep.Po
	-rm -f ./$(DEPDIR)/mbd.epolicy.Po
	-rm -f ./$(DEPDIR)/mbd.fairshare.Po
	-rm -f ./$(DEPDIR)/mbd.grp.Po
	-rm -f ./$(DEPDIR)/mbd.host.Po
	-rm -f ./$(DEPDIR)/mbd.init.Po
	-rm -f ./$(DEPDIR)/mbd.jarray.Po
	-rm -f ./$(DEPDIR)/mbd.jgrp.Po
	-rm -f ./$(DEPDIR)/mbd.job.Po
	-rm -f ./$(DEPDIR)/mbd.log.Po
	-rm -f ./$(DEPDIR)/mbd.main.Po
	-rm -f ./$(DEPDIR)/mbd.misc.Po
	-rm -f ./$(DEPDIR)/mbd.policy.Po
	-rm -f ./$(DEPDIR)/mbd.proxy.Po
	-rm -f ./$(DEPDIR)/mbd.queue.Po
	-rm -f ./$(DEPDIR)/mbd.requeue.Po
	-rm -f ./$(DEPDIR)/mbd.resource.Po
	-rm -f ./$(DEPDIR)/mbd.serv.Po
	-rm -f ./$(DEPDIR)/mbd.window.Po
	-rm -f ./$(DEPDIR)/misc.Po
	-rm -f ./$(DEPDIR)/sbd.comm.Po
	-rm -f ./$(DEPDIR)/sbd.file.Po
	-rm -f ./$(DEPDIR)/sbd.job.Po
	-rm -f ./$(DEPDIR)/sbd.main.Po
	-rm -f ./$(DEPDIR)/sbd.misc.Po
	-rm -f ./$(DEPDIR)/sbd.policy.Po
	-rm -f ./$(DEPDIR)/sbd.serv.Po
	-rm -f ./$(DEPDIR)/sbd.sig.Po
	-rm -f ./$(DEPDIR)/sbd.xdr.Po
	-rm -f Makefile
maintainer-clean-am: distclean-am maintainer-clean-generic

mostlyclean: mostlyclean-am

mostlyclean-am: mostlyclean-compile mostlyclean-generic

pdf: pdf-am

pdf-am:

ps: ps-am

ps-am:

uninstall-am: uninstall-sbinPROGRAMS

.MAKE: install-am install-strip

.PHONY: CTAGS GTAGS TAGS all all-am am--depfiles check check-am clean \
	clean-generic clean-sbinPROGRAMS cscopelist-am ctags ctags-am \
	distclean distclean-compile distclean-generic distclean-tags \
	distdir dvi dvi-am html html-am info info-am install \
	install-am install-data install-data-am install-dvi \
	install-dvi-am install-exec install-exec-am install-html \
	install-html-am install-info install-info-am install-man \
	install-pdf install-pdf-am install-ps install-ps-am \
	install-sbinPROGRAMS install-strip installcheck \
	installcheck-am installdirs maintainer-clean \
	maintainer-clean-generic mostlyclean mostlyclean-compile \
	mostlyclean-generic pdf pdf-am ps ps-am tags tags-am uninstall \
	uninstall-am uninstall-sbinPROGRAMS

.PRECIOUS: Makefile


etags :
	etags *.[hc] ../*.h ../lib/*.[hc] ../../lsf/*.h \
  	../../lsf/lib/*.[hc] ../../lsf/intlib/*.[hc]

# Tell versions [3.59,3.63) of GNU make to not export all variables.
# Otherwise a system limit (for SysV at least) may be exceeded.
.NOEXPORT:
