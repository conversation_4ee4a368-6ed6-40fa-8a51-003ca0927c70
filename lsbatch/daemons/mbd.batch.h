/**
 * @file mbd.batch.h
 * @brief MBD批量作业处理模块头文件
 * 
 * 定义了批量作业处理相关的数据结构、常量和函数声明
 */

#ifndef _MBD_BATCH_H_
#define _MBD_BATCH_H_

#include <time.h>
#include <sys/types.h>
#include "../lsbatch.h"

/* 批量处理相关常量 */
#define BATCH_MAX_JOBS_DEFAULT      1000    /* 默认最大批量作业数 */
#define BATCH_MAX_JOBS_LIMIT        10000   /* 批量作业数上限 */
#define BATCH_CLEANUP_INTERVAL      300     /* 清理间隔（秒） */
#define BATCH_STREAM_TIMEOUT        600     /* 流式响应超时（秒） */
#define BATCH_NONSTREAM_TIMEOUT     3600    /* 非流式响应超时（秒） */

/* 批量提交选项标志 */
#define BATCH_SUBMIT_STREAM_RESPONSE    0x0001  /* 启用流式响应 */
#define BATCH_SUBMIT_FAIL_FAST          0x0002  /* 快速失败模式 */
#define BATCH_SUBMIT_CONTINUE_ON_ERROR  0x0004  /* 遇到错误继续处理 */
#define BATCH_SUBMIT_PRIORITY_ORDER     0x0008  /* 按优先级顺序处理 */

/* 流式消息类型 */
typedef enum {
    STREAM_MSG_JOB_RESULT = 1,      /* 作业结果消息 */
    STREAM_MSG_BATCH_STATUS = 2,    /* 批量状态消息 */
    STREAM_MSG_ERROR = 3,           /* 错误消息 */
    STREAM_MSG_COMPLETE = 4         /* 完成消息 */
} stream_message_type_t;

/* 流式消息标志 */
#define STREAM_FLAG_MORE_MESSAGES   0x0001  /* 还有更多消息 */
#define STREAM_FLAG_LAST_MESSAGE    0x0002  /* 最后一条消息 */
#define STREAM_FLAG_ERROR           0x0004  /* 错误消息 */

/* 批量作业结果结构 */
struct batchJobResult {
    int jobIndex;           /* 作业在批量中的索引 */
    LS_LONG_INT jobId;      /* LSF作业ID */
    int status;             /* 作业状态 */
    char *queue;            /* 队列名称 */
    time_t submitTime;      /* 提交时间 */
    int errorCode;          /* 错误代码（0表示成功） */
    char *errorMsg;         /* 错误消息 */
};

/* 批量提交请求结构 */
struct batchSubmitReq {
    int jobCount;                   /* 作业数量 */
    struct submitReq *jobs;         /* 作业数组 */
    int options;                    /* 批量选项标志 */
    int priority;                   /* 批量优先级 */
    char *batchName;                /* 批量名称（可选） */
    int maxConcurrentJobs;          /* 最大并发作业数 */
    int timeoutSeconds;             /* 超时时间（秒） */
};

/* 批量提交响应结构 */
struct batchSubmitReply {
    int batchId;                    /* 批量ID */
    time_t startTime;               /* 开始时间 */
    time_t completionTime;          /* 完成时间 */
    int successCount;               /* 成功作业数 */
    int failureCount;               /* 失败作业数 */
    struct batchJobResult *results; /* 结果数组 */
};

/* 流式消息头结构 */
struct streamHeader {
    stream_message_type_t messageType;  /* 消息类型 */
    int sequenceId;                     /* 序列号 */
    int batchId;                        /* 批量ID */
    int flags;                          /* 消息标志 */
    time_t timestamp;                   /* 时间戳 */
};

/* 流式作业结果结构 */
struct streamJobResult {
    int batchId;            /* 批量ID */
    int jobIndex;           /* 作业索引 */
    LS_LONG_INT jobId;      /* 作业ID */
    int status;             /* 作业状态 */
    char *queue;            /* 队列名称 */
    int resultCode;         /* 结果代码 */
    time_t timestamp;       /* 时间戳 */
};

/* 批量完成信号结构 */
struct batchCompleteSignal {
    int batchId;            /* 批量ID */
    int totalJobs;          /* 总作业数 */
    int successCount;       /* 成功数量 */
    int failureCount;       /* 失败数量 */
    time_t completionTime;  /* 完成时间 */
};

/* 批量状态查询请求 */
struct batchStatusReq {
    int batchId;            /* 批量ID */
    int options;            /* 查询选项 */
};

/* 批量状态查询响应 */
struct batchStatusReply {
    int batchId;                    /* 批量ID */
    int totalJobs;                  /* 总作业数 */
    int submittedJobs;              /* 已提交作业数 */
    int completedJobs;              /* 已完成作业数 */
    int failedJobs;                 /* 失败作业数 */
    int runningJobs;                /* 运行中作业数 */
    int pendingJobs;                /* 等待中作业数 */
    time_t startTime;               /* 开始时间 */
    time_t lastUpdateTime;          /* 最后更新时间 */
    struct batchJobResult *results; /* 详细结果（可选） */
};

/* 函数声明 */

/**
 * @brief 处理批量作业提交请求
 * @param xdrs XDR流
 * @param chfd 客户端channel文件描述符
 * @param from 客户端地址
 * @param hostName 客户端主机名
 * @param reqHdr 请求头
 * @param laddr 本地地址
 * @param auth 认证信息
 * @param schedule 调度标志
 * @param dispatch 分发标志
 * @return 0成功，-1失败
 */
extern int do_batchSubmitReq(XDR *xdrs,
                             int chfd,
                             struct sockaddr_in *from,
                             char *hostName,
                             struct LSFHeader *reqHdr,
                             struct sockaddr_in *laddr,
                             struct lsfAuth *auth,
                             int *schedule,
                             int dispatch);

/**
 * @brief 处理批量状态查询请求
 * @param xdrs XDR流
 * @param chfd 客户端channel文件描述符
 * @param from 客户端地址
 * @param hostName 客户端主机名
 * @param reqHdr 请求头
 * @param auth 认证信息
 * @return 0成功，-1失败
 */
extern int do_batchStatusReq(XDR *xdrs,
                            int chfd,
                            struct sockaddr_in *from,
                            char *hostName,
                            struct LSFHeader *reqHdr,
                            struct lsfAuth *auth);

/**
 * @brief 批量管理器清理函数
 * 定期清理已完成的批量管理器
 */
extern void batch_manager_cleanup(void);

/**
 * @brief 获取批量管理器统计信息
 * @param totalBatches 总批量数（输出）
 * @param activeBatches 活跃批量数（输出）
 * @param totalJobs 总作业数（输出）
 */
extern void batch_manager_statistics(int *totalBatches, int *activeBatches, int *totalJobs);

/**
 * @brief 初始化批量处理模块
 * @return 0成功，-1失败
 */
extern int batch_module_init(void);

/**
 * @brief 清理批量处理模块
 */
extern void batch_module_cleanup(void);

/**
 * @brief 处理作业状态变化通知
 * 当作业状态发生变化时，更新相关的批量管理器
 * @param jobData 作业数据
 * @param oldStatus 旧状态
 * @param newStatus 新状态
 */
extern void batch_job_status_update(struct jData *jobData, int oldStatus, int newStatus);

/**
 * @brief 获取批量作业的详细信息
 * @param batchId 批量ID
 * @param jobResults 输出的作业结果数组
 * @param jobCount 输出的作业数量
 * @return 0成功，-1失败
 */
extern int batch_get_job_details(int batchId, struct batchJobResult **jobResults, int *jobCount);

/* XDR函数声明 */
extern bool_t xdr_batchSubmitReq(XDR *xdrs, struct batchSubmitReq *objp, struct LSFHeader *hdr);
extern bool_t xdr_batchSubmitReply(XDR *xdrs, struct batchSubmitReply *objp, struct LSFHeader *hdr);
extern bool_t xdr_streamHeader(XDR *xdrs, struct streamHeader *objp, struct LSFHeader *hdr);
extern bool_t xdr_streamJobResult(XDR *xdrs, struct streamJobResult *objp, struct LSFHeader *hdr);
extern bool_t xdr_batchCompleteSignal(XDR *xdrs, struct batchCompleteSignal *objp, struct LSFHeader *hdr);
extern bool_t xdr_batchStatusReq(XDR *xdrs, struct batchStatusReq *objp, struct LSFHeader *hdr);
extern bool_t xdr_batchStatusReply(XDR *xdrs, struct batchStatusReply *objp, struct LSFHeader *hdr);
extern bool_t xdr_batchJobResult(XDR *xdrs, struct batchJobResult *objp, struct LSFHeader *hdr);

/* 消息类型定义在daemonout.h中 */

/* 批量状态查询选项 */
#define BATCH_STATUS_DETAILED       0x0001  /* 返回详细结果 */

/* 错误代码定义 */
#define LSBE_BATCH_TOO_MANY_JOBS    0x2001  /* 批量作业数过多 */
#define LSBE_BATCH_INVALID_REQUEST  0x2002  /* 无效的批量请求 */
#define LSBE_BATCH_NOT_FOUND        0x2003  /* 批量不存在 */
#define LSBE_BATCH_STREAM_ERROR     0x2004  /* 流式响应错误 */
#define LSBE_BATCH_TIMEOUT          0x2005  /* 批量处理超时 */

/* 内部辅助函数声明 */
static int send_stream_header(int chfd, struct streamHeader *header);
static int send_batch_reply(int chfd, struct batchSubmitReply *reply, int errorCode);
static int send_batch_status_reply(int chfd, struct batchStatusReply *reply, int errorCode);

#endif /* _MBD_BATCH_H_ */
