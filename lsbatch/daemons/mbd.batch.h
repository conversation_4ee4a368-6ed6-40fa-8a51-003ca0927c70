/**
 * @file mbd.batch.h
 * @brief MBD批量作业处理模块头文件
 * 
 * 定义了批量作业处理相关的数据结构、常量和函数声明
 */

#ifndef _MBD_BATCH_H_
#define _MBD_BATCH_H_

#include <time.h>
#include <sys/types.h>
#include "../lsbatch.h"

/* 批量处理相关常量 - 使用lsbatch.h中的定义 */
#define BATCH_MAX_JOBS_LIMIT        10000   /* 批量作业数上限 */
#define BATCH_CLEANUP_INTERVAL      300     /* 清理间隔（秒） */
#define BATCH_STREAM_TIMEOUT        600     /* 流式响应超时（秒） */
#define BATCH_NONSTREAM_TIMEOUT     3600    /* 非流式响应超时（秒） */

/* 批量提交选项标志 - 使用lsbatch.h中的定义 */
#define BATCH_SUBMIT_FAIL_FAST          0x0002  /* 快速失败模式 */
#define BATCH_SUBMIT_PRIORITY_ORDER     0x0008  /* 按优先级顺序处理 */

/* 流式消息类型 - 使用lsbatch.h中的定义 */
/* 流式消息标志 - 使用lsbatch.h中的定义 */
#define STREAM_FLAG_ERROR           0x0004  /* 错误消息 */

/* 批量作业相关结构定义在lsbatch.h中 */

/* 批量状态查询相关结构已在lsbatch.h中定义 */

/* 函数声明 */

/**
 * @brief 处理批量作业提交请求
 * @param xdrs XDR流
 * @param chfd 客户端channel文件描述符
 * @param from 客户端地址
 * @param hostName 客户端主机名
 * @param reqHdr 请求头
 * @param laddr 本地地址
 * @param auth 认证信息
 * @param schedule 调度标志
 * @param dispatch 分发标志
 * @return 0成功，-1失败
 */
extern int do_batchSubmitReq(XDR *xdrs,
                             int chfd,
                             struct sockaddr_in *from,
                             char *hostName,
                             struct LSFHeader *reqHdr,
                             struct sockaddr_in *laddr,
                             struct lsfAuth *auth,
                             int *schedule,
                             int dispatch);

/**
 * @brief 处理批量状态查询请求
 * @param xdrs XDR流
 * @param chfd 客户端channel文件描述符
 * @param from 客户端地址
 * @param hostName 客户端主机名
 * @param reqHdr 请求头
 * @param auth 认证信息
 * @return 0成功，-1失败
 */
extern int do_batchStatusReq(XDR *xdrs,
                            int chfd,
                            struct sockaddr_in *from,
                            char *hostName,
                            struct LSFHeader *reqHdr,
                            struct lsfAuth *auth);

/**
 * @brief 批量管理器清理函数
 * 定期清理已完成的批量管理器
 */
extern void batch_manager_cleanup(void);

/**
 * @brief 获取批量管理器统计信息
 * @param totalBatches 总批量数（输出）
 * @param activeBatches 活跃批量数（输出）
 * @param totalJobs 总作业数（输出）
 */
extern void batch_manager_statistics(int *totalBatches, int *activeBatches, int *totalJobs);

/**
 * @brief 初始化批量处理模块
 * @return 0成功，-1失败
 */
extern int batch_module_init(void);

/**
 * @brief 清理批量处理模块
 */
extern void batch_module_cleanup(void);

/**
 * @brief 处理作业状态变化通知
 * 当作业状态发生变化时，更新相关的批量管理器
 * @param jobData 作业数据
 * @param oldStatus 旧状态
 * @param newStatus 新状态
 */
extern void batch_job_status_update(struct jData *jobData, int oldStatus, int newStatus);

/**
 * @brief 获取批量作业的详细信息
 * @param batchId 批量ID
 * @param jobResults 输出的作业结果数组
 * @param jobCount 输出的作业数量
 * @return 0成功，-1失败
 */
extern int batch_get_job_details(int batchId, struct batchJobResult **jobResults, int *jobCount);

/* XDR函数声明在lsb.xdr.h中 */

/* 消息类型定义在daemonout.h中 */

/* 批量状态查询选项 */
#define BATCH_STATUS_DETAILED       0x0001  /* 返回详细结果 */

/* 错误代码定义 */
#define LSBE_BATCH_TOO_MANY_JOBS    0x2001  /* 批量作业数过多 */
#define LSBE_BATCH_INVALID_REQUEST  0x2002  /* 无效的批量请求 */
#define LSBE_BATCH_NOT_FOUND        0x2003  /* 批量不存在 */
#define LSBE_BATCH_STREAM_ERROR     0x2004  /* 流式响应错误 */
#define LSBE_BATCH_TIMEOUT          0x2005  /* 批量处理超时 */

/* 内部辅助函数在mbd.batch.c中定义，不需要在头文件中声明 */

#endif /* _MBD_BATCH_H_ */
