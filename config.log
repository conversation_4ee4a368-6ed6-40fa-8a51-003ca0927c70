This file contains any messages produced by compilers while
running configure, to aid debugging if configure makes a mistake.

It was created by volclava configure 2.0, which was
generated by GNU Autoconf 2.69.  Invocation command line was

  $ ./configure 

## --------- ##
## Platform. ##
## --------- ##

hostname = master-test
uname -m = x86_64
uname -r = 5.15.0-139-generic
uname -s = Linux
uname -v = #149~20.04.1-Ubuntu SMP Wed Apr 16 08:29:56 UTC 2025

/usr/bin/uname -p = x86_64
/bin/uname -X     = unknown

/bin/arch              = x86_64
/usr/bin/arch -k       = unknown
/usr/convex/getsysinfo = unknown
/usr/bin/hostinfo      = unknown
/bin/machine           = unknown
/usr/bin/oslevel       = unknown
/bin/universe          = unknown

PATH: /root/.vscode-server/cli/servers/Stable-c306e94f98122556ca081f527b466015e1bc37b0/server/bin/remote-cli
PATH: /root/.cargo/bin
PATH: /opt/ros/galactic/bin
PATH: /software/volclava-1.0/bin
PATH: /usr/local/sbin
PATH: /usr/local/bin
PATH: /usr/sbin
PATH: /usr/bin
PATH: /sbin
PATH: /bin
PATH: /usr/games
PATH: /usr/local/games
PATH: /snap/bin
PATH: /opt/volclava/bin
PATH: /opt/volclava/bin


## ----------- ##
## Core tests. ##
## ----------- ##

configure:2169: checking build system type
configure:2183: result: x86_64-pc-linux-gnu
configure:2203: checking host system type
configure:2216: result: x86_64-pc-linux-gnu
configure:2236: checking target system type
configure:2249: result: x86_64-pc-linux-gnu
configure:2314: checking for a BSD-compatible install
configure:2382: result: /usr/bin/install -c
configure:2393: checking whether build environment is sane
configure:2448: result: yes
configure:2597: checking for a thread-safe mkdir -p
configure:2636: result: /usr/bin/mkdir -p
configure:2643: checking for gawk
configure:2673: result: no
configure:2643: checking for mawk
configure:2659: found /usr/bin/mawk
configure:2670: result: mawk
configure:2681: checking whether make sets $(MAKE)
configure:2703: result: yes
configure:2732: checking whether make supports nested variables
configure:2749: result: yes
configure:2898: checking whether make supports nested variables
configure:2915: result: yes
configure:2976: checking for gcc
configure:2992: found /usr/bin/gcc
configure:3003: result: gcc
configure:3232: checking for C compiler version
configure:3241: gcc --version >&5
gcc (Ubuntu 9.4.0-1ubuntu1~20.04.2) 9.4.0
Copyright (C) 2019 Free Software Foundation, Inc.
This is free software; see the source for copying conditions.  There is NO
warranty; not even for MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.

configure:3252: $? = 0
configure:3241: gcc -v >&5
Using built-in specs.
COLLECT_GCC=gcc
COLLECT_LTO_WRAPPER=/usr/lib/gcc/x86_64-linux-gnu/9/lto-wrapper
OFFLOAD_TARGET_NAMES=nvptx-none:hsa
OFFLOAD_TARGET_DEFAULT=1
Target: x86_64-linux-gnu
Configured with: ../src/configure -v --with-pkgversion='Ubuntu 9.4.0-1ubuntu1~20.04.2' --with-bugurl=file:///usr/share/doc/gcc-9/README.Bugs --enable-languages=c,ada,c++,go,brig,d,fortran,objc,obj-c++,gm2 --prefix=/usr --with-gcc-major-version-only --program-suffix=-9 --program-prefix=x86_64-linux-gnu- --enable-shared --enable-linker-build-id --libexecdir=/usr/lib --without-included-gettext --enable-threads=posix --libdir=/usr/lib --enable-nls --enable-clocale=gnu --enable-libstdcxx-debug --enable-libstdcxx-time=yes --with-default-libstdcxx-abi=new --enable-gnu-unique-object --disable-vtable-verify --enable-plugin --enable-default-pie --with-system-zlib --with-target-system-zlib=auto --enable-objc-gc=auto --enable-multiarch --disable-werror --with-arch-32=i686 --with-abi=m64 --with-multilib-list=m32,m64,mx32 --enable-multilib --with-tune=generic --enable-offload-targets=nvptx-none=/build/gcc-9-9QDOt0/gcc-9-9.4.0/debian/tmp-nvptx/usr,hsa --without-cuda-driver --enable-checking=release --build=x86_64-linux-gnu --host=x86_64-linux-gnu --target=x86_64-linux-gnu
Thread model: posix
gcc version 9.4.0 (Ubuntu 9.4.0-1ubuntu1~20.04.2) 
configure:3252: $? = 0
configure:3241: gcc -V >&5
gcc: error: unrecognized command line option '-V'
gcc: fatal error: no input files
compilation terminated.
configure:3252: $? = 1
configure:3241: gcc -qversion >&5
gcc: error: unrecognized command line option '-qversion'; did you mean '--version'?
gcc: fatal error: no input files
compilation terminated.
configure:3252: $? = 1
configure:3272: checking whether the C compiler works
configure:3294: gcc    conftest.c  >&5
configure:3298: $? = 0
configure:3346: result: yes
configure:3349: checking for C compiler default output file name
configure:3351: result: a.out
configure:3357: checking for suffix of executables
configure:3364: gcc -o conftest    conftest.c  >&5
configure:3368: $? = 0
configure:3390: result: 
configure:3412: checking whether we are cross compiling
configure:3420: gcc -o conftest    conftest.c  >&5
configure:3424: $? = 0
configure:3431: ./conftest
configure:3435: $? = 0
configure:3423: result: no
configure:3428: checking for suffix of object files
configure:3450: gcc -c   conftest.c >&5
configure:3454: $? = 0
configure:3475: result: o
configure:3479: checking whether we are using the GNU C compiler
configure:3498: gcc -c   conftest.c >&5
configure:3498: $? = 0
configure:3507: result: yes
configure:3516: checking whether gcc accepts -g
configure:3536: gcc -c -g  conftest.c >&5
configure:3536: $? = 0
configure:3577: result: yes
configure:3594: checking for gcc option to accept ISO C89
configure:3657: gcc  -c -g -O2  conftest.c >&5
configure:3657: $? = 0
configure:3670: result: none needed
configure:3695: checking whether gcc understands -c and -o together
configure:3717: gcc -c conftest.c -o conftest2.o
configure:3720: $? = 0
configure:3717: gcc -c conftest.c -o conftest2.o
configure:3720: $? = 0
configure:3732: result: yes
configure:3752: checking whether make supports the include directive
configure:3767: make -f confmf.GNU && cat confinc.out
make: Warning: File 'confinc.mk' has modification time 1.4 s in the future
make: warning:  Clock skew detected.  Your build may be incomplete.
this is the am__doit target
configure:3770: $? = 0
configure:3789: result: yes (GNU style)
configure:3814: checking dependency style of gcc
configure:3925: result: gcc3
configure:3984: checking for ranlib
configure:4000: found /usr/bin/ranlib
configure:4011: result: ranlib
configure:4037: checking for bison
configure:4067: result: no
configure:4037: checking for byacc
configure:4067: result: no
configure:4081: checking for flex
configure:4111: result: no
configure:4081: checking for lex
configure:4111: result: no
configure:4236: checking whether ln -s works
configure:4243: result: no, using cp -pR
configure:4247: checking whether make sets $(MAKE)
configure:4269: result: yes
configure:4293: checking how to run the C preprocessor
configure:4324: gcc -E  conftest.c
configure:4324: $? = 0
configure:4338: gcc -E  conftest.c
conftest.c:11:10: fatal error: ac_nonexistent.h: No such file or directory
   11 | #include <ac_nonexistent.h>
      |          ^~~~~~~~~~~~~~~~~~
compilation terminated.
configure:4338: $? = 1
configure: failed program was:
| /* confdefs.h */
| #define PACKAGE_NAME "volclava"
| #define PACKAGE_TARNAME "volclava"
| #define PACKAGE_VERSION "2.0"
| #define PACKAGE_STRING "volclava 2.0"
| #define PACKAGE_BUGREPORT ""
| #define PACKAGE_URL ""
| #define PACKAGE "volclava"
| #define VERSION "2.0"
| /* end confdefs.h.  */
| #include <ac_nonexistent.h>
configure:4363: result: gcc -E
configure:4383: gcc -E  conftest.c
configure:4383: $? = 0
configure:4397: gcc -E  conftest.c
conftest.c:11:10: fatal error: ac_nonexistent.h: No such file or directory
   11 | #include <ac_nonexistent.h>
      |          ^~~~~~~~~~~~~~~~~~
compilation terminated.
configure:4397: $? = 1
configure: failed program was:
| /* confdefs.h */
| #define PACKAGE_NAME "volclava"
| #define PACKAGE_TARNAME "volclava"
| #define PACKAGE_VERSION "2.0"
| #define PACKAGE_STRING "volclava 2.0"
| #define PACKAGE_BUGREPORT ""
| #define PACKAGE_URL ""
| #define PACKAGE "volclava"
| #define VERSION "2.0"
| /* end confdefs.h.  */
| #include <ac_nonexistent.h>
configure:4426: checking for grep that handles long lines and -e
configure:4484: result: /usr/bin/grep
configure:4489: checking for egrep
configure:4551: result: /usr/bin/grep -E
configure:4556: checking for ANSI C header files
configure:4576: gcc -c -g -O2 -Wall -fPIC -Wno-error=format-security -I/usr/include/tirpc  conftest.c >&5
configure:4576: $? = 0
configure:4649: gcc -o conftest -g -O2 -Wall -fPIC -Wno-error=format-security -I/usr/include/tirpc   conftest.c  >&5
configure:4649: $? = 0
configure:4649: ./conftest
configure:4649: $? = 0
configure:4660: result: yes
configure:4673: checking for sys/types.h
configure:4673: gcc -c -g -O2 -Wall -fPIC -Wno-error=format-security -I/usr/include/tirpc  conftest.c >&5
configure:4673: $? = 0
configure:4673: result: yes
configure:4673: checking for sys/stat.h
configure:4673: gcc -c -g -O2 -Wall -fPIC -Wno-error=format-security -I/usr/include/tirpc  conftest.c >&5
configure:4673: $? = 0
configure:4673: result: yes
configure:4673: checking for stdlib.h
configure:4673: gcc -c -g -O2 -Wall -fPIC -Wno-error=format-security -I/usr/include/tirpc  conftest.c >&5
configure:4673: $? = 0
configure:4673: result: yes
configure:4673: checking for string.h
configure:4673: gcc -c -g -O2 -Wall -fPIC -Wno-error=format-security -I/usr/include/tirpc  conftest.c >&5
configure:4673: $? = 0
configure:4673: result: yes
configure:4673: checking for memory.h
configure:4673: gcc -c -g -O2 -Wall -fPIC -Wno-error=format-security -I/usr/include/tirpc  conftest.c >&5
configure:4673: $? = 0
configure:4673: result: yes
configure:4673: checking for strings.h
configure:4673: gcc -c -g -O2 -Wall -fPIC -Wno-error=format-security -I/usr/include/tirpc  conftest.c >&5
configure:4673: $? = 0
configure:4673: result: yes
configure:4673: checking for inttypes.h
configure:4673: gcc -c -g -O2 -Wall -fPIC -Wno-error=format-security -I/usr/include/tirpc  conftest.c >&5
configure:4673: $? = 0
configure:4673: result: yes
configure:4673: checking for stdint.h
configure:4673: gcc -c -g -O2 -Wall -fPIC -Wno-error=format-security -I/usr/include/tirpc  conftest.c >&5
configure:4673: $? = 0
configure:4673: result: yes
configure:4673: checking for unistd.h
configure:4673: gcc -c -g -O2 -Wall -fPIC -Wno-error=format-security -I/usr/include/tirpc  conftest.c >&5
configure:4673: $? = 0
configure:4673: result: yes
configure:4687: checking rpc/xdr.h usability
configure:4687: gcc -c -g -O2 -Wall -fPIC -Wno-error=format-security -I/usr/include/tirpc  conftest.c >&5
configure:4687: $? = 0
configure:4687: result: yes
configure:4687: checking rpc/xdr.h presence
configure:4687: gcc -E  conftest.c
configure:4687: $? = 0
configure:4687: result: yes
configure:4687: checking for rpc/xdr.h
configure:4687: result: yes
configure:4712: checking for library containing xdr_int
configure:4743: gcc -o conftest -g -O2 -Wall -fPIC -Wno-error=format-security -I/usr/include/tirpc   conftest.c  >&5
configure:4743: $? = 0
configure:4760: result: none required
configure:4775: checking tcl.h usability
configure:4775: gcc -c -g -O2 -Wall -fPIC -Wno-error=format-security -I/usr/include/tirpc  conftest.c >&5
conftest.c:55:10: fatal error: tcl.h: No such file or directory
   55 | #include <tcl.h>
      |          ^~~~~~~
compilation terminated.
configure:4775: $? = 1
configure: failed program was:
| /* confdefs.h */
| #define PACKAGE_NAME "volclava"
| #define PACKAGE_TARNAME "volclava"
| #define PACKAGE_VERSION "2.0"
| #define PACKAGE_STRING "volclava 2.0"
| #define PACKAGE_BUGREPORT ""
| #define PACKAGE_URL ""
| #define PACKAGE "volclava"
| #define VERSION "2.0"
| #define STDC_HEADERS 1
| #define HAVE_SYS_TYPES_H 1
| #define HAVE_SYS_STAT_H 1
| #define HAVE_STDLIB_H 1
| #define HAVE_STRING_H 1
| #define HAVE_MEMORY_H 1
| #define HAVE_STRINGS_H 1
| #define HAVE_INTTYPES_H 1
| #define HAVE_STDINT_H 1
| #define HAVE_UNISTD_H 1
| #define HAVE_RPC_XDR_H 1
| /* end confdefs.h.  */
| #include <stdio.h>
| #ifdef HAVE_SYS_TYPES_H
| # include <sys/types.h>
| #endif
| #ifdef HAVE_SYS_STAT_H
| # include <sys/stat.h>
| #endif
| #ifdef STDC_HEADERS
| # include <stdlib.h>
| # include <stddef.h>
| #else
| # ifdef HAVE_STDLIB_H
| #  include <stdlib.h>
| # endif
| #endif
| #ifdef HAVE_STRING_H
| # if !defined STDC_HEADERS && defined HAVE_MEMORY_H
| #  include <memory.h>
| # endif
| # include <string.h>
| #endif
| #ifdef HAVE_STRINGS_H
| # include <strings.h>
| #endif
| #ifdef HAVE_INTTYPES_H
| # include <inttypes.h>
| #endif
| #ifdef HAVE_STDINT_H
| # include <stdint.h>
| #endif
| #ifdef HAVE_UNISTD_H
| # include <unistd.h>
| #endif
| #include <tcl.h>
configure:4775: result: no
configure:4775: checking tcl.h presence
configure:4775: gcc -E  conftest.c
conftest.c:22:10: fatal error: tcl.h: No such file or directory
   22 | #include <tcl.h>
      |          ^~~~~~~
compilation terminated.
configure:4775: $? = 1
configure: failed program was:
| /* confdefs.h */
| #define PACKAGE_NAME "volclava"
| #define PACKAGE_TARNAME "volclava"
| #define PACKAGE_VERSION "2.0"
| #define PACKAGE_STRING "volclava 2.0"
| #define PACKAGE_BUGREPORT ""
| #define PACKAGE_URL ""
| #define PACKAGE "volclava"
| #define VERSION "2.0"
| #define STDC_HEADERS 1
| #define HAVE_SYS_TYPES_H 1
| #define HAVE_SYS_STAT_H 1
| #define HAVE_STDLIB_H 1
| #define HAVE_STRING_H 1
| #define HAVE_MEMORY_H 1
| #define HAVE_STRINGS_H 1
| #define HAVE_INTTYPES_H 1
| #define HAVE_STDINT_H 1
| #define HAVE_UNISTD_H 1
| #define HAVE_RPC_XDR_H 1
| /* end confdefs.h.  */
| #include <tcl.h>
configure:4775: result: no
configure:4775: checking for tcl.h
configure:4775: result: no
configure:4784: checking tcl/tcl.h usability
configure:4784: gcc -c -g -O2 -Wall -fPIC -Wno-error=format-security -I/usr/include/tirpc  conftest.c >&5
configure:4784: $? = 0
configure:4784: result: yes
configure:4784: checking tcl/tcl.h presence
configure:4784: gcc -E  conftest.c
configure:4784: $? = 0
configure:4784: result: yes
configure:4784: checking for tcl/tcl.h
configure:4784: result: yes
configure:4800: checking for Tcl_CreateInterp in -ltcl
configure:4825: gcc -o conftest -g -O2 -Wall -fPIC -Wno-error=format-security -I/usr/include/tirpc   conftest.c -ltcl   >&5
configure:4825: $? = 0
configure:4834: result: yes
configure:5291: checking that generated files are newer than configure
configure:5297: result: done
configure:5324: creating ./config.status

## ---------------------- ##
## Running config.status. ##
## ---------------------- ##

This file was extended by volclava config.status 2.0, which was
generated by GNU Autoconf 2.69.  Invocation command line was

  CONFIG_FILES    = 
  CONFIG_HEADERS  = 
  CONFIG_LINKS    = 
  CONFIG_COMMANDS = 
  $ ./config.status 

on master-test

config.status:914: creating Makefile
config.status:914: creating config/Makefile
config.status:914: creating lsf/Makefile
config.status:914: creating lsf/intlib/Makefile
config.status:914: creating lsf/lib/Makefile
config.status:914: creating lsf/lim/Makefile
config.status:914: creating lsf/res/Makefile
config.status:914: creating lsf/pim/Makefile
config.status:914: creating lsf/lstools/Makefile
config.status:914: creating lsf/lsadm/Makefile
config.status:914: creating lsf/man/Makefile
config.status:914: creating lsf/man/man1/Makefile
config.status:914: creating lsf/man/man5/Makefile
config.status:914: creating lsf/man/man8/Makefile
config.status:914: creating lsbatch/Makefile
config.status:914: creating lsbatch/lib/Makefile
config.status:914: creating lsbatch/cmd/Makefile
config.status:914: creating lsbatch/bhist/Makefile
config.status:914: creating lsbatch/daemons/Makefile
config.status:914: creating lsbatch/man1/Makefile
config.status:914: creating lsbatch/man5/Makefile
config.status:914: creating lsbatch/man8/Makefile
config.status:914: creating eauth/Makefile
config.status:914: creating scripts/Makefile
config.status:914: creating chkpnt/Makefile
config.status:914: creating config/lsf.conf
config.status:914: creating config/lsb.hosts
config.status:914: creating config/lsb.params
config.status:914: creating config/lsb.queues
config.status:914: creating config/lsb.users
config.status:914: creating config/lsf.cluster.volclava
config.status:914: creating config/lsf.shared
config.status:914: creating config/lsf.task
config.status:914: creating config/volclava.csh
config.status:914: creating config/volclava
config.status:914: creating config/volclava.setup
config.status:914: creating config/volclava.sh
config.status:914: creating config.h
config.status:1095: config.h is unchanged
config.status:1143: executing depfiles commands
config.status:1220: cd lsf/intlib       && sed -e '/# am--include-marker/d' Makefile         | make -f - am--depfiles
make: Nothing to be done for 'am--depfiles'.
config.status:1225: $? = 0
config.status:1220: cd lsf/lib       && sed -e '/# am--include-marker/d' Makefile         | make -f - am--depfiles
make: Nothing to be done for 'am--depfiles'.
config.status:1225: $? = 0
config.status:1220: cd lsf/lim       && sed -e '/# am--include-marker/d' Makefile         | make -f - am--depfiles
make: Nothing to be done for 'am--depfiles'.
config.status:1225: $? = 0
config.status:1220: cd lsf/res       && sed -e '/# am--include-marker/d' Makefile         | make -f - am--depfiles
make: Nothing to be done for 'am--depfiles'.
config.status:1225: $? = 0
config.status:1220: cd lsf/pim       && sed -e '/# am--include-marker/d' Makefile         | make -f - am--depfiles
make: Nothing to be done for 'am--depfiles'.
config.status:1225: $? = 0
config.status:1220: cd lsf/lstools       && sed -e '/# am--include-marker/d' Makefile         | make -f - am--depfiles
make: Nothing to be done for 'am--depfiles'.
config.status:1225: $? = 0
config.status:1220: cd lsf/lsadm       && sed -e '/# am--include-marker/d' Makefile         | make -f - am--depfiles
make: Nothing to be done for 'am--depfiles'.
config.status:1225: $? = 0
config.status:1220: cd lsbatch/lib       && sed -e '/# am--include-marker/d' Makefile         | make -f - am--depfiles
make: Nothing to be done for 'am--depfiles'.
config.status:1225: $? = 0
config.status:1220: cd lsbatch/cmd       && sed -e '/# am--include-marker/d' Makefile         | make -f - am--depfiles
make: Nothing to be done for 'am--depfiles'.
config.status:1225: $? = 0
config.status:1220: cd lsbatch/bhist       && sed -e '/# am--include-marker/d' Makefile         | make -f - am--depfiles
make: Nothing to be done for 'am--depfiles'.
config.status:1225: $? = 0
config.status:1220: cd lsbatch/daemons       && sed -e '/# am--include-marker/d' Makefile         | make -f - am--depfiles
make: Nothing to be done for 'am--depfiles'.
config.status:1225: $? = 0
config.status:1220: cd eauth       && sed -e '/# am--include-marker/d' Makefile         | make -f - am--depfiles
make: Nothing to be done for 'am--depfiles'.
config.status:1225: $? = 0
config.status:1220: cd chkpnt       && sed -e '/# am--include-marker/d' Makefile         | make -f - am--depfiles
make: Nothing to be done for 'am--depfiles'.
config.status:1225: $? = 0

## ---------------- ##
## Cache variables. ##
## ---------------- ##

ac_cv_build=x86_64-pc-linux-gnu
ac_cv_c_compiler_gnu=yes
ac_cv_env_CC_set=
ac_cv_env_CC_value=
ac_cv_env_CFLAGS_set=
ac_cv_env_CFLAGS_value=
ac_cv_env_CPPFLAGS_set=
ac_cv_env_CPPFLAGS_value=
ac_cv_env_CPP_set=
ac_cv_env_CPP_value=
ac_cv_env_LDFLAGS_set=
ac_cv_env_LDFLAGS_value=
ac_cv_env_LIBS_set=
ac_cv_env_LIBS_value=
ac_cv_env_YACC_set=
ac_cv_env_YACC_value=
ac_cv_env_YFLAGS_set=
ac_cv_env_YFLAGS_value=
ac_cv_env_build_alias_set=
ac_cv_env_build_alias_value=
ac_cv_env_host_alias_set=
ac_cv_env_host_alias_value=
ac_cv_env_target_alias_set=
ac_cv_env_target_alias_value=
ac_cv_env_volclavaadmin_set=
ac_cv_env_volclavaadmin_value=
ac_cv_env_volclavacluster_set=
ac_cv_env_volclavacluster_value=
ac_cv_header_inttypes_h=yes
ac_cv_header_memory_h=yes
ac_cv_header_rpc_xdr_h=yes
ac_cv_header_stdc=yes
ac_cv_header_stdint_h=yes
ac_cv_header_stdlib_h=yes
ac_cv_header_string_h=yes
ac_cv_header_strings_h=yes
ac_cv_header_sys_stat_h=yes
ac_cv_header_sys_types_h=yes
ac_cv_header_tcl_h=no
ac_cv_header_tcl_tcl_h=yes
ac_cv_header_unistd_h=yes
ac_cv_host=x86_64-pc-linux-gnu
ac_cv_lib_tcl_Tcl_CreateInterp=yes
ac_cv_objext=o
ac_cv_path_EGREP='/usr/bin/grep -E'
ac_cv_path_GREP=/usr/bin/grep
ac_cv_path_install='/usr/bin/install -c'
ac_cv_path_mkdir=/usr/bin/mkdir
ac_cv_prog_AWK=mawk
ac_cv_prog_CPP='gcc -E'
ac_cv_prog_ac_ct_CC=gcc
ac_cv_prog_ac_ct_RANLIB=ranlib
ac_cv_prog_cc_c89=
ac_cv_prog_cc_g=yes
ac_cv_prog_make_make_set=yes
ac_cv_search_xdr_int='none required'
ac_cv_target=x86_64-pc-linux-gnu
am_cv_CC_dependencies_compiler_type=gcc3
am_cv_make_support_nested_variables=yes
am_cv_prog_cc_c_o=yes

## ----------------- ##
## Output variables. ##
## ----------------- ##

ACLOCAL='${SHELL} /mnt/hgfs/ubuntu_share/volclava/missing aclocal-1.16'
AMDEPBACKSLASH='\'
AMDEP_FALSE='#'
AMDEP_TRUE=''
AMTAR='$${TAR-tar}'
AM_BACKSLASH='\'
AM_DEFAULT_V='$(AM_DEFAULT_VERBOSITY)'
AM_DEFAULT_VERBOSITY='0'
AM_V='$(V)'
AUTOCONF='${SHELL} /mnt/hgfs/ubuntu_share/volclava/missing autoconf'
AUTOHEADER='${SHELL} /mnt/hgfs/ubuntu_share/volclava/missing autoheader'
AUTOMAKE='${SHELL} /mnt/hgfs/ubuntu_share/volclava/missing automake-1.16'
AWK='mawk'
CC='gcc'
CCDEPMODE='depmode=gcc3'
CFLAGS='-g -O2 -Wall -fPIC -Wno-error=format-security -I/usr/include/tirpc'
CPP='gcc -E'
CPPFLAGS=''
CYGPATH_W='echo'
CYGWIN_FALSE=''
CYGWIN_TRUE='#'
DEFS='-DHAVE_CONFIG_H'
DEPDIR='.deps'
ECHO_C=''
ECHO_N='-n'
ECHO_T=''
EGREP='/usr/bin/grep -E'
EXEEXT=''
GREP='/usr/bin/grep'
INSTALL_DATA='${INSTALL} -m 644'
INSTALL_PROGRAM='${INSTALL}'
INSTALL_SCRIPT='${INSTALL}'
INSTALL_STRIP_PROGRAM='$(install_sh) -c -s'
LDFLAGS=''
LEX=':'
LEXLIB=''
LEX_OUTPUT_ROOT=''
LIBOBJS=''
LIBS='-ltcl '
LN_S='cp -pR'
LTLIBOBJS=''
MAKEINFO='${SHELL} /mnt/hgfs/ubuntu_share/volclava/missing makeinfo'
MKDIR_P='/usr/bin/mkdir -p'
OBJEXT='o'
PACKAGE='volclava'
PACKAGE_BUGREPORT=''
PACKAGE_NAME='volclava'
PACKAGE_STRING='volclava 2.0'
PACKAGE_TARNAME='volclava'
PACKAGE_URL=''
PACKAGE_VERSION='2.0'
PATH_SEPARATOR=':'
RANLIB='ranlib'
SCHED_EXPERIMENTAL_FALSE=''
SCHED_EXPERIMENTAL_TRUE='#'
SET_MAKE=''
SHELL='/bin/bash'
STRIP=''
VERSION='2.0'
YACC='yacc'
YFLAGS=''
ac_ct_CC='gcc'
am__EXEEXT_FALSE=''
am__EXEEXT_TRUE='#'
am__fastdepCC_FALSE='#'
am__fastdepCC_TRUE=''
am__include='include'
am__isrc=''
am__leading_dot='.'
am__nodep='_no'
am__quote=''
am__tar='$${TAR-tar} chof - "$$tardir"'
am__untar='$${TAR-tar} xf -'
bindir='${exec_prefix}/bin'
build='x86_64-pc-linux-gnu'
build_alias=''
build_cpu='x86_64'
build_os='linux-gnu'
build_vendor='pc'
datadir='${datarootdir}'
datarootdir='${prefix}/share'
docdir='${datarootdir}/doc/${PACKAGE_TARNAME}'
dvidir='${docdir}'
exec_prefix='${prefix}'
host='x86_64-pc-linux-gnu'
host_alias=''
host_cpu='x86_64'
host_os='linux-gnu'
host_vendor='pc'
htmldir='${docdir}'
includedir='${prefix}/include'
infodir='${datarootdir}/info'
install_sh='${SHELL} /mnt/hgfs/ubuntu_share/volclava/install-sh'
libdir='${exec_prefix}/lib'
libexecdir='${exec_prefix}/libexec'
localedir='${datarootdir}/locale'
localstatedir='${prefix}/var'
mandir='${datarootdir}/man'
mkdir_p='$(MKDIR_P)'
oldincludedir='/usr/include'
pdfdir='${docdir}'
prefix='/opt/volclava-2.0'
program_transform_name='s,x,x,'
psdir='${docdir}'
runstatedir='${localstatedir}/run'
sbindir='${exec_prefix}/sbin'
sharedstatedir='${prefix}/com'
sysconfdir='${prefix}/etc'
target='x86_64-pc-linux-gnu'
target_alias=''
target_cpu='x86_64'
target_os='linux-gnu'
target_vendor='pc'
volclavaadmin=''
volclavacluster='volclava'
volclavadmin='volclava'

## ----------- ##
## confdefs.h. ##
## ----------- ##

/* confdefs.h */
#define PACKAGE_NAME "volclava"
#define PACKAGE_TARNAME "volclava"
#define PACKAGE_VERSION "2.0"
#define PACKAGE_STRING "volclava 2.0"
#define PACKAGE_BUGREPORT ""
#define PACKAGE_URL ""
#define PACKAGE "volclava"
#define VERSION "2.0"
#define STDC_HEADERS 1
#define HAVE_SYS_TYPES_H 1
#define HAVE_SYS_STAT_H 1
#define HAVE_STDLIB_H 1
#define HAVE_STRING_H 1
#define HAVE_MEMORY_H 1
#define HAVE_STRINGS_H 1
#define HAVE_INTTYPES_H 1
#define HAVE_STDINT_H 1
#define HAVE_UNISTD_H 1
#define HAVE_RPC_XDR_H 1
#define HAVE_TCL_TCL_H 1
#define HAVE_LIBTCL 1

configure: exit 0

## ---------------------- ##
## Running config.status. ##
## ---------------------- ##

This file was extended by volclava config.status 2.0, which was
generated by GNU Autoconf 2.69.  Invocation command line was

  CONFIG_FILES    = 
  CONFIG_HEADERS  = 
  CONFIG_LINKS    = 
  CONFIG_COMMANDS = 
  $ ./config.status lsbatch/lib/Makefile depfiles

on master-test

config.status:914: creating lsbatch/lib/Makefile
config.status:1143: executing depfiles commands
config.status:1220: cd lsbatch/lib       && sed -e '/# am--include-marker/d' Makefile         | make -f - am--depfiles
make[1]: Entering directory '/mnt/hgfs/ubuntu_share/volclava/lsbatch/lib'
make[1]: Leaving directory '/mnt/hgfs/ubuntu_share/volclava/lsbatch/lib'
config.status:1225: $? = 0
