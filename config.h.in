/* config.h.in.  Generated from configure.ac by autoheader.  */

/* Define to 1 if you have the <inttypes.h> header file. */
#undef HAVE_INTTYPES_H

/* Define to 1 if you have the `tcl' library (-ltcl). */
#undef HAVE_LIBTCL

/* Define to 1 if you have the `tcl8.0' library (-ltcl8.0). */
#undef HAVE_LIBTCL8_0

/* Define to 1 if you have the `tcl8.1' library (-ltcl8.1). */
#undef HAVE_LIBTCL8_1

/* Define to 1 if you have the `tcl8.2' library (-ltcl8.2). */
#undef HAVE_LIBTCL8_2

/* Define to 1 if you have the `tcl8.3' library (-ltcl8.3). */
#undef HAVE_LIBTCL8_3

/* Define to 1 if you have the `tcl8.4' library (-ltcl8.4). */
#undef HAVE_LIBTCL8_4

/* Define to 1 if you have the `tcl8.5' library (-ltcl8.5). */
#undef HAVE_LIBTCL8_5

/* Define to 1 if you have the `tcl8.6' library (-ltcl8.6). */
#undef HAVE_LIBTCL8_6

/* Define to 1 if you have the <memory.h> header file. */
#undef HAVE_MEMORY_H

/* Define to 1 if you have the <rpc/xdr.h> header file. */
#undef HAVE_RPC_XDR_H

/* Define to 1 if you have the <stdint.h> header file. */
#undef HAVE_STDINT_H

/* Define to 1 if you have the <stdlib.h> header file. */
#undef HAVE_STDLIB_H

/* Define to 1 if you have the <strings.h> header file. */
#undef HAVE_STRINGS_H

/* Define to 1 if you have the <string.h> header file. */
#undef HAVE_STRING_H

/* Define to 1 if you have the <sys/stat.h> header file. */
#undef HAVE_SYS_STAT_H

/* Define to 1 if you have the <sys/types.h> header file. */
#undef HAVE_SYS_TYPES_H

/* Define to 1 if you have the <tcl.h> header file. */
#undef HAVE_TCL_H

/* Define to 1 if you have the <tcl/tcl.h> header file. */
#undef HAVE_TCL_TCL_H

/* Define to 1 if you have the <tirpc/rpc/xdr.h> header file. */
#undef HAVE_TIRPC_RPC_XDR_H

/* Define to 1 if you have the <unistd.h> header file. */
#undef HAVE_UNISTD_H

/* Name of package */
#undef PACKAGE

/* Define to the address where bug reports for this package should be sent. */
#undef PACKAGE_BUGREPORT

/* Define to the full name of this package. */
#undef PACKAGE_NAME

/* Define to the full name and version of this package. */
#undef PACKAGE_STRING

/* Define to the one symbol short name of this package. */
#undef PACKAGE_TARNAME

/* Define to the home page for this package. */
#undef PACKAGE_URL

/* Define to the version of this package. */
#undef PACKAGE_VERSION

/* Define to 1 if you have the ANSI C header files. */
#undef STDC_HEADERS

/* Version number of package */
#undef VERSION

/* Define to 1 if `lex' declares `yytext' as a `char *' by default, not a
   `char[]'. */
#undef YYTEXT_POINTER
