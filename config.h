/* config.h.  Generated from config.h.in by configure.  */
/* config.h.in.  Generated from configure.ac by autoheader.  */

/* Define to 1 if you have the <inttypes.h> header file. */
#define HAVE_INTTYPES_H 1

/* Define to 1 if you have the `tcl' library (-ltcl). */
#define HAVE_LIBTCL 1

/* Define to 1 if you have the `tcl8.0' library (-ltcl8.0). */
/* #undef HAVE_LIBTCL8_0 */

/* Define to 1 if you have the `tcl8.1' library (-ltcl8.1). */
/* #undef HAVE_LIBTCL8_1 */

/* Define to 1 if you have the `tcl8.2' library (-ltcl8.2). */
/* #undef HAVE_LIBTCL8_2 */

/* Define to 1 if you have the `tcl8.3' library (-ltcl8.3). */
/* #undef HAVE_LIBTCL8_3 */

/* Define to 1 if you have the `tcl8.4' library (-ltcl8.4). */
/* #undef HAVE_LIBTCL8_4 */

/* Define to 1 if you have the `tcl8.5' library (-ltcl8.5). */
/* #undef HAVE_LIBTCL8_5 */

/* Define to 1 if you have the `tcl8.6' library (-ltcl8.6). */
/* #undef HAVE_LIBTCL8_6 */

/* Define to 1 if you have the <memory.h> header file. */
#define HAVE_MEMORY_H 1

/* Define to 1 if you have the <rpc/xdr.h> header file. */
#define HAVE_RPC_XDR_H 1

/* Define to 1 if you have the <stdint.h> header file. */
#define HAVE_STDINT_H 1

/* Define to 1 if you have the <stdlib.h> header file. */
#define HAVE_STDLIB_H 1

/* Define to 1 if you have the <strings.h> header file. */
#define HAVE_STRINGS_H 1

/* Define to 1 if you have the <string.h> header file. */
#define HAVE_STRING_H 1

/* Define to 1 if you have the <sys/stat.h> header file. */
#define HAVE_SYS_STAT_H 1

/* Define to 1 if you have the <sys/types.h> header file. */
#define HAVE_SYS_TYPES_H 1

/* Define to 1 if you have the <tcl.h> header file. */
/* #undef HAVE_TCL_H */

/* Define to 1 if you have the <tcl/tcl.h> header file. */
#define HAVE_TCL_TCL_H 1

/* Define to 1 if you have the <tirpc/rpc/xdr.h> header file. */
/* #undef HAVE_TIRPC_RPC_XDR_H */

/* Define to 1 if you have the <unistd.h> header file. */
#define HAVE_UNISTD_H 1

/* Name of package */
#define PACKAGE "volclava"

/* Define to the address where bug reports for this package should be sent. */
#define PACKAGE_BUGREPORT ""

/* Define to the full name of this package. */
#define PACKAGE_NAME "volclava"

/* Define to the full name and version of this package. */
#define PACKAGE_STRING "volclava 2.0"

/* Define to the one symbol short name of this package. */
#define PACKAGE_TARNAME "volclava"

/* Define to the home page for this package. */
#define PACKAGE_URL ""

/* Define to the version of this package. */
#define PACKAGE_VERSION "2.0"

/* Define to 1 if you have the ANSI C header files. */
#define STDC_HEADERS 1

/* Version number of package */
#define VERSION "2.0"

/* Define to 1 if `lex' declares `yytext' as a `char *' by default, not a
   `char[]'. */
/* #undef YYTEXT_POINTER */
