lib.tid.o: lib.tid.c /usr/include/stdc-predef.h /usr/include/stdio.h \
 /usr/include/x86_64-linux-gnu/bits/libc-header-start.h \
 /usr/include/features.h /usr/include/x86_64-linux-gnu/sys/cdefs.h \
 /usr/include/x86_64-linux-gnu/bits/wordsize.h \
 /usr/include/x86_64-linux-gnu/bits/long-double.h \
 /usr/include/x86_64-linux-gnu/gnu/stubs.h \
 /usr/include/x86_64-linux-gnu/gnu/stubs-64.h \
 /usr/lib/gcc/x86_64-linux-gnu/9/include/stddef.h \
 /usr/lib/gcc/x86_64-linux-gnu/9/include/stdarg.h \
 /usr/include/x86_64-linux-gnu/bits/types.h \
 /usr/include/x86_64-linux-gnu/bits/timesize.h \
 /usr/include/x86_64-linux-gnu/bits/typesizes.h \
 /usr/include/x86_64-linux-gnu/bits/time64.h \
 /usr/include/x86_64-linux-gnu/bits/types/__fpos_t.h \
 /usr/include/x86_64-linux-gnu/bits/types/__mbstate_t.h \
 /usr/include/x86_64-linux-gnu/bits/types/__fpos64_t.h \
 /usr/include/x86_64-linux-gnu/bits/types/__FILE.h \
 /usr/include/x86_64-linux-gnu/bits/types/FILE.h \
 /usr/include/x86_64-linux-gnu/bits/types/struct_FILE.h \
 /usr/include/x86_64-linux-gnu/bits/stdio_lim.h \
 /usr/include/x86_64-linux-gnu/bits/sys_errlist.h \
 /usr/include/x86_64-linux-gnu/bits/stdio.h \
 /usr/include/x86_64-linux-gnu/bits/stdio2.h /usr/include/malloc.h lib.h \
 ../lsf.h ../../config.h /usr/lib/gcc/x86_64-linux-gnu/9/include/stdint.h \
 /usr/include/stdint.h /usr/include/x86_64-linux-gnu/bits/wchar.h \
 /usr/include/x86_64-linux-gnu/bits/stdint-intn.h \
 /usr/include/x86_64-linux-gnu/bits/stdint-uintn.h /usr/include/errno.h \
 /usr/include/x86_64-linux-gnu/bits/errno.h /usr/include/linux/errno.h \
 /usr/include/x86_64-linux-gnu/asm/errno.h \
 /usr/include/asm-generic/errno.h /usr/include/asm-generic/errno-base.h \
 /usr/include/unistd.h /usr/include/x86_64-linux-gnu/bits/posix_opt.h \
 /usr/include/x86_64-linux-gnu/bits/environments.h \
 /usr/include/x86_64-linux-gnu/bits/confname.h \
 /usr/include/x86_64-linux-gnu/bits/getopt_posix.h \
 /usr/include/x86_64-linux-gnu/bits/getopt_core.h \
 /usr/include/x86_64-linux-gnu/bits/unistd.h \
 /usr/include/x86_64-linux-gnu/bits/unistd_ext.h /usr/include/math.h \
 /usr/include/x86_64-linux-gnu/bits/math-vector.h \
 /usr/include/x86_64-linux-gnu/bits/libm-simd-decl-stubs.h \
 /usr/include/x86_64-linux-gnu/bits/floatn.h \
 /usr/include/x86_64-linux-gnu/bits/floatn-common.h \
 /usr/include/x86_64-linux-gnu/bits/flt-eval-method.h \
 /usr/include/x86_64-linux-gnu/bits/fp-logb.h \
 /usr/include/x86_64-linux-gnu/bits/fp-fast.h \
 /usr/include/x86_64-linux-gnu/bits/mathcalls-helper-functions.h \
 /usr/include/x86_64-linux-gnu/bits/mathcalls.h \
 /usr/include/x86_64-linux-gnu/bits/mathinline.h /usr/include/assert.h \
 /usr/include/syslog.h /usr/include/x86_64-linux-gnu/sys/syslog.h \
 /usr/include/x86_64-linux-gnu/bits/syslog-path.h \
 /usr/include/x86_64-linux-gnu/bits/syslog.h /usr/include/string.h \
 /usr/include/x86_64-linux-gnu/bits/types/locale_t.h \
 /usr/include/x86_64-linux-gnu/bits/types/__locale_t.h \
 /usr/include/strings.h \
 /usr/include/x86_64-linux-gnu/bits/strings_fortified.h \
 /usr/include/x86_64-linux-gnu/bits/string_fortified.h \
 /usr/include/stdlib.h /usr/include/x86_64-linux-gnu/bits/waitflags.h \
 /usr/include/x86_64-linux-gnu/bits/waitstatus.h \
 /usr/include/x86_64-linux-gnu/sys/types.h \
 /usr/include/x86_64-linux-gnu/bits/types/clock_t.h \
 /usr/include/x86_64-linux-gnu/bits/types/clockid_t.h \
 /usr/include/x86_64-linux-gnu/bits/types/time_t.h \
 /usr/include/x86_64-linux-gnu/bits/types/timer_t.h /usr/include/endian.h \
 /usr/include/x86_64-linux-gnu/bits/endian.h \
 /usr/include/x86_64-linux-gnu/bits/endianness.h \
 /usr/include/x86_64-linux-gnu/bits/byteswap.h \
 /usr/include/x86_64-linux-gnu/bits/uintn-identity.h \
 /usr/include/x86_64-linux-gnu/sys/select.h \
 /usr/include/x86_64-linux-gnu/bits/select.h \
 /usr/include/x86_64-linux-gnu/bits/types/sigset_t.h \
 /usr/include/x86_64-linux-gnu/bits/types/__sigset_t.h \
 /usr/include/x86_64-linux-gnu/bits/types/struct_timeval.h \
 /usr/include/x86_64-linux-gnu/bits/types/struct_timespec.h \
 /usr/include/x86_64-linux-gnu/bits/select2.h \
 /usr/include/x86_64-linux-gnu/bits/pthreadtypes.h \
 /usr/include/x86_64-linux-gnu/bits/thread-shared-types.h \
 /usr/include/x86_64-linux-gnu/bits/pthreadtypes-arch.h \
 /usr/include/x86_64-linux-gnu/bits/struct_mutex.h \
 /usr/include/x86_64-linux-gnu/bits/struct_rwlock.h /usr/include/alloca.h \
 /usr/include/x86_64-linux-gnu/bits/stdlib-bsearch.h \
 /usr/include/x86_64-linux-gnu/bits/stdlib-float.h \
 /usr/include/x86_64-linux-gnu/bits/stdlib.h /usr/include/signal.h \
 /usr/include/x86_64-linux-gnu/bits/signum.h \
 /usr/include/x86_64-linux-gnu/bits/signum-generic.h \
 /usr/include/x86_64-linux-gnu/bits/types/sig_atomic_t.h \
 /usr/include/x86_64-linux-gnu/bits/types/siginfo_t.h \
 /usr/include/x86_64-linux-gnu/bits/types/__sigval_t.h \
 /usr/include/x86_64-linux-gnu/bits/siginfo-arch.h \
 /usr/include/x86_64-linux-gnu/bits/siginfo-consts.h \
 /usr/include/x86_64-linux-gnu/bits/types/sigval_t.h \
 /usr/include/x86_64-linux-gnu/bits/types/sigevent_t.h \
 /usr/include/x86_64-linux-gnu/bits/sigevent-consts.h \
 /usr/include/x86_64-linux-gnu/bits/sigaction.h \
 /usr/include/x86_64-linux-gnu/bits/sigcontext.h \
 /usr/include/x86_64-linux-gnu/bits/types/stack_t.h \
 /usr/include/x86_64-linux-gnu/sys/ucontext.h \
 /usr/include/x86_64-linux-gnu/bits/sigstack.h \
 /usr/include/x86_64-linux-gnu/bits/ss_flags.h \
 /usr/include/x86_64-linux-gnu/bits/types/struct_sigstack.h \
 /usr/include/x86_64-linux-gnu/bits/sigthread.h \
 /usr/include/x86_64-linux-gnu/bits/signal_ext.h /usr/include/netdb.h \
 /usr/include/netinet/in.h /usr/include/x86_64-linux-gnu/sys/socket.h \
 /usr/include/x86_64-linux-gnu/bits/types/struct_iovec.h \
 /usr/include/x86_64-linux-gnu/bits/socket.h \
 /usr/include/x86_64-linux-gnu/bits/socket_type.h \
 /usr/include/x86_64-linux-gnu/bits/sockaddr.h \
 /usr/include/x86_64-linux-gnu/asm/socket.h \
 /usr/include/asm-generic/socket.h /usr/include/linux/posix_types.h \
 /usr/include/linux/stddef.h \
 /usr/include/x86_64-linux-gnu/asm/posix_types.h \
 /usr/include/x86_64-linux-gnu/asm/posix_types_64.h \
 /usr/include/asm-generic/posix_types.h \
 /usr/include/x86_64-linux-gnu/asm/bitsperlong.h \
 /usr/include/asm-generic/bitsperlong.h \
 /usr/include/x86_64-linux-gnu/asm/sockios.h \
 /usr/include/asm-generic/sockios.h \
 /usr/include/x86_64-linux-gnu/bits/types/struct_osockaddr.h \
 /usr/include/x86_64-linux-gnu/bits/socket2.h \
 /usr/include/x86_64-linux-gnu/bits/in.h /usr/include/rpc/netdb.h \
 /usr/include/x86_64-linux-gnu/bits/netdb.h /usr/include/fcntl.h \
 /usr/include/x86_64-linux-gnu/bits/fcntl.h \
 /usr/include/x86_64-linux-gnu/bits/fcntl-linux.h \
 /usr/include/x86_64-linux-gnu/bits/stat.h \
 /usr/include/x86_64-linux-gnu/bits/fcntl2.h /usr/include/grp.h \
 /usr/include/ctype.h /usr/include/dirent.h \
 /usr/include/x86_64-linux-gnu/bits/dirent.h \
 /usr/include/x86_64-linux-gnu/bits/posix1_lim.h \
 /usr/include/x86_64-linux-gnu/bits/local_lim.h \
 /usr/include/linux/limits.h \
 /usr/include/x86_64-linux-gnu/bits/dirent_ext.h /usr/include/time.h \
 /usr/include/x86_64-linux-gnu/bits/time.h \
 /usr/include/x86_64-linux-gnu/bits/types/struct_tm.h \
 /usr/include/x86_64-linux-gnu/bits/types/struct_itimerspec.h \
 /usr/include/termios.h /usr/include/x86_64-linux-gnu/bits/termios.h \
 /usr/include/x86_64-linux-gnu/bits/termios-struct.h \
 /usr/include/x86_64-linux-gnu/bits/termios-c_cc.h \
 /usr/include/x86_64-linux-gnu/bits/termios-c_iflag.h \
 /usr/include/x86_64-linux-gnu/bits/termios-c_oflag.h \
 /usr/include/x86_64-linux-gnu/bits/termios-baud.h \
 /usr/include/x86_64-linux-gnu/bits/termios-c_cflag.h \
 /usr/include/x86_64-linux-gnu/bits/termios-c_lflag.h \
 /usr/include/x86_64-linux-gnu/bits/termios-tcflow.h \
 /usr/include/x86_64-linux-gnu/bits/termios-misc.h \
 /usr/include/x86_64-linux-gnu/sys/ttydefaults.h /usr/include/pwd.h \
 /usr/lib/gcc/x86_64-linux-gnu/9/include/limits.h \
 /usr/lib/gcc/x86_64-linux-gnu/9/include/syslimits.h \
 /usr/include/limits.h /usr/include/x86_64-linux-gnu/bits/posix2_lim.h \
 /usr/include/x86_64-linux-gnu/sys/param.h \
 /usr/include/x86_64-linux-gnu/bits/param.h /usr/include/linux/param.h \
 /usr/include/x86_64-linux-gnu/asm/param.h \
 /usr/include/asm-generic/param.h \
 /usr/include/x86_64-linux-gnu/sys/time.h \
 /usr/include/x86_64-linux-gnu/sys/stat.h \
 /usr/include/x86_64-linux-gnu/sys/ioctl.h \
 /usr/include/x86_64-linux-gnu/bits/ioctls.h \
 /usr/include/x86_64-linux-gnu/asm/ioctls.h \
 /usr/include/asm-generic/ioctls.h /usr/include/linux/ioctl.h \
 /usr/include/x86_64-linux-gnu/asm/ioctl.h \
 /usr/include/asm-generic/ioctl.h \
 /usr/include/x86_64-linux-gnu/bits/ioctl-types.h \
 /usr/include/x86_64-linux-gnu/sys/resource.h \
 /usr/include/x86_64-linux-gnu/bits/resource.h \
 /usr/include/x86_64-linux-gnu/bits/types/struct_rusage.h \
 /usr/include/x86_64-linux-gnu/sys/wait.h \
 /usr/include/x86_64-linux-gnu/sys/mman.h \
 /usr/include/x86_64-linux-gnu/bits/mman.h \
 /usr/include/x86_64-linux-gnu/bits/mman-map-flags-generic.h \
 /usr/include/x86_64-linux-gnu/bits/mman-linux.h \
 /usr/include/x86_64-linux-gnu/bits/mman-shared.h \
 /usr/include/arpa/inet.h /usr/include/rpc/types.h /usr/include/rpc/xdr.h \
 /usr/include/rpcsvc/ypclnt.h ../lim/limout.h ../lim/../lib/lib.hdr.h \
 ../lim/../lib/lib.xdrlim.h ../lim/../lib/lib.hdr.h \
 ../lim/../lib/lproto.h ../lim/../lib/../lsf.h ../lim/../lib/lib.table.h \
 ../lim/../lib/lib.channel.h ../lim/../lib/../res/resout.h \
 ../lim/../lib/../res/../lib/lib.hdr.h \
 ../lim/../lib/../res/../lib/lib.xdrres.h \
 ../lim/../lib/../res/../lib/lib.hdr.h \
 ../lim/../lib/../res/../lib/lib.rf.h ../lim/../lib/lib.pim.h \
 ../lim/../lib/lsi18n.h ../res/resout.h lib.hdr.h lib.xdrlim.h lib.xdr.h \
 mls.h lib.queue.h

/usr/include/stdc-predef.h:

/usr/include/stdio.h:

/usr/include/x86_64-linux-gnu/bits/libc-header-start.h:

/usr/include/features.h:

/usr/include/x86_64-linux-gnu/sys/cdefs.h:

/usr/include/x86_64-linux-gnu/bits/wordsize.h:

/usr/include/x86_64-linux-gnu/bits/long-double.h:

/usr/include/x86_64-linux-gnu/gnu/stubs.h:

/usr/include/x86_64-linux-gnu/gnu/stubs-64.h:

/usr/lib/gcc/x86_64-linux-gnu/9/include/stddef.h:

/usr/lib/gcc/x86_64-linux-gnu/9/include/stdarg.h:

/usr/include/x86_64-linux-gnu/bits/types.h:

/usr/include/x86_64-linux-gnu/bits/timesize.h:

/usr/include/x86_64-linux-gnu/bits/typesizes.h:

/usr/include/x86_64-linux-gnu/bits/time64.h:

/usr/include/x86_64-linux-gnu/bits/types/__fpos_t.h:

/usr/include/x86_64-linux-gnu/bits/types/__mbstate_t.h:

/usr/include/x86_64-linux-gnu/bits/types/__fpos64_t.h:

/usr/include/x86_64-linux-gnu/bits/types/__FILE.h:

/usr/include/x86_64-linux-gnu/bits/types/FILE.h:

/usr/include/x86_64-linux-gnu/bits/types/struct_FILE.h:

/usr/include/x86_64-linux-gnu/bits/stdio_lim.h:

/usr/include/x86_64-linux-gnu/bits/sys_errlist.h:

/usr/include/x86_64-linux-gnu/bits/stdio.h:

/usr/include/x86_64-linux-gnu/bits/stdio2.h:

/usr/include/malloc.h:

lib.h:

../lsf.h:

../../config.h:

/usr/lib/gcc/x86_64-linux-gnu/9/include/stdint.h:

/usr/include/stdint.h:

/usr/include/x86_64-linux-gnu/bits/wchar.h:

/usr/include/x86_64-linux-gnu/bits/stdint-intn.h:

/usr/include/x86_64-linux-gnu/bits/stdint-uintn.h:

/usr/include/errno.h:

/usr/include/x86_64-linux-gnu/bits/errno.h:

/usr/include/linux/errno.h:

/usr/include/x86_64-linux-gnu/asm/errno.h:

/usr/include/asm-generic/errno.h:

/usr/include/asm-generic/errno-base.h:

/usr/include/unistd.h:

/usr/include/x86_64-linux-gnu/bits/posix_opt.h:

/usr/include/x86_64-linux-gnu/bits/environments.h:

/usr/include/x86_64-linux-gnu/bits/confname.h:

/usr/include/x86_64-linux-gnu/bits/getopt_posix.h:

/usr/include/x86_64-linux-gnu/bits/getopt_core.h:

/usr/include/x86_64-linux-gnu/bits/unistd.h:

/usr/include/x86_64-linux-gnu/bits/unistd_ext.h:

/usr/include/math.h:

/usr/include/x86_64-linux-gnu/bits/math-vector.h:

/usr/include/x86_64-linux-gnu/bits/libm-simd-decl-stubs.h:

/usr/include/x86_64-linux-gnu/bits/floatn.h:

/usr/include/x86_64-linux-gnu/bits/floatn-common.h:

/usr/include/x86_64-linux-gnu/bits/flt-eval-method.h:

/usr/include/x86_64-linux-gnu/bits/fp-logb.h:

/usr/include/x86_64-linux-gnu/bits/fp-fast.h:

/usr/include/x86_64-linux-gnu/bits/mathcalls-helper-functions.h:

/usr/include/x86_64-linux-gnu/bits/mathcalls.h:

/usr/include/x86_64-linux-gnu/bits/mathinline.h:

/usr/include/assert.h:

/usr/include/syslog.h:

/usr/include/x86_64-linux-gnu/sys/syslog.h:

/usr/include/x86_64-linux-gnu/bits/syslog-path.h:

/usr/include/x86_64-linux-gnu/bits/syslog.h:

/usr/include/string.h:

/usr/include/x86_64-linux-gnu/bits/types/locale_t.h:

/usr/include/x86_64-linux-gnu/bits/types/__locale_t.h:

/usr/include/strings.h:

/usr/include/x86_64-linux-gnu/bits/strings_fortified.h:

/usr/include/x86_64-linux-gnu/bits/string_fortified.h:

/usr/include/stdlib.h:

/usr/include/x86_64-linux-gnu/bits/waitflags.h:

/usr/include/x86_64-linux-gnu/bits/waitstatus.h:

/usr/include/x86_64-linux-gnu/sys/types.h:

/usr/include/x86_64-linux-gnu/bits/types/clock_t.h:

/usr/include/x86_64-linux-gnu/bits/types/clockid_t.h:

/usr/include/x86_64-linux-gnu/bits/types/time_t.h:

/usr/include/x86_64-linux-gnu/bits/types/timer_t.h:

/usr/include/endian.h:

/usr/include/x86_64-linux-gnu/bits/endian.h:

/usr/include/x86_64-linux-gnu/bits/endianness.h:

/usr/include/x86_64-linux-gnu/bits/byteswap.h:

/usr/include/x86_64-linux-gnu/bits/uintn-identity.h:

/usr/include/x86_64-linux-gnu/sys/select.h:

/usr/include/x86_64-linux-gnu/bits/select.h:

/usr/include/x86_64-linux-gnu/bits/types/sigset_t.h:

/usr/include/x86_64-linux-gnu/bits/types/__sigset_t.h:

/usr/include/x86_64-linux-gnu/bits/types/struct_timeval.h:

/usr/include/x86_64-linux-gnu/bits/types/struct_timespec.h:

/usr/include/x86_64-linux-gnu/bits/select2.h:

/usr/include/x86_64-linux-gnu/bits/pthreadtypes.h:

/usr/include/x86_64-linux-gnu/bits/thread-shared-types.h:

/usr/include/x86_64-linux-gnu/bits/pthreadtypes-arch.h:

/usr/include/x86_64-linux-gnu/bits/struct_mutex.h:

/usr/include/x86_64-linux-gnu/bits/struct_rwlock.h:

/usr/include/alloca.h:

/usr/include/x86_64-linux-gnu/bits/stdlib-bsearch.h:

/usr/include/x86_64-linux-gnu/bits/stdlib-float.h:

/usr/include/x86_64-linux-gnu/bits/stdlib.h:

/usr/include/signal.h:

/usr/include/x86_64-linux-gnu/bits/signum.h:

/usr/include/x86_64-linux-gnu/bits/signum-generic.h:

/usr/include/x86_64-linux-gnu/bits/types/sig_atomic_t.h:

/usr/include/x86_64-linux-gnu/bits/types/siginfo_t.h:

/usr/include/x86_64-linux-gnu/bits/types/__sigval_t.h:

/usr/include/x86_64-linux-gnu/bits/siginfo-arch.h:

/usr/include/x86_64-linux-gnu/bits/siginfo-consts.h:

/usr/include/x86_64-linux-gnu/bits/types/sigval_t.h:

/usr/include/x86_64-linux-gnu/bits/types/sigevent_t.h:

/usr/include/x86_64-linux-gnu/bits/sigevent-consts.h:

/usr/include/x86_64-linux-gnu/bits/sigaction.h:

/usr/include/x86_64-linux-gnu/bits/sigcontext.h:

/usr/include/x86_64-linux-gnu/bits/types/stack_t.h:

/usr/include/x86_64-linux-gnu/sys/ucontext.h:

/usr/include/x86_64-linux-gnu/bits/sigstack.h:

/usr/include/x86_64-linux-gnu/bits/ss_flags.h:

/usr/include/x86_64-linux-gnu/bits/types/struct_sigstack.h:

/usr/include/x86_64-linux-gnu/bits/sigthread.h:

/usr/include/x86_64-linux-gnu/bits/signal_ext.h:

/usr/include/netdb.h:

/usr/include/netinet/in.h:

/usr/include/x86_64-linux-gnu/sys/socket.h:

/usr/include/x86_64-linux-gnu/bits/types/struct_iovec.h:

/usr/include/x86_64-linux-gnu/bits/socket.h:

/usr/include/x86_64-linux-gnu/bits/socket_type.h:

/usr/include/x86_64-linux-gnu/bits/sockaddr.h:

/usr/include/x86_64-linux-gnu/asm/socket.h:

/usr/include/asm-generic/socket.h:

/usr/include/linux/posix_types.h:

/usr/include/linux/stddef.h:

/usr/include/x86_64-linux-gnu/asm/posix_types.h:

/usr/include/x86_64-linux-gnu/asm/posix_types_64.h:

/usr/include/asm-generic/posix_types.h:

/usr/include/x86_64-linux-gnu/asm/bitsperlong.h:

/usr/include/asm-generic/bitsperlong.h:

/usr/include/x86_64-linux-gnu/asm/sockios.h:

/usr/include/asm-generic/sockios.h:

/usr/include/x86_64-linux-gnu/bits/types/struct_osockaddr.h:

/usr/include/x86_64-linux-gnu/bits/socket2.h:

/usr/include/x86_64-linux-gnu/bits/in.h:

/usr/include/rpc/netdb.h:

/usr/include/x86_64-linux-gnu/bits/netdb.h:

/usr/include/fcntl.h:

/usr/include/x86_64-linux-gnu/bits/fcntl.h:

/usr/include/x86_64-linux-gnu/bits/fcntl-linux.h:

/usr/include/x86_64-linux-gnu/bits/stat.h:

/usr/include/x86_64-linux-gnu/bits/fcntl2.h:

/usr/include/grp.h:

/usr/include/ctype.h:

/usr/include/dirent.h:

/usr/include/x86_64-linux-gnu/bits/dirent.h:

/usr/include/x86_64-linux-gnu/bits/posix1_lim.h:

/usr/include/x86_64-linux-gnu/bits/local_lim.h:

/usr/include/linux/limits.h:

/usr/include/x86_64-linux-gnu/bits/dirent_ext.h:

/usr/include/time.h:

/usr/include/x86_64-linux-gnu/bits/time.h:

/usr/include/x86_64-linux-gnu/bits/types/struct_tm.h:

/usr/include/x86_64-linux-gnu/bits/types/struct_itimerspec.h:

/usr/include/termios.h:

/usr/include/x86_64-linux-gnu/bits/termios.h:

/usr/include/x86_64-linux-gnu/bits/termios-struct.h:

/usr/include/x86_64-linux-gnu/bits/termios-c_cc.h:

/usr/include/x86_64-linux-gnu/bits/termios-c_iflag.h:

/usr/include/x86_64-linux-gnu/bits/termios-c_oflag.h:

/usr/include/x86_64-linux-gnu/bits/termios-baud.h:

/usr/include/x86_64-linux-gnu/bits/termios-c_cflag.h:

/usr/include/x86_64-linux-gnu/bits/termios-c_lflag.h:

/usr/include/x86_64-linux-gnu/bits/termios-tcflow.h:

/usr/include/x86_64-linux-gnu/bits/termios-misc.h:

/usr/include/x86_64-linux-gnu/sys/ttydefaults.h:

/usr/include/pwd.h:

/usr/lib/gcc/x86_64-linux-gnu/9/include/limits.h:

/usr/lib/gcc/x86_64-linux-gnu/9/include/syslimits.h:

/usr/include/limits.h:

/usr/include/x86_64-linux-gnu/bits/posix2_lim.h:

/usr/include/x86_64-linux-gnu/sys/param.h:

/usr/include/x86_64-linux-gnu/bits/param.h:

/usr/include/linux/param.h:

/usr/include/x86_64-linux-gnu/asm/param.h:

/usr/include/asm-generic/param.h:

/usr/include/x86_64-linux-gnu/sys/time.h:

/usr/include/x86_64-linux-gnu/sys/stat.h:

/usr/include/x86_64-linux-gnu/sys/ioctl.h:

/usr/include/x86_64-linux-gnu/bits/ioctls.h:

/usr/include/x86_64-linux-gnu/asm/ioctls.h:

/usr/include/asm-generic/ioctls.h:

/usr/include/linux/ioctl.h:

/usr/include/x86_64-linux-gnu/asm/ioctl.h:

/usr/include/asm-generic/ioctl.h:

/usr/include/x86_64-linux-gnu/bits/ioctl-types.h:

/usr/include/x86_64-linux-gnu/sys/resource.h:

/usr/include/x86_64-linux-gnu/bits/resource.h:

/usr/include/x86_64-linux-gnu/bits/types/struct_rusage.h:

/usr/include/x86_64-linux-gnu/sys/wait.h:

/usr/include/x86_64-linux-gnu/sys/mman.h:

/usr/include/x86_64-linux-gnu/bits/mman.h:

/usr/include/x86_64-linux-gnu/bits/mman-map-flags-generic.h:

/usr/include/x86_64-linux-gnu/bits/mman-linux.h:

/usr/include/x86_64-linux-gnu/bits/mman-shared.h:

/usr/include/arpa/inet.h:

/usr/include/rpc/types.h:

/usr/include/rpc/xdr.h:

/usr/include/rpcsvc/ypclnt.h:

../lim/limout.h:

../lim/../lib/lib.hdr.h:

../lim/../lib/lib.xdrlim.h:

../lim/../lib/lib.hdr.h:

../lim/../lib/lproto.h:

../lim/../lib/../lsf.h:

../lim/../lib/lib.table.h:

../lim/../lib/lib.channel.h:

../lim/../lib/../res/resout.h:

../lim/../lib/../res/../lib/lib.hdr.h:

../lim/../lib/../res/../lib/lib.xdrres.h:

../lim/../lib/../res/../lib/lib.hdr.h:

../lim/../lib/../res/../lib/lib.rf.h:

../lim/../lib/lib.pim.h:

../lim/../lib/lsi18n.h:

../res/resout.h:

lib.hdr.h:

lib.xdrlim.h:

lib.xdr.h:

mls.h:

lib.queue.h:
