# Makefile.in generated by automake 1.16.1 from Makefile.am.
# @configure_input@

# Copyright (C) 1994-2018 Free Software Foundation, Inc.

# This Makefile.in is free software; the Free Software Foundation
# gives unlimited permission to copy and/or distribute it,
# with or without modifications, as long as this notice is preserved.

# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY, to the extent permitted by law; without
# even the implied warranty of MERCHANTABILITY or FITNESS FOR A
# PARTICULAR PURPOSE.

@SET_MAKE@

VPATH = @srcdir@
am__is_gnu_make = { \
  if test -z '$(MAKELEVEL)'; then \
    false; \
  elif test -n '$(MAKE_HOST)'; then \
    true; \
  elif test -n '$(MAKE_VERSION)' && test -n '$(CURDIR)'; then \
    true; \
  else \
    false; \
  fi; \
}
am__make_running_with_option = \
  case $${target_option-} in \
      ?) ;; \
      *) echo "am__make_running_with_option: internal error: invalid" \
              "target option '$${target_option-}' specified" >&2; \
         exit 1;; \
  esac; \
  has_opt=no; \
  sane_makeflags=$$MAKEFLAGS; \
  if $(am__is_gnu_make); then \
    sane_makeflags=$$MFLAGS; \
  else \
    case $$MAKEFLAGS in \
      *\\[\ \	]*) \
        bs=\\; \
        sane_makeflags=`printf '%s\n' "$$MAKEFLAGS" \
          | sed "s/$$bs$$bs[$$bs $$bs	]*//g"`;; \
    esac; \
  fi; \
  skip_next=no; \
  strip_trailopt () \
  { \
    flg=`printf '%s\n' "$$flg" | sed "s/$$1.*$$//"`; \
  }; \
  for flg in $$sane_makeflags; do \
    test $$skip_next = yes && { skip_next=no; continue; }; \
    case $$flg in \
      *=*|--*) continue;; \
        -*I) strip_trailopt 'I'; skip_next=yes;; \
      -*I?*) strip_trailopt 'I';; \
        -*O) strip_trailopt 'O'; skip_next=yes;; \
      -*O?*) strip_trailopt 'O';; \
        -*l) strip_trailopt 'l'; skip_next=yes;; \
      -*l?*) strip_trailopt 'l';; \
      -[dEDm]) skip_next=yes;; \
      -[JT]) skip_next=yes;; \
    esac; \
    case $$flg in \
      *$$target_option*) has_opt=yes; break;; \
    esac; \
  done; \
  test $$has_opt = yes
am__make_dryrun = (target_option=n; $(am__make_running_with_option))
am__make_keepgoing = (target_option=k; $(am__make_running_with_option))
pkgdatadir = $(datadir)/@PACKAGE@
pkgincludedir = $(includedir)/@PACKAGE@
pkglibdir = $(libdir)/@PACKAGE@
pkglibexecdir = $(libexecdir)/@PACKAGE@
am__cd = CDPATH="$${ZSH_VERSION+.}$(PATH_SEPARATOR)" && cd
install_sh_DATA = $(install_sh) -c -m 644
install_sh_PROGRAM = $(install_sh) -c
install_sh_SCRIPT = $(install_sh) -c
INSTALL_HEADER = $(INSTALL_DATA)
transform = $(program_transform_name)
NORMAL_INSTALL = :
PRE_INSTALL = :
POST_INSTALL = :
NORMAL_UNINSTALL = :
PRE_UNINSTALL = :
POST_UNINSTALL = :
build_triplet = @build@
host_triplet = @host@
target_triplet = @target@
subdir = lsf/lib
ACLOCAL_M4 = $(top_srcdir)/aclocal.m4
am__aclocal_m4_deps = $(top_srcdir)/configure.ac
am__configure_deps = $(am__aclocal_m4_deps) $(CONFIGURE_DEPENDENCIES) \
	$(ACLOCAL_M4)
DIST_COMMON = $(srcdir)/Makefile.am $(am__DIST_COMMON)
mkinstalldirs = $(install_sh) -d
CONFIG_HEADER = $(top_builddir)/config.h
CONFIG_CLEAN_FILES =
CONFIG_CLEAN_VPATH_FILES =
am__vpath_adj_setup = srcdirstrip=`echo "$(srcdir)" | sed 's|.|.|g'`;
am__vpath_adj = case $$p in \
    $(srcdir)/*) f=`echo "$$p" | sed "s|^$$srcdirstrip/||"`;; \
    *) f=$$p;; \
  esac;
am__strip_dir = f=`echo $$p | sed -e 's|^.*/||'`;
am__install_max = 40
am__nobase_strip_setup = \
  srcdirstrip=`echo "$(srcdir)" | sed 's/[].[^$$\\*|]/\\\\&/g'`
am__nobase_strip = \
  for p in $$list; do echo "$$p"; done | sed -e "s|$$srcdirstrip/||"
am__nobase_list = $(am__nobase_strip_setup); \
  for p in $$list; do echo "$$p $$p"; done | \
  sed "s| $$srcdirstrip/| |;"' / .*\//!s/ .*/ ./; s,\( .*\)/[^/]*$$,\1,' | \
  $(AWK) 'BEGIN { files["."] = "" } { files[$$2] = files[$$2] " " $$1; \
    if (++n[$$2] == $(am__install_max)) \
      { print $$2, files[$$2]; n[$$2] = 0; files[$$2] = "" } } \
    END { for (dir in files) print dir, files[dir] }'
am__base_list = \
  sed '$$!N;$$!N;$$!N;$$!N;$$!N;$$!N;$$!N;s/\n/ /g' | \
  sed '$$!N;$$!N;$$!N;$$!N;s/\n/ /g'
am__uninstall_files_from_dir = { \
  test -z "$$files" \
    || { test ! -d "$$dir" && test ! -f "$$dir" && test ! -r "$$dir"; } \
    || { echo " ( cd '$$dir' && rm -f" $$files ")"; \
         $(am__cd) "$$dir" && rm -f $$files; }; \
  }
am__installdirs = "$(DESTDIR)$(libdir)"
LIBRARIES = $(lib_LIBRARIES)
AR = ar
ARFLAGS = cru
AM_V_AR = $(am__v_AR_@AM_V@)
am__v_AR_ = $(am__v_AR_@AM_DEFAULT_V@)
am__v_AR_0 = @echo "  AR      " $@;
am__v_AR_1 = 
liblsf_a_AR = $(AR) $(ARFLAGS)
liblsf_a_LIBADD =
am_liblsf_a_OBJECTS = lib.channel.$(OBJEXT) lib.err.$(OBJEXT) \
	lib.limits.$(OBJEXT) lib.rcp.$(OBJEXT) lib.so.$(OBJEXT) \
	lib.xdr.$(OBJEXT) lib.comm.$(OBJEXT) lib.errno.$(OBJEXT) \
	lib.load.$(OBJEXT) lib.rdwr.$(OBJEXT) lib.sock.$(OBJEXT) \
	lib.xdrlim.$(OBJEXT) lib.conf.$(OBJEXT) lib.esub.$(OBJEXT) \
	lib.lsf.$(OBJEXT) lib.res.$(OBJEXT) lib.syntax.$(OBJEXT) \
	lib.xdrmisc.$(OBJEXT) lib.confmisc.$(OBJEXT) \
	lib.misc.$(OBJEXT) lib.resctrl.$(OBJEXT) lib.syslog.$(OBJEXT) \
	lib.xdrnio.$(OBJEXT) lib.conn.$(OBJEXT) lib.host.$(OBJEXT) \
	lib.nioback.$(OBJEXT) lib.reslog.$(OBJEXT) lib.table.$(OBJEXT) \
	lib.xdrres.$(OBJEXT) lib.control.$(OBJEXT) lib.i18n.$(OBJEXT) \
	lib.nios.$(OBJEXT) lib.rex.$(OBJEXT) lib.term.$(OBJEXT) \
	lib.xdrrf.$(OBJEXT) lib.cwd.$(OBJEXT) lib.id.$(OBJEXT) \
	lib.osal.$(OBJEXT) lib.rf.$(OBJEXT) lib.tid.$(OBJEXT) \
	usleep.$(OBJEXT) lib.debug.$(OBJEXT) lib.info.$(OBJEXT) \
	lib.pim.$(OBJEXT) lib.tty.$(OBJEXT) whathost.$(OBJEXT) \
	lib.dir.$(OBJEXT) lib.init.$(OBJEXT) lib.place.$(OBJEXT) \
	lib.rtask.$(OBJEXT) lib.utmp.$(OBJEXT) lib.eauth.$(OBJEXT) \
	lib.initenv.$(OBJEXT) lib.priority.$(OBJEXT) \
	lib.rwait.$(OBJEXT) lib.wconf.$(OBJEXT) lib.eligible.$(OBJEXT) \
	lib.lim.$(OBJEXT) lib.queue.$(OBJEXT) lib.sig.$(OBJEXT) \
	lib.words.$(OBJEXT)
liblsf_a_OBJECTS = $(am_liblsf_a_OBJECTS)
AM_V_P = $(am__v_P_@AM_V@)
am__v_P_ = $(am__v_P_@AM_DEFAULT_V@)
am__v_P_0 = false
am__v_P_1 = :
AM_V_GEN = $(am__v_GEN_@AM_V@)
am__v_GEN_ = $(am__v_GEN_@AM_DEFAULT_V@)
am__v_GEN_0 = @echo "  GEN     " $@;
am__v_GEN_1 = 
AM_V_at = $(am__v_at_@AM_V@)
am__v_at_ = $(am__v_at_@AM_DEFAULT_V@)
am__v_at_0 = @
am__v_at_1 = 
DEFAULT_INCLUDES = -I.@am__isrc@ -I$(top_builddir)
depcomp = $(SHELL) $(top_srcdir)/depcomp
am__maybe_remake_depfiles = depfiles
am__depfiles_remade = ./$(DEPDIR)/lib.channel.Po \
	./$(DEPDIR)/lib.comm.Po ./$(DEPDIR)/lib.conf.Po \
	./$(DEPDIR)/lib.confmisc.Po ./$(DEPDIR)/lib.conn.Po \
	./$(DEPDIR)/lib.control.Po ./$(DEPDIR)/lib.cwd.Po \
	./$(DEPDIR)/lib.debug.Po ./$(DEPDIR)/lib.dir.Po \
	./$(DEPDIR)/lib.eauth.Po ./$(DEPDIR)/lib.eligible.Po \
	./$(DEPDIR)/lib.err.Po ./$(DEPDIR)/lib.errno.Po \
	./$(DEPDIR)/lib.esub.Po ./$(DEPDIR)/lib.host.Po \
	./$(DEPDIR)/lib.i18n.Po ./$(DEPDIR)/lib.id.Po \
	./$(DEPDIR)/lib.info.Po ./$(DEPDIR)/lib.init.Po \
	./$(DEPDIR)/lib.initenv.Po ./$(DEPDIR)/lib.lim.Po \
	./$(DEPDIR)/lib.limits.Po ./$(DEPDIR)/lib.load.Po \
	./$(DEPDIR)/lib.lsf.Po ./$(DEPDIR)/lib.misc.Po \
	./$(DEPDIR)/lib.nioback.Po ./$(DEPDIR)/lib.nios.Po \
	./$(DEPDIR)/lib.osal.Po ./$(DEPDIR)/lib.pim.Po \
	./$(DEPDIR)/lib.place.Po ./$(DEPDIR)/lib.priority.Po \
	./$(DEPDIR)/lib.queue.Po ./$(DEPDIR)/lib.rcp.Po \
	./$(DEPDIR)/lib.rdwr.Po ./$(DEPDIR)/lib.res.Po \
	./$(DEPDIR)/lib.resctrl.Po ./$(DEPDIR)/lib.reslog.Po \
	./$(DEPDIR)/lib.rex.Po ./$(DEPDIR)/lib.rf.Po \
	./$(DEPDIR)/lib.rtask.Po ./$(DEPDIR)/lib.rwait.Po \
	./$(DEPDIR)/lib.sig.Po ./$(DEPDIR)/lib.so.Po \
	./$(DEPDIR)/lib.sock.Po ./$(DEPDIR)/lib.syntax.Po \
	./$(DEPDIR)/lib.syslog.Po ./$(DEPDIR)/lib.table.Po \
	./$(DEPDIR)/lib.term.Po ./$(DEPDIR)/lib.tid.Po \
	./$(DEPDIR)/lib.tty.Po ./$(DEPDIR)/lib.utmp.Po \
	./$(DEPDIR)/lib.wconf.Po ./$(DEPDIR)/lib.words.Po \
	./$(DEPDIR)/lib.xdr.Po ./$(DEPDIR)/lib.xdrlim.Po \
	./$(DEPDIR)/lib.xdrmisc.Po ./$(DEPDIR)/lib.xdrnio.Po \
	./$(DEPDIR)/lib.xdrres.Po ./$(DEPDIR)/lib.xdrrf.Po \
	./$(DEPDIR)/usleep.Po ./$(DEPDIR)/whathost.Po
am__mv = mv -f
COMPILE = $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) \
	$(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS)
AM_V_CC = $(am__v_CC_@AM_V@)
am__v_CC_ = $(am__v_CC_@AM_DEFAULT_V@)
am__v_CC_0 = @echo "  CC      " $@;
am__v_CC_1 = 
CCLD = $(CC)
LINK = $(CCLD) $(AM_CFLAGS) $(CFLAGS) $(AM_LDFLAGS) $(LDFLAGS) -o $@
AM_V_CCLD = $(am__v_CCLD_@AM_V@)
am__v_CCLD_ = $(am__v_CCLD_@AM_DEFAULT_V@)
am__v_CCLD_0 = @echo "  CCLD    " $@;
am__v_CCLD_1 = 
SOURCES = $(liblsf_a_SOURCES)
DIST_SOURCES = $(liblsf_a_SOURCES)
am__can_run_installinfo = \
  case $$AM_UPDATE_INFO_DIR in \
    n|no|NO) false;; \
    *) (install-info --version) >/dev/null 2>&1;; \
  esac
am__tagged_files = $(HEADERS) $(SOURCES) $(TAGS_FILES) $(LISP)
# Read a list of newline-separated strings from the standard input,
# and print each of them once, without duplicates.  Input order is
# *not* preserved.
am__uniquify_input = $(AWK) '\
  BEGIN { nonempty = 0; } \
  { items[$$0] = 1; nonempty = 1; } \
  END { if (nonempty) { for (i in items) print i; }; } \
'
# Make sure the list of sources is unique.  This is necessary because,
# e.g., the same source file might be shared among _SOURCES variables
# for different programs/libraries.
am__define_uniq_tagged_files = \
  list='$(am__tagged_files)'; \
  unique=`for i in $$list; do \
    if test -f "$$i"; then echo $$i; else echo $(srcdir)/$$i; fi; \
  done | $(am__uniquify_input)`
ETAGS = etags
CTAGS = ctags
am__DIST_COMMON = $(srcdir)/Makefile.in $(top_srcdir)/depcomp \
	ChangeLog
DISTFILES = $(DIST_COMMON) $(DIST_SOURCES) $(TEXINFOS) $(EXTRA_DIST)
ACLOCAL = @ACLOCAL@
AMTAR = @AMTAR@
AM_DEFAULT_VERBOSITY = @AM_DEFAULT_VERBOSITY@
AUTOCONF = @AUTOCONF@
AUTOHEADER = @AUTOHEADER@
AUTOMAKE = @AUTOMAKE@
AWK = @AWK@
CC = @CC@
CCDEPMODE = @CCDEPMODE@
CFLAGS = @CFLAGS@
CPP = @CPP@
CPPFLAGS = @CPPFLAGS@
CYGPATH_W = @CYGPATH_W@
DEFS = @DEFS@
DEPDIR = @DEPDIR@
ECHO_C = @ECHO_C@
ECHO_N = @ECHO_N@
ECHO_T = @ECHO_T@
EGREP = @EGREP@
EXEEXT = @EXEEXT@
GREP = @GREP@
INSTALL = @INSTALL@
INSTALL_DATA = @INSTALL_DATA@
INSTALL_PROGRAM = @INSTALL_PROGRAM@
INSTALL_SCRIPT = @INSTALL_SCRIPT@
INSTALL_STRIP_PROGRAM = @INSTALL_STRIP_PROGRAM@
LDFLAGS = @LDFLAGS@
LEX = @LEX@
LEXLIB = @LEXLIB@
LEX_OUTPUT_ROOT = @LEX_OUTPUT_ROOT@
LIBOBJS = @LIBOBJS@
LIBS = @LIBS@
LN_S = @LN_S@
LTLIBOBJS = @LTLIBOBJS@
MAKEINFO = @MAKEINFO@
MKDIR_P = @MKDIR_P@
OBJEXT = @OBJEXT@
PACKAGE = @PACKAGE@
PACKAGE_BUGREPORT = @PACKAGE_BUGREPORT@
PACKAGE_NAME = @PACKAGE_NAME@
PACKAGE_STRING = @PACKAGE_STRING@
PACKAGE_TARNAME = @PACKAGE_TARNAME@
PACKAGE_URL = @PACKAGE_URL@
PACKAGE_VERSION = @PACKAGE_VERSION@
PATH_SEPARATOR = @PATH_SEPARATOR@
RANLIB = @RANLIB@
SET_MAKE = @SET_MAKE@
SHELL = @SHELL@
STRIP = @STRIP@
VERSION = @VERSION@
YACC = @YACC@
YFLAGS = @YFLAGS@
abs_builddir = @abs_builddir@
abs_srcdir = @abs_srcdir@
abs_top_builddir = @abs_top_builddir@
abs_top_srcdir = @abs_top_srcdir@
ac_ct_CC = @ac_ct_CC@
am__include = @am__include@
am__leading_dot = @am__leading_dot@
am__quote = @am__quote@
am__tar = @am__tar@
am__untar = @am__untar@
bindir = @bindir@
build = @build@
build_alias = @build_alias@
build_cpu = @build_cpu@
build_os = @build_os@
build_vendor = @build_vendor@
builddir = @builddir@
datadir = @datadir@
datarootdir = @datarootdir@
docdir = @docdir@
dvidir = @dvidir@
exec_prefix = @exec_prefix@
host = @host@
host_alias = @host_alias@
host_cpu = @host_cpu@
host_os = @host_os@
host_vendor = @host_vendor@
htmldir = @htmldir@
includedir = @includedir@
infodir = @infodir@
install_sh = @install_sh@
libdir = @libdir@
libexecdir = @libexecdir@
localedir = @localedir@
localstatedir = @localstatedir@
mandir = @mandir@
mkdir_p = @mkdir_p@
oldincludedir = @oldincludedir@
pdfdir = @pdfdir@
prefix = @prefix@
program_transform_name = @program_transform_name@
psdir = @psdir@
runstatedir = @runstatedir@
sbindir = @sbindir@
sharedstatedir = @sharedstatedir@
srcdir = @srcdir@
sysconfdir = @sysconfdir@
target = @target@
target_alias = @target_alias@
target_cpu = @target_cpu@
target_os = @target_os@
target_vendor = @target_vendor@
top_build_prefix = @top_build_prefix@
top_builddir = @top_builddir@
top_srcdir = @top_srcdir@
volclavaadmin = @volclavaadmin@
volclavacluster = @volclavacluster@
volclavadmin = @volclavadmin@

#
# Copyright (C) 2011 David Bigagli
#
INCLUDES = -I../
lib_LIBRARIES = liblsf.a
liblsf_a_SOURCES = \
lib.channel.c lib.err.c lib.limits.c lib.rcp.c lib.so.c lib.xdr.c \
lib.comm.c lib.errno.c lib.load.c lib.rdwr.c lib.sock.c lib.xdrlim.c \
lib.conf.c lib.esub.c lib.lsf.c lib.res.c lib.syntax.c lib.xdrmisc.c \
lib.confmisc.c lib.misc.c lib.resctrl.c lib.syslog.c lib.xdrnio.c \
lib.conn.c lib.host.c lib.nioback.c lib.reslog.c lib.table.c lib.xdrres.c \
lib.control.c lib.i18n.c lib.nios.c lib.rex.c lib.term.c lib.xdrrf.c \
lib.cwd.c lib.id.c lib.osal.c lib.rf.c lib.tid.c usleep.c \
lib.debug.c lib.info.c lib.pim.c lib.tty.c whathost.c \
lib.dir.c lib.init.c lib.place.c lib.rtask.c lib.utmp.c \
lib.eauth.c lib.initenv.c lib.priority.c lib.rwait.c lib.wconf.c \
lib.eligible.c lib.lim.c lib.queue.c lib.sig.c lib.words.c \
lib.channel.h lib.h lib.osal.h lib.pim.h  lib.rcp.h lib.table.h \
lib.xdr.h lib.xdrres.h lsi18n.h lib.conf.h lib.hdr.h lib.osux.h lib.queue.h \
lib.rf.h lib.so.h lib.words.h lib.xdrlim.h lproto.h  mls.h

all: all-am

.SUFFIXES:
.SUFFIXES: .c .o .obj
$(srcdir)/Makefile.in:  $(srcdir)/Makefile.am  $(am__configure_deps)
	@for dep in $?; do \
	  case '$(am__configure_deps)' in \
	    *$$dep*) \
	      ( cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh ) \
	        && { if test -f $@; then exit 0; else break; fi; }; \
	      exit 1;; \
	  esac; \
	done; \
	echo ' cd $(top_srcdir) && $(AUTOMAKE) --gnu lsf/lib/Makefile'; \
	$(am__cd) $(top_srcdir) && \
	  $(AUTOMAKE) --gnu lsf/lib/Makefile
Makefile: $(srcdir)/Makefile.in $(top_builddir)/config.status
	@case '$?' in \
	  *config.status*) \
	    cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh;; \
	  *) \
	    echo ' cd $(top_builddir) && $(SHELL) ./config.status $(subdir)/$@ $(am__maybe_remake_depfiles)'; \
	    cd $(top_builddir) && $(SHELL) ./config.status $(subdir)/$@ $(am__maybe_remake_depfiles);; \
	esac;

$(top_builddir)/config.status: $(top_srcdir)/configure $(CONFIG_STATUS_DEPENDENCIES)
	cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh

$(top_srcdir)/configure:  $(am__configure_deps)
	cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh
$(ACLOCAL_M4):  $(am__aclocal_m4_deps)
	cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh
$(am__aclocal_m4_deps):
install-libLIBRARIES: $(lib_LIBRARIES)
	@$(NORMAL_INSTALL)
	@list='$(lib_LIBRARIES)'; test -n "$(libdir)" || list=; \
	list2=; for p in $$list; do \
	  if test -f $$p; then \
	    list2="$$list2 $$p"; \
	  else :; fi; \
	done; \
	test -z "$$list2" || { \
	  echo " $(MKDIR_P) '$(DESTDIR)$(libdir)'"; \
	  $(MKDIR_P) "$(DESTDIR)$(libdir)" || exit 1; \
	  echo " $(INSTALL_DATA) $$list2 '$(DESTDIR)$(libdir)'"; \
	  $(INSTALL_DATA) $$list2 "$(DESTDIR)$(libdir)" || exit $$?; }
	@$(POST_INSTALL)
	@list='$(lib_LIBRARIES)'; test -n "$(libdir)" || list=; \
	for p in $$list; do \
	  if test -f $$p; then \
	    $(am__strip_dir) \
	    echo " ( cd '$(DESTDIR)$(libdir)' && $(RANLIB) $$f )"; \
	    ( cd "$(DESTDIR)$(libdir)" && $(RANLIB) $$f ) || exit $$?; \
	  else :; fi; \
	done

uninstall-libLIBRARIES:
	@$(NORMAL_UNINSTALL)
	@list='$(lib_LIBRARIES)'; test -n "$(libdir)" || list=; \
	files=`for p in $$list; do echo $$p; done | sed -e 's|^.*/||'`; \
	dir='$(DESTDIR)$(libdir)'; $(am__uninstall_files_from_dir)

clean-libLIBRARIES:
	-test -z "$(lib_LIBRARIES)" || rm -f $(lib_LIBRARIES)

liblsf.a: $(liblsf_a_OBJECTS) $(liblsf_a_DEPENDENCIES) $(EXTRA_liblsf_a_DEPENDENCIES) 
	$(AM_V_at)-rm -f liblsf.a
	$(AM_V_AR)$(liblsf_a_AR) liblsf.a $(liblsf_a_OBJECTS) $(liblsf_a_LIBADD)
	$(AM_V_at)$(RANLIB) liblsf.a

mostlyclean-compile:
	-rm -f *.$(OBJEXT)

distclean-compile:
	-rm -f *.tab.c

@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/lib.channel.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/lib.comm.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/lib.conf.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/lib.confmisc.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/lib.conn.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/lib.control.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/lib.cwd.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/lib.debug.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/lib.dir.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/lib.eauth.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/lib.eligible.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/lib.err.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/lib.errno.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/lib.esub.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/lib.host.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/lib.i18n.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/lib.id.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/lib.info.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/lib.init.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/lib.initenv.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/lib.lim.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/lib.limits.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/lib.load.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/lib.lsf.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/lib.misc.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/lib.nioback.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/lib.nios.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/lib.osal.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/lib.pim.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/lib.place.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/lib.priority.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/lib.queue.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/lib.rcp.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/lib.rdwr.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/lib.res.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/lib.resctrl.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/lib.reslog.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/lib.rex.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/lib.rf.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/lib.rtask.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/lib.rwait.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/lib.sig.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/lib.so.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/lib.sock.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/lib.syntax.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/lib.syslog.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/lib.table.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/lib.term.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/lib.tid.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/lib.tty.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/lib.utmp.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/lib.wconf.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/lib.words.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/lib.xdr.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/lib.xdrlim.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/lib.xdrmisc.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/lib.xdrnio.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/lib.xdrres.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/lib.xdrrf.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/usleep.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/whathost.Po@am__quote@ # am--include-marker

$(am__depfiles_remade):
	@$(MKDIR_P) $(@D)
	@echo '# dummy' >$@-t && $(am__mv) $@-t $@

am--depfiles: $(am__depfiles_remade)

.c.o:
@am__fastdepCC_TRUE@	$(AM_V_CC)$(COMPILE) -MT $@ -MD -MP -MF $(DEPDIR)/$*.Tpo -c -o $@ $<
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/$*.Tpo $(DEPDIR)/$*.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='$<' object='$@' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(COMPILE) -c -o $@ $<

.c.obj:
@am__fastdepCC_TRUE@	$(AM_V_CC)$(COMPILE) -MT $@ -MD -MP -MF $(DEPDIR)/$*.Tpo -c -o $@ `$(CYGPATH_W) '$<'`
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/$*.Tpo $(DEPDIR)/$*.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='$<' object='$@' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(COMPILE) -c -o $@ `$(CYGPATH_W) '$<'`

ID: $(am__tagged_files)
	$(am__define_uniq_tagged_files); mkid -fID $$unique
tags: tags-am
TAGS: tags

tags-am: $(TAGS_DEPENDENCIES) $(am__tagged_files)
	set x; \
	here=`pwd`; \
	$(am__define_uniq_tagged_files); \
	shift; \
	if test -z "$(ETAGS_ARGS)$$*$$unique"; then :; else \
	  test -n "$$unique" || unique=$$empty_fix; \
	  if test $$# -gt 0; then \
	    $(ETAGS) $(ETAGSFLAGS) $(AM_ETAGSFLAGS) $(ETAGS_ARGS) \
	      "$$@" $$unique; \
	  else \
	    $(ETAGS) $(ETAGSFLAGS) $(AM_ETAGSFLAGS) $(ETAGS_ARGS) \
	      $$unique; \
	  fi; \
	fi
ctags: ctags-am

CTAGS: ctags
ctags-am: $(TAGS_DEPENDENCIES) $(am__tagged_files)
	$(am__define_uniq_tagged_files); \
	test -z "$(CTAGS_ARGS)$$unique" \
	  || $(CTAGS) $(CTAGSFLAGS) $(AM_CTAGSFLAGS) $(CTAGS_ARGS) \
	     $$unique

GTAGS:
	here=`$(am__cd) $(top_builddir) && pwd` \
	  && $(am__cd) $(top_srcdir) \
	  && gtags -i $(GTAGS_ARGS) "$$here"
cscopelist: cscopelist-am

cscopelist-am: $(am__tagged_files)
	list='$(am__tagged_files)'; \
	case "$(srcdir)" in \
	  [\\/]* | ?:[\\/]*) sdir="$(srcdir)" ;; \
	  *) sdir=$(subdir)/$(srcdir) ;; \
	esac; \
	for i in $$list; do \
	  if test -f "$$i"; then \
	    echo "$(subdir)/$$i"; \
	  else \
	    echo "$$sdir/$$i"; \
	  fi; \
	done >> $(top_builddir)/cscope.files

distclean-tags:
	-rm -f TAGS ID GTAGS GRTAGS GSYMS GPATH tags

distdir: $(BUILT_SOURCES)
	$(MAKE) $(AM_MAKEFLAGS) distdir-am

distdir-am: $(DISTFILES)
	@srcdirstrip=`echo "$(srcdir)" | sed 's/[].[^$$\\*]/\\\\&/g'`; \
	topsrcdirstrip=`echo "$(top_srcdir)" | sed 's/[].[^$$\\*]/\\\\&/g'`; \
	list='$(DISTFILES)'; \
	  dist_files=`for file in $$list; do echo $$file; done | \
	  sed -e "s|^$$srcdirstrip/||;t" \
	      -e "s|^$$topsrcdirstrip/|$(top_builddir)/|;t"`; \
	case $$dist_files in \
	  */*) $(MKDIR_P) `echo "$$dist_files" | \
			   sed '/\//!d;s|^|$(distdir)/|;s,/[^/]*$$,,' | \
			   sort -u` ;; \
	esac; \
	for file in $$dist_files; do \
	  if test -f $$file || test -d $$file; then d=.; else d=$(srcdir); fi; \
	  if test -d $$d/$$file; then \
	    dir=`echo "/$$file" | sed -e 's,/[^/]*$$,,'`; \
	    if test -d "$(distdir)/$$file"; then \
	      find "$(distdir)/$$file" -type d ! -perm -700 -exec chmod u+rwx {} \;; \
	    fi; \
	    if test -d $(srcdir)/$$file && test $$d != $(srcdir); then \
	      cp -fpR $(srcdir)/$$file "$(distdir)$$dir" || exit 1; \
	      find "$(distdir)/$$file" -type d ! -perm -700 -exec chmod u+rwx {} \;; \
	    fi; \
	    cp -fpR $$d/$$file "$(distdir)$$dir" || exit 1; \
	  else \
	    test -f "$(distdir)/$$file" \
	    || cp -p $$d/$$file "$(distdir)/$$file" \
	    || exit 1; \
	  fi; \
	done
check-am: all-am
check: check-am
all-am: Makefile $(LIBRARIES)
installdirs:
	for dir in "$(DESTDIR)$(libdir)"; do \
	  test -z "$$dir" || $(MKDIR_P) "$$dir"; \
	done
install: install-am
install-exec: install-exec-am
install-data: install-data-am
uninstall: uninstall-am

install-am: all-am
	@$(MAKE) $(AM_MAKEFLAGS) install-exec-am install-data-am

installcheck: installcheck-am
install-strip:
	if test -z '$(STRIP)'; then \
	  $(MAKE) $(AM_MAKEFLAGS) INSTALL_PROGRAM="$(INSTALL_STRIP_PROGRAM)" \
	    install_sh_PROGRAM="$(INSTALL_STRIP_PROGRAM)" INSTALL_STRIP_FLAG=-s \
	      install; \
	else \
	  $(MAKE) $(AM_MAKEFLAGS) INSTALL_PROGRAM="$(INSTALL_STRIP_PROGRAM)" \
	    install_sh_PROGRAM="$(INSTALL_STRIP_PROGRAM)" INSTALL_STRIP_FLAG=-s \
	    "INSTALL_PROGRAM_ENV=STRIPPROG='$(STRIP)'" install; \
	fi
mostlyclean-generic:

clean-generic:

distclean-generic:
	-test -z "$(CONFIG_CLEAN_FILES)" || rm -f $(CONFIG_CLEAN_FILES)
	-test . = "$(srcdir)" || test -z "$(CONFIG_CLEAN_VPATH_FILES)" || rm -f $(CONFIG_CLEAN_VPATH_FILES)

maintainer-clean-generic:
	@echo "This command is intended for maintainers to use"
	@echo "it deletes files that may require special tools to rebuild."
clean: clean-am

clean-am: clean-generic clean-libLIBRARIES mostlyclean-am

distclean: distclean-am
		-rm -f ./$(DEPDIR)/lib.channel.Po
	-rm -f ./$(DEPDIR)/lib.comm.Po
	-rm -f ./$(DEPDIR)/lib.conf.Po
	-rm -f ./$(DEPDIR)/lib.confmisc.Po
	-rm -f ./$(DEPDIR)/lib.conn.Po
	-rm -f ./$(DEPDIR)/lib.control.Po
	-rm -f ./$(DEPDIR)/lib.cwd.Po
	-rm -f ./$(DEPDIR)/lib.debug.Po
	-rm -f ./$(DEPDIR)/lib.dir.Po
	-rm -f ./$(DEPDIR)/lib.eauth.Po
	-rm -f ./$(DEPDIR)/lib.eligible.Po
	-rm -f ./$(DEPDIR)/lib.err.Po
	-rm -f ./$(DEPDIR)/lib.errno.Po
	-rm -f ./$(DEPDIR)/lib.esub.Po
	-rm -f ./$(DEPDIR)/lib.host.Po
	-rm -f ./$(DEPDIR)/lib.i18n.Po
	-rm -f ./$(DEPDIR)/lib.id.Po
	-rm -f ./$(DEPDIR)/lib.info.Po
	-rm -f ./$(DEPDIR)/lib.init.Po
	-rm -f ./$(DEPDIR)/lib.initenv.Po
	-rm -f ./$(DEPDIR)/lib.lim.Po
	-rm -f ./$(DEPDIR)/lib.limits.Po
	-rm -f ./$(DEPDIR)/lib.load.Po
	-rm -f ./$(DEPDIR)/lib.lsf.Po
	-rm -f ./$(DEPDIR)/lib.misc.Po
	-rm -f ./$(DEPDIR)/lib.nioback.Po
	-rm -f ./$(DEPDIR)/lib.nios.Po
	-rm -f ./$(DEPDIR)/lib.osal.Po
	-rm -f ./$(DEPDIR)/lib.pim.Po
	-rm -f ./$(DEPDIR)/lib.place.Po
	-rm -f ./$(DEPDIR)/lib.priority.Po
	-rm -f ./$(DEPDIR)/lib.queue.Po
	-rm -f ./$(DEPDIR)/lib.rcp.Po
	-rm -f ./$(DEPDIR)/lib.rdwr.Po
	-rm -f ./$(DEPDIR)/lib.res.Po
	-rm -f ./$(DEPDIR)/lib.resctrl.Po
	-rm -f ./$(DEPDIR)/lib.reslog.Po
	-rm -f ./$(DEPDIR)/lib.rex.Po
	-rm -f ./$(DEPDIR)/lib.rf.Po
	-rm -f ./$(DEPDIR)/lib.rtask.Po
	-rm -f ./$(DEPDIR)/lib.rwait.Po
	-rm -f ./$(DEPDIR)/lib.sig.Po
	-rm -f ./$(DEPDIR)/lib.so.Po
	-rm -f ./$(DEPDIR)/lib.sock.Po
	-rm -f ./$(DEPDIR)/lib.syntax.Po
	-rm -f ./$(DEPDIR)/lib.syslog.Po
	-rm -f ./$(DEPDIR)/lib.table.Po
	-rm -f ./$(DEPDIR)/lib.term.Po
	-rm -f ./$(DEPDIR)/lib.tid.Po
	-rm -f ./$(DEPDIR)/lib.tty.Po
	-rm -f ./$(DEPDIR)/lib.utmp.Po
	-rm -f ./$(DEPDIR)/lib.wconf.Po
	-rm -f ./$(DEPDIR)/lib.words.Po
	-rm -f ./$(DEPDIR)/lib.xdr.Po
	-rm -f ./$(DEPDIR)/lib.xdrlim.Po
	-rm -f ./$(DEPDIR)/lib.xdrmisc.Po
	-rm -f ./$(DEPDIR)/lib.xdrnio.Po
	-rm -f ./$(DEPDIR)/lib.xdrres.Po
	-rm -f ./$(DEPDIR)/lib.xdrrf.Po
	-rm -f ./$(DEPDIR)/usleep.Po
	-rm -f ./$(DEPDIR)/whathost.Po
	-rm -f Makefile
distclean-am: clean-am distclean-compile distclean-generic \
	distclean-tags

dvi: dvi-am

dvi-am:

html: html-am

html-am:

info: info-am

info-am:

install-data-am:

install-dvi: install-dvi-am

install-dvi-am:

install-exec-am: install-libLIBRARIES

install-html: install-html-am

install-html-am:

install-info: install-info-am

install-info-am:

install-man:

install-pdf: install-pdf-am

install-pdf-am:

install-ps: install-ps-am

install-ps-am:

installcheck-am:

maintainer-clean: maintainer-clean-am
		-rm -f ./$(DEPDIR)/lib.channel.Po
	-rm -f ./$(DEPDIR)/lib.comm.Po
	-rm -f ./$(DEPDIR)/lib.conf.Po
	-rm -f ./$(DEPDIR)/lib.confmisc.Po
	-rm -f ./$(DEPDIR)/lib.conn.Po
	-rm -f ./$(DEPDIR)/lib.control.Po
	-rm -f ./$(DEPDIR)/lib.cwd.Po
	-rm -f ./$(DEPDIR)/lib.debug.Po
	-rm -f ./$(DEPDIR)/lib.dir.Po
	-rm -f ./$(DEPDIR)/lib.eauth.Po
	-rm -f ./$(DEPDIR)/lib.eligible.Po
	-rm -f ./$(DEPDIR)/lib.err.Po
	-rm -f ./$(DEPDIR)/lib.errno.Po
	-rm -f ./$(DEPDIR)/lib.esub.Po
	-rm -f ./$(DEPDIR)/lib.host.Po
	-rm -f ./$(DEPDIR)/lib.i18n.Po
	-rm -f ./$(DEPDIR)/lib.id.Po
	-rm -f ./$(DEPDIR)/lib.info.Po
	-rm -f ./$(DEPDIR)/lib.init.Po
	-rm -f ./$(DEPDIR)/lib.initenv.Po
	-rm -f ./$(DEPDIR)/lib.lim.Po
	-rm -f ./$(DEPDIR)/lib.limits.Po
	-rm -f ./$(DEPDIR)/lib.load.Po
	-rm -f ./$(DEPDIR)/lib.lsf.Po
	-rm -f ./$(DEPDIR)/lib.misc.Po
	-rm -f ./$(DEPDIR)/lib.nioback.Po
	-rm -f ./$(DEPDIR)/lib.nios.Po
	-rm -f ./$(DEPDIR)/lib.osal.Po
	-rm -f ./$(DEPDIR)/lib.pim.Po
	-rm -f ./$(DEPDIR)/lib.place.Po
	-rm -f ./$(DEPDIR)/lib.priority.Po
	-rm -f ./$(DEPDIR)/lib.queue.Po
	-rm -f ./$(DEPDIR)/lib.rcp.Po
	-rm -f ./$(DEPDIR)/lib.rdwr.Po
	-rm -f ./$(DEPDIR)/lib.res.Po
	-rm -f ./$(DEPDIR)/lib.resctrl.Po
	-rm -f ./$(DEPDIR)/lib.reslog.Po
	-rm -f ./$(DEPDIR)/lib.rex.Po
	-rm -f ./$(DEPDIR)/lib.rf.Po
	-rm -f ./$(DEPDIR)/lib.rtask.Po
	-rm -f ./$(DEPDIR)/lib.rwait.Po
	-rm -f ./$(DEPDIR)/lib.sig.Po
	-rm -f ./$(DEPDIR)/lib.so.Po
	-rm -f ./$(DEPDIR)/lib.sock.Po
	-rm -f ./$(DEPDIR)/lib.syntax.Po
	-rm -f ./$(DEPDIR)/lib.syslog.Po
	-rm -f ./$(DEPDIR)/lib.table.Po
	-rm -f ./$(DEPDIR)/lib.term.Po
	-rm -f ./$(DEPDIR)/lib.tid.Po
	-rm -f ./$(DEPDIR)/lib.tty.Po
	-rm -f ./$(DEPDIR)/lib.utmp.Po
	-rm -f ./$(DEPDIR)/lib.wconf.Po
	-rm -f ./$(DEPDIR)/lib.words.Po
	-rm -f ./$(DEPDIR)/lib.xdr.Po
	-rm -f ./$(DEPDIR)/lib.xdrlim.Po
	-rm -f ./$(DEPDIR)/lib.xdrmisc.Po
	-rm -f ./$(DEPDIR)/lib.xdrnio.Po
	-rm -f ./$(DEPDIR)/lib.xdrres.Po
	-rm -f ./$(DEPDIR)/lib.xdrrf.Po
	-rm -f ./$(DEPDIR)/usleep.Po
	-rm -f ./$(DEPDIR)/whathost.Po
	-rm -f Makefile
maintainer-clean-am: distclean-am maintainer-clean-generic

mostlyclean: mostlyclean-am

mostlyclean-am: mostlyclean-compile mostlyclean-generic

pdf: pdf-am

pdf-am:

ps: ps-am

ps-am:

uninstall-am: uninstall-libLIBRARIES

.MAKE: install-am install-strip

.PHONY: CTAGS GTAGS TAGS all all-am am--depfiles check check-am clean \
	clean-generic clean-libLIBRARIES cscopelist-am ctags ctags-am \
	distclean distclean-compile distclean-generic distclean-tags \
	distdir dvi dvi-am html html-am info info-am install \
	install-am install-data install-data-am install-dvi \
	install-dvi-am install-exec install-exec-am install-html \
	install-html-am install-info install-info-am \
	install-libLIBRARIES install-man install-pdf install-pdf-am \
	install-ps install-ps-am install-strip installcheck \
	installcheck-am installdirs maintainer-clean \
	maintainer-clean-generic mostlyclean mostlyclean-compile \
	mostlyclean-generic pdf pdf-am ps ps-am tags tags-am uninstall \
	uninstall-am uninstall-libLIBRARIES

.PRECIOUS: Makefile


etags:
	etags ../*.h *.[hc] ../intlib/*.[hc] ../lim/*.[hc] \
	../res/*.[hc] ../lstools/*.[hc]

# Tell versions [3.59,3.63) of GNU make to not export all variables.
# Otherwise a system limit (for SysV at least) may be exceeded.
.NOEXPORT:
