#
# Copyright (C) 2011 <PERSON>
#
INCLUDES = -I../

lib_LIBRARIES = liblsf.a

liblsf_a_SOURCES = \
lib.channel.c lib.err.c lib.limits.c lib.rcp.c lib.so.c lib.xdr.c \
lib.comm.c lib.errno.c lib.load.c lib.rdwr.c lib.sock.c lib.xdrlim.c \
lib.conf.c lib.esub.c lib.lsf.c lib.res.c lib.syntax.c lib.xdrmisc.c \
lib.confmisc.c lib.misc.c lib.resctrl.c lib.syslog.c lib.xdrnio.c \
lib.conn.c lib.host.c lib.nioback.c lib.reslog.c lib.table.c lib.xdrres.c \
lib.control.c lib.i18n.c lib.nios.c lib.rex.c lib.term.c lib.xdrrf.c \
lib.cwd.c lib.id.c lib.osal.c lib.rf.c lib.tid.c usleep.c \
lib.debug.c lib.info.c lib.pim.c lib.tty.c whathost.c \
lib.dir.c lib.init.c lib.place.c lib.rtask.c lib.utmp.c \
lib.eauth.c lib.initenv.c lib.priority.c lib.rwait.c lib.wconf.c \
lib.eligible.c lib.lim.c lib.queue.c lib.sig.c lib.words.c \
lib.channel.h lib.h lib.osal.h lib.pim.h  lib.rcp.h lib.table.h \
lib.xdr.h lib.xdrres.h lsi18n.h lib.conf.h lib.hdr.h lib.osux.h lib.queue.h \
lib.rf.h lib.so.h lib.words.h lib.xdrlim.h lproto.h  mls.h

etags:
	etags ../*.h *.[hc] ../intlib/*.[hc] ../lim/*.[hc] \
	../res/*.[hc] ../lstools/*.[hc]
