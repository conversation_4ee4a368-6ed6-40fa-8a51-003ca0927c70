# Makefile.in generated by automake 1.16.1 from Makefile.am.
# lsf/lstools/Makefile.  Generated from Makefile.in by configure.

# Copyright (C) 1994-2018 Free Software Foundation, Inc.

# This Makefile.in is free software; the Free Software Foundation
# gives unlimited permission to copy and/or distribute it,
# with or without modifications, as long as this notice is preserved.

# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY, to the extent permitted by law; without
# even the implied warranty of MERCHANTABILITY or FITNESS FOR A
# PARTICULAR PURPOSE.




am__is_gnu_make = { \
  if test -z '$(MAKELEVEL)'; then \
    false; \
  elif test -n '$(MAKE_HOST)'; then \
    true; \
  elif test -n '$(MAKE_VERSION)' && test -n '$(CURDIR)'; then \
    true; \
  else \
    false; \
  fi; \
}
am__make_running_with_option = \
  case $${target_option-} in \
      ?) ;; \
      *) echo "am__make_running_with_option: internal error: invalid" \
              "target option '$${target_option-}' specified" >&2; \
         exit 1;; \
  esac; \
  has_opt=no; \
  sane_makeflags=$$MAKEFLAGS; \
  if $(am__is_gnu_make); then \
    sane_makeflags=$$MFLAGS; \
  else \
    case $$MAKEFLAGS in \
      *\\[\ \	]*) \
        bs=\\; \
        sane_makeflags=`printf '%s\n' "$$MAKEFLAGS" \
          | sed "s/$$bs$$bs[$$bs $$bs	]*//g"`;; \
    esac; \
  fi; \
  skip_next=no; \
  strip_trailopt () \
  { \
    flg=`printf '%s\n' "$$flg" | sed "s/$$1.*$$//"`; \
  }; \
  for flg in $$sane_makeflags; do \
    test $$skip_next = yes && { skip_next=no; continue; }; \
    case $$flg in \
      *=*|--*) continue;; \
        -*I) strip_trailopt 'I'; skip_next=yes;; \
      -*I?*) strip_trailopt 'I';; \
        -*O) strip_trailopt 'O'; skip_next=yes;; \
      -*O?*) strip_trailopt 'O';; \
        -*l) strip_trailopt 'l'; skip_next=yes;; \
      -*l?*) strip_trailopt 'l';; \
      -[dEDm]) skip_next=yes;; \
      -[JT]) skip_next=yes;; \
    esac; \
    case $$flg in \
      *$$target_option*) has_opt=yes; break;; \
    esac; \
  done; \
  test $$has_opt = yes
am__make_dryrun = (target_option=n; $(am__make_running_with_option))
am__make_keepgoing = (target_option=k; $(am__make_running_with_option))
pkgdatadir = $(datadir)/volclava
pkgincludedir = $(includedir)/volclava
pkglibdir = $(libdir)/volclava
pkglibexecdir = $(libexecdir)/volclava
am__cd = CDPATH="$${ZSH_VERSION+.}$(PATH_SEPARATOR)" && cd
install_sh_DATA = $(install_sh) -c -m 644
install_sh_PROGRAM = $(install_sh) -c
install_sh_SCRIPT = $(install_sh) -c
INSTALL_HEADER = $(INSTALL_DATA)
transform = $(program_transform_name)
NORMAL_INSTALL = :
PRE_INSTALL = :
POST_INSTALL = :
NORMAL_UNINSTALL = :
PRE_UNINSTALL = :
POST_UNINSTALL = :
build_triplet = x86_64-pc-linux-gnu
host_triplet = x86_64-pc-linux-gnu
target_triplet = x86_64-pc-linux-gnu
bin_PROGRAMS = lsacct$(EXEEXT) lseligible$(EXEEXT) lshosts$(EXEEXT) \
	lsid$(EXEEXT) lsinfo$(EXEEXT) lsloadadj$(EXEEXT) \
	lsload$(EXEEXT) lsmon$(EXEEXT) lsplace$(EXEEXT) lsrcp$(EXEEXT) \
	lsrun$(EXEEXT) lsaddhost$(EXEEXT) lsrmhost$(EXEEXT)
am__append_1 = -lnsl
subdir = lsf/lstools
ACLOCAL_M4 = $(top_srcdir)/aclocal.m4
am__aclocal_m4_deps = $(top_srcdir)/configure.ac
am__configure_deps = $(am__aclocal_m4_deps) $(CONFIGURE_DEPENDENCIES) \
	$(ACLOCAL_M4)
DIST_COMMON = $(srcdir)/Makefile.am $(am__DIST_COMMON)
mkinstalldirs = $(install_sh) -d
CONFIG_HEADER = $(top_builddir)/config.h
CONFIG_CLEAN_FILES =
CONFIG_CLEAN_VPATH_FILES =
am__installdirs = "$(DESTDIR)$(bindir)"
PROGRAMS = $(bin_PROGRAMS)
am_lsacct_OBJECTS = lsacct.$(OBJEXT)
lsacct_OBJECTS = $(am_lsacct_OBJECTS)
lsacct_DEPENDENCIES = ../lib/liblsf.a ../intlib/liblsfint.a
am_lsaddhost_OBJECTS = lsaddhost.$(OBJEXT)
lsaddhost_OBJECTS = $(am_lsaddhost_OBJECTS)
lsaddhost_DEPENDENCIES = ../lib/liblsf.a
am_lseligible_OBJECTS = lseligible.$(OBJEXT)
lseligible_OBJECTS = $(am_lseligible_OBJECTS)
lseligible_DEPENDENCIES = ../lib/liblsf.a ../intlib/liblsfint.a
am_lshosts_OBJECTS = lshosts.$(OBJEXT)
lshosts_OBJECTS = $(am_lshosts_OBJECTS)
lshosts_DEPENDENCIES = ../lib/liblsf.a ../intlib/liblsfint.a
am_lsid_OBJECTS = lsid.$(OBJEXT)
lsid_OBJECTS = $(am_lsid_OBJECTS)
lsid_DEPENDENCIES = ../lib/liblsf.a ../intlib/liblsfint.a
am_lsinfo_OBJECTS = lsinfo.$(OBJEXT)
lsinfo_OBJECTS = $(am_lsinfo_OBJECTS)
lsinfo_DEPENDENCIES = ../lib/liblsf.a ../intlib/liblsfint.a
am_lsload_OBJECTS = lsload.$(OBJEXT) load.$(OBJEXT)
lsload_OBJECTS = $(am_lsload_OBJECTS)
lsload_DEPENDENCIES = ../lib/liblsf.a ../intlib/liblsfint.a
am_lsloadadj_OBJECTS = lsloadadj.$(OBJEXT)
lsloadadj_OBJECTS = $(am_lsloadadj_OBJECTS)
lsloadadj_DEPENDENCIES = ../lib/liblsf.a ../intlib/liblsfint.a
am_lsmon_OBJECTS = lsmon.$(OBJEXT)
lsmon_OBJECTS = $(am_lsmon_OBJECTS)
lsmon_DEPENDENCIES = load.o ../lib/liblsf.a ../intlib/liblsfint.a
am_lsplace_OBJECTS = lsplace.$(OBJEXT)
lsplace_OBJECTS = $(am_lsplace_OBJECTS)
lsplace_DEPENDENCIES = ../lib/liblsf.a ../intlib/liblsfint.a
am_lsrcp_OBJECTS = lsrcp.$(OBJEXT)
lsrcp_OBJECTS = $(am_lsrcp_OBJECTS)
am__DEPENDENCIES_1 =
lsrcp_DEPENDENCIES = ../lib/liblsf.a ../intlib/liblsfint.a \
	$(am__DEPENDENCIES_1)
am_lsrmhost_OBJECTS = lsrmhost.$(OBJEXT)
lsrmhost_OBJECTS = $(am_lsrmhost_OBJECTS)
lsrmhost_DEPENDENCIES = ../lib/liblsf.a
am_lsrun_OBJECTS = lsrun.$(OBJEXT)
lsrun_OBJECTS = $(am_lsrun_OBJECTS)
lsrun_DEPENDENCIES = ../lib/liblsf.a
AM_V_P = $(am__v_P_$(V))
am__v_P_ = $(am__v_P_$(AM_DEFAULT_VERBOSITY))
am__v_P_0 = false
am__v_P_1 = :
AM_V_GEN = $(am__v_GEN_$(V))
am__v_GEN_ = $(am__v_GEN_$(AM_DEFAULT_VERBOSITY))
am__v_GEN_0 = @echo "  GEN     " $@;
am__v_GEN_1 = 
AM_V_at = $(am__v_at_$(V))
am__v_at_ = $(am__v_at_$(AM_DEFAULT_VERBOSITY))
am__v_at_0 = @
am__v_at_1 = 
DEFAULT_INCLUDES = -I. -I$(top_builddir)
depcomp = $(SHELL) $(top_srcdir)/depcomp
am__maybe_remake_depfiles = depfiles
am__depfiles_remade = ./$(DEPDIR)/load.Po ./$(DEPDIR)/lsacct.Po \
	./$(DEPDIR)/lsaddhost.Po ./$(DEPDIR)/lseligible.Po \
	./$(DEPDIR)/lshosts.Po ./$(DEPDIR)/lsid.Po \
	./$(DEPDIR)/lsinfo.Po ./$(DEPDIR)/lsload.Po \
	./$(DEPDIR)/lsloadadj.Po ./$(DEPDIR)/lsmon.Po \
	./$(DEPDIR)/lsplace.Po ./$(DEPDIR)/lsrcp.Po \
	./$(DEPDIR)/lsrmhost.Po ./$(DEPDIR)/lsrun.Po
am__mv = mv -f
COMPILE = $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) \
	$(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS)
AM_V_CC = $(am__v_CC_$(V))
am__v_CC_ = $(am__v_CC_$(AM_DEFAULT_VERBOSITY))
am__v_CC_0 = @echo "  CC      " $@;
am__v_CC_1 = 
CCLD = $(CC)
LINK = $(CCLD) $(AM_CFLAGS) $(CFLAGS) $(AM_LDFLAGS) $(LDFLAGS) -o $@
AM_V_CCLD = $(am__v_CCLD_$(V))
am__v_CCLD_ = $(am__v_CCLD_$(AM_DEFAULT_VERBOSITY))
am__v_CCLD_0 = @echo "  CCLD    " $@;
am__v_CCLD_1 = 
SOURCES = $(lsacct_SOURCES) $(lsaddhost_SOURCES) $(lseligible_SOURCES) \
	$(lshosts_SOURCES) $(lsid_SOURCES) $(lsinfo_SOURCES) \
	$(lsload_SOURCES) $(lsloadadj_SOURCES) $(lsmon_SOURCES) \
	$(lsplace_SOURCES) $(lsrcp_SOURCES) $(lsrmhost_SOURCES) \
	$(lsrun_SOURCES)
DIST_SOURCES = $(lsacct_SOURCES) $(lsaddhost_SOURCES) \
	$(lseligible_SOURCES) $(lshosts_SOURCES) $(lsid_SOURCES) \
	$(lsinfo_SOURCES) $(lsload_SOURCES) $(lsloadadj_SOURCES) \
	$(lsmon_SOURCES) $(lsplace_SOURCES) $(lsrcp_SOURCES) \
	$(lsrmhost_SOURCES) $(lsrun_SOURCES)
am__can_run_installinfo = \
  case $$AM_UPDATE_INFO_DIR in \
    n|no|NO) false;; \
    *) (install-info --version) >/dev/null 2>&1;; \
  esac
am__tagged_files = $(HEADERS) $(SOURCES) $(TAGS_FILES) $(LISP)
# Read a list of newline-separated strings from the standard input,
# and print each of them once, without duplicates.  Input order is
# *not* preserved.
am__uniquify_input = $(AWK) '\
  BEGIN { nonempty = 0; } \
  { items[$$0] = 1; nonempty = 1; } \
  END { if (nonempty) { for (i in items) print i; }; } \
'
# Make sure the list of sources is unique.  This is necessary because,
# e.g., the same source file might be shared among _SOURCES variables
# for different programs/libraries.
am__define_uniq_tagged_files = \
  list='$(am__tagged_files)'; \
  unique=`for i in $$list; do \
    if test -f "$$i"; then echo $$i; else echo $(srcdir)/$$i; fi; \
  done | $(am__uniquify_input)`
ETAGS = etags
CTAGS = ctags
am__DIST_COMMON = $(srcdir)/Makefile.in $(top_srcdir)/depcomp \
	ChangeLog
DISTFILES = $(DIST_COMMON) $(DIST_SOURCES) $(TEXINFOS) $(EXTRA_DIST)
ACLOCAL = ${SHELL} /mnt/hgfs/ubuntu_share/volclava/missing aclocal-1.16
AMTAR = $${TAR-tar}
AM_DEFAULT_VERBOSITY = 0
AUTOCONF = ${SHELL} /mnt/hgfs/ubuntu_share/volclava/missing autoconf
AUTOHEADER = ${SHELL} /mnt/hgfs/ubuntu_share/volclava/missing autoheader
AUTOMAKE = ${SHELL} /mnt/hgfs/ubuntu_share/volclava/missing automake-1.16
AWK = mawk
CC = gcc
CCDEPMODE = depmode=gcc3
CFLAGS = -g -O2 -Wall -fPIC -Wno-error=format-security -I/usr/include/tirpc
CPP = gcc -E
CPPFLAGS = 
CYGPATH_W = echo
DEFS = -DHAVE_CONFIG_H
DEPDIR = .deps
ECHO_C = 
ECHO_N = -n
ECHO_T = 
EGREP = /usr/bin/grep -E
EXEEXT = 
GREP = /usr/bin/grep
INSTALL = /usr/bin/install -c
INSTALL_DATA = ${INSTALL} -m 644
INSTALL_PROGRAM = ${INSTALL}
INSTALL_SCRIPT = ${INSTALL}
INSTALL_STRIP_PROGRAM = $(install_sh) -c -s
LDFLAGS = 
LEX = :
LEXLIB = 
LEX_OUTPUT_ROOT = 
LIBOBJS = 
LIBS = -ltcl 
LN_S = cp -pR
LTLIBOBJS = 
MAKEINFO = ${SHELL} /mnt/hgfs/ubuntu_share/volclava/missing makeinfo
MKDIR_P = /usr/bin/mkdir -p
OBJEXT = o
PACKAGE = volclava
PACKAGE_BUGREPORT = 
PACKAGE_NAME = volclava
PACKAGE_STRING = volclava 2.0
PACKAGE_TARNAME = volclava
PACKAGE_URL = 
PACKAGE_VERSION = 2.0
PATH_SEPARATOR = :
RANLIB = ranlib
SET_MAKE = 
SHELL = /bin/bash
STRIP = 
VERSION = 2.0
YACC = yacc
YFLAGS = 
abs_builddir = /mnt/hgfs/ubuntu_share/volclava/lsf/lstools
abs_srcdir = /mnt/hgfs/ubuntu_share/volclava/lsf/lstools
abs_top_builddir = /mnt/hgfs/ubuntu_share/volclava
abs_top_srcdir = /mnt/hgfs/ubuntu_share/volclava
ac_ct_CC = gcc
am__include = include
am__leading_dot = .
am__quote = 
am__tar = $${TAR-tar} chof - "$$tardir"
am__untar = $${TAR-tar} xf -
bindir = ${exec_prefix}/bin
build = x86_64-pc-linux-gnu
build_alias = 
build_cpu = x86_64
build_os = linux-gnu
build_vendor = pc
builddir = .
datadir = ${datarootdir}
datarootdir = ${prefix}/share
docdir = ${datarootdir}/doc/${PACKAGE_TARNAME}
dvidir = ${docdir}
exec_prefix = ${prefix}
host = x86_64-pc-linux-gnu
host_alias = 
host_cpu = x86_64
host_os = linux-gnu
host_vendor = pc
htmldir = ${docdir}
includedir = ${prefix}/include
infodir = ${datarootdir}/info
install_sh = ${SHELL} /mnt/hgfs/ubuntu_share/volclava/install-sh
libdir = ${exec_prefix}/lib
libexecdir = ${exec_prefix}/libexec
localedir = ${datarootdir}/locale
localstatedir = ${prefix}/var
mandir = ${datarootdir}/man
mkdir_p = $(MKDIR_P)
oldincludedir = /usr/include
pdfdir = ${docdir}
prefix = /opt/volclava-2.0
program_transform_name = s,x,x,
psdir = ${docdir}
runstatedir = ${localstatedir}/run
sbindir = ${exec_prefix}/sbin
sharedstatedir = ${prefix}/com
srcdir = .
sysconfdir = ${prefix}/etc
target = x86_64-pc-linux-gnu
target_alias = 
target_cpu = x86_64
target_os = linux-gnu
target_vendor = pc
top_build_prefix = ../../
top_builddir = ../..
top_srcdir = ../..
volclavaadmin = 
volclavacluster = volclava
volclavadmin = volclava

#
# Copyright (C) openlava foundation
#
INCLUDES = -I..
lsacct_SOURCES = lsacct.c  
lsacct_LDADD = ../lib/liblsf.a ../intlib/liblsfint.a
lseligible_SOURCES = lseligible.c  
lseligible_LDADD = ../lib/liblsf.a ../intlib/liblsfint.a
lshosts_SOURCES = lshosts.c  
lshosts_LDADD = ../lib/liblsf.a ../intlib/liblsfint.a
lsid_SOURCES = lsid.c  
lsid_LDADD = ../lib/liblsf.a ../intlib/liblsfint.a
lsinfo_SOURCES = lsinfo.c  
lsinfo_LDADD = ../lib/liblsf.a ../intlib/liblsfint.a
lsloadadj_SOURCES = lsloadadj.c  
lsloadadj_LDADD = ../lib/liblsf.a ../intlib/liblsfint.a
lsload_SOURCES = lsload.c load.c
lsload_LDADD = ../lib/liblsf.a ../intlib/liblsfint.a
lsmon_SOURCES = lsmon.c  
lsmon_LDADD = load.o ../lib/liblsf.a ../intlib/liblsfint.a -lncurses
lsplace_SOURCES = lsplace.c  
lsplace_LDADD = ../lib/liblsf.a ../intlib/liblsfint.a
lsrcp_SOURCES = lsrcp.c
lsrcp_LDADD = ../lib/liblsf.a ../intlib/liblsfint.a $(am__append_1)
lsrun_SOURCES = lsrun.c
lsrun_LDADD = ../lib/liblsf.a 
lsaddhost_SOURCES = lsaddhost.c
lsaddhost_LDADD = ../lib/liblsf.a 
lsrmhost_SOURCES = lsrmhost.c
lsrmhost_LDADD = ../lib/liblsf.a 
all: all-am

.SUFFIXES:
.SUFFIXES: .c .o .obj
$(srcdir)/Makefile.in:  $(srcdir)/Makefile.am  $(am__configure_deps)
	@for dep in $?; do \
	  case '$(am__configure_deps)' in \
	    *$$dep*) \
	      ( cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh ) \
	        && { if test -f $@; then exit 0; else break; fi; }; \
	      exit 1;; \
	  esac; \
	done; \
	echo ' cd $(top_srcdir) && $(AUTOMAKE) --gnu lsf/lstools/Makefile'; \
	$(am__cd) $(top_srcdir) && \
	  $(AUTOMAKE) --gnu lsf/lstools/Makefile
Makefile: $(srcdir)/Makefile.in $(top_builddir)/config.status
	@case '$?' in \
	  *config.status*) \
	    cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh;; \
	  *) \
	    echo ' cd $(top_builddir) && $(SHELL) ./config.status $(subdir)/$@ $(am__maybe_remake_depfiles)'; \
	    cd $(top_builddir) && $(SHELL) ./config.status $(subdir)/$@ $(am__maybe_remake_depfiles);; \
	esac;

$(top_builddir)/config.status: $(top_srcdir)/configure $(CONFIG_STATUS_DEPENDENCIES)
	cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh

$(top_srcdir)/configure:  $(am__configure_deps)
	cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh
$(ACLOCAL_M4):  $(am__aclocal_m4_deps)
	cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh
$(am__aclocal_m4_deps):
install-binPROGRAMS: $(bin_PROGRAMS)
	@$(NORMAL_INSTALL)
	@list='$(bin_PROGRAMS)'; test -n "$(bindir)" || list=; \
	if test -n "$$list"; then \
	  echo " $(MKDIR_P) '$(DESTDIR)$(bindir)'"; \
	  $(MKDIR_P) "$(DESTDIR)$(bindir)" || exit 1; \
	fi; \
	for p in $$list; do echo "$$p $$p"; done | \
	sed 's/$(EXEEXT)$$//' | \
	while read p p1; do if test -f $$p \
	  ; then echo "$$p"; echo "$$p"; else :; fi; \
	done | \
	sed -e 'p;s,.*/,,;n;h' \
	    -e 's|.*|.|' \
	    -e 'p;x;s,.*/,,;s/$(EXEEXT)$$//;$(transform);s/$$/$(EXEEXT)/' | \
	sed 'N;N;N;s,\n, ,g' | \
	$(AWK) 'BEGIN { files["."] = ""; dirs["."] = 1 } \
	  { d=$$3; if (dirs[d] != 1) { print "d", d; dirs[d] = 1 } \
	    if ($$2 == $$4) files[d] = files[d] " " $$1; \
	    else { print "f", $$3 "/" $$4, $$1; } } \
	  END { for (d in files) print "f", d, files[d] }' | \
	while read type dir files; do \
	    if test "$$dir" = .; then dir=; else dir=/$$dir; fi; \
	    test -z "$$files" || { \
	      echo " $(INSTALL_PROGRAM_ENV) $(INSTALL_PROGRAM) $$files '$(DESTDIR)$(bindir)$$dir'"; \
	      $(INSTALL_PROGRAM_ENV) $(INSTALL_PROGRAM) $$files "$(DESTDIR)$(bindir)$$dir" || exit $$?; \
	    } \
	; done

uninstall-binPROGRAMS:
	@$(NORMAL_UNINSTALL)
	@list='$(bin_PROGRAMS)'; test -n "$(bindir)" || list=; \
	files=`for p in $$list; do echo "$$p"; done | \
	  sed -e 'h;s,^.*/,,;s/$(EXEEXT)$$//;$(transform)' \
	      -e 's/$$/$(EXEEXT)/' \
	`; \
	test -n "$$list" || exit 0; \
	echo " ( cd '$(DESTDIR)$(bindir)' && rm -f" $$files ")"; \
	cd "$(DESTDIR)$(bindir)" && rm -f $$files

clean-binPROGRAMS:
	-test -z "$(bin_PROGRAMS)" || rm -f $(bin_PROGRAMS)

lsacct$(EXEEXT): $(lsacct_OBJECTS) $(lsacct_DEPENDENCIES) $(EXTRA_lsacct_DEPENDENCIES) 
	@rm -f lsacct$(EXEEXT)
	$(AM_V_CCLD)$(LINK) $(lsacct_OBJECTS) $(lsacct_LDADD) $(LIBS)

lsaddhost$(EXEEXT): $(lsaddhost_OBJECTS) $(lsaddhost_DEPENDENCIES) $(EXTRA_lsaddhost_DEPENDENCIES) 
	@rm -f lsaddhost$(EXEEXT)
	$(AM_V_CCLD)$(LINK) $(lsaddhost_OBJECTS) $(lsaddhost_LDADD) $(LIBS)

lseligible$(EXEEXT): $(lseligible_OBJECTS) $(lseligible_DEPENDENCIES) $(EXTRA_lseligible_DEPENDENCIES) 
	@rm -f lseligible$(EXEEXT)
	$(AM_V_CCLD)$(LINK) $(lseligible_OBJECTS) $(lseligible_LDADD) $(LIBS)

lshosts$(EXEEXT): $(lshosts_OBJECTS) $(lshosts_DEPENDENCIES) $(EXTRA_lshosts_DEPENDENCIES) 
	@rm -f lshosts$(EXEEXT)
	$(AM_V_CCLD)$(LINK) $(lshosts_OBJECTS) $(lshosts_LDADD) $(LIBS)

lsid$(EXEEXT): $(lsid_OBJECTS) $(lsid_DEPENDENCIES) $(EXTRA_lsid_DEPENDENCIES) 
	@rm -f lsid$(EXEEXT)
	$(AM_V_CCLD)$(LINK) $(lsid_OBJECTS) $(lsid_LDADD) $(LIBS)

lsinfo$(EXEEXT): $(lsinfo_OBJECTS) $(lsinfo_DEPENDENCIES) $(EXTRA_lsinfo_DEPENDENCIES) 
	@rm -f lsinfo$(EXEEXT)
	$(AM_V_CCLD)$(LINK) $(lsinfo_OBJECTS) $(lsinfo_LDADD) $(LIBS)

lsload$(EXEEXT): $(lsload_OBJECTS) $(lsload_DEPENDENCIES) $(EXTRA_lsload_DEPENDENCIES) 
	@rm -f lsload$(EXEEXT)
	$(AM_V_CCLD)$(LINK) $(lsload_OBJECTS) $(lsload_LDADD) $(LIBS)

lsloadadj$(EXEEXT): $(lsloadadj_OBJECTS) $(lsloadadj_DEPENDENCIES) $(EXTRA_lsloadadj_DEPENDENCIES) 
	@rm -f lsloadadj$(EXEEXT)
	$(AM_V_CCLD)$(LINK) $(lsloadadj_OBJECTS) $(lsloadadj_LDADD) $(LIBS)

lsmon$(EXEEXT): $(lsmon_OBJECTS) $(lsmon_DEPENDENCIES) $(EXTRA_lsmon_DEPENDENCIES) 
	@rm -f lsmon$(EXEEXT)
	$(AM_V_CCLD)$(LINK) $(lsmon_OBJECTS) $(lsmon_LDADD) $(LIBS)

lsplace$(EXEEXT): $(lsplace_OBJECTS) $(lsplace_DEPENDENCIES) $(EXTRA_lsplace_DEPENDENCIES) 
	@rm -f lsplace$(EXEEXT)
	$(AM_V_CCLD)$(LINK) $(lsplace_OBJECTS) $(lsplace_LDADD) $(LIBS)

lsrcp$(EXEEXT): $(lsrcp_OBJECTS) $(lsrcp_DEPENDENCIES) $(EXTRA_lsrcp_DEPENDENCIES) 
	@rm -f lsrcp$(EXEEXT)
	$(AM_V_CCLD)$(LINK) $(lsrcp_OBJECTS) $(lsrcp_LDADD) $(LIBS)

lsrmhost$(EXEEXT): $(lsrmhost_OBJECTS) $(lsrmhost_DEPENDENCIES) $(EXTRA_lsrmhost_DEPENDENCIES) 
	@rm -f lsrmhost$(EXEEXT)
	$(AM_V_CCLD)$(LINK) $(lsrmhost_OBJECTS) $(lsrmhost_LDADD) $(LIBS)

lsrun$(EXEEXT): $(lsrun_OBJECTS) $(lsrun_DEPENDENCIES) $(EXTRA_lsrun_DEPENDENCIES) 
	@rm -f lsrun$(EXEEXT)
	$(AM_V_CCLD)$(LINK) $(lsrun_OBJECTS) $(lsrun_LDADD) $(LIBS)

mostlyclean-compile:
	-rm -f *.$(OBJEXT)

distclean-compile:
	-rm -f *.tab.c

include ./$(DEPDIR)/load.Po # am--include-marker
include ./$(DEPDIR)/lsacct.Po # am--include-marker
include ./$(DEPDIR)/lsaddhost.Po # am--include-marker
include ./$(DEPDIR)/lseligible.Po # am--include-marker
include ./$(DEPDIR)/lshosts.Po # am--include-marker
include ./$(DEPDIR)/lsid.Po # am--include-marker
include ./$(DEPDIR)/lsinfo.Po # am--include-marker
include ./$(DEPDIR)/lsload.Po # am--include-marker
include ./$(DEPDIR)/lsloadadj.Po # am--include-marker
include ./$(DEPDIR)/lsmon.Po # am--include-marker
include ./$(DEPDIR)/lsplace.Po # am--include-marker
include ./$(DEPDIR)/lsrcp.Po # am--include-marker
include ./$(DEPDIR)/lsrmhost.Po # am--include-marker
include ./$(DEPDIR)/lsrun.Po # am--include-marker

$(am__depfiles_remade):
	@$(MKDIR_P) $(@D)
	@echo '# dummy' >$@-t && $(am__mv) $@-t $@

am--depfiles: $(am__depfiles_remade)

.c.o:
	$(AM_V_CC)$(COMPILE) -MT $@ -MD -MP -MF $(DEPDIR)/$*.Tpo -c -o $@ $<
	$(AM_V_at)$(am__mv) $(DEPDIR)/$*.Tpo $(DEPDIR)/$*.Po
#	$(AM_V_CC)source='$<' object='$@' libtool=no \
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) \
#	$(AM_V_CC_no)$(COMPILE) -c -o $@ $<

.c.obj:
	$(AM_V_CC)$(COMPILE) -MT $@ -MD -MP -MF $(DEPDIR)/$*.Tpo -c -o $@ `$(CYGPATH_W) '$<'`
	$(AM_V_at)$(am__mv) $(DEPDIR)/$*.Tpo $(DEPDIR)/$*.Po
#	$(AM_V_CC)source='$<' object='$@' libtool=no \
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) \
#	$(AM_V_CC_no)$(COMPILE) -c -o $@ `$(CYGPATH_W) '$<'`

ID: $(am__tagged_files)
	$(am__define_uniq_tagged_files); mkid -fID $$unique
tags: tags-am
TAGS: tags

tags-am: $(TAGS_DEPENDENCIES) $(am__tagged_files)
	set x; \
	here=`pwd`; \
	$(am__define_uniq_tagged_files); \
	shift; \
	if test -z "$(ETAGS_ARGS)$$*$$unique"; then :; else \
	  test -n "$$unique" || unique=$$empty_fix; \
	  if test $$# -gt 0; then \
	    $(ETAGS) $(ETAGSFLAGS) $(AM_ETAGSFLAGS) $(ETAGS_ARGS) \
	      "$$@" $$unique; \
	  else \
	    $(ETAGS) $(ETAGSFLAGS) $(AM_ETAGSFLAGS) $(ETAGS_ARGS) \
	      $$unique; \
	  fi; \
	fi
ctags: ctags-am

CTAGS: ctags
ctags-am: $(TAGS_DEPENDENCIES) $(am__tagged_files)
	$(am__define_uniq_tagged_files); \
	test -z "$(CTAGS_ARGS)$$unique" \
	  || $(CTAGS) $(CTAGSFLAGS) $(AM_CTAGSFLAGS) $(CTAGS_ARGS) \
	     $$unique

GTAGS:
	here=`$(am__cd) $(top_builddir) && pwd` \
	  && $(am__cd) $(top_srcdir) \
	  && gtags -i $(GTAGS_ARGS) "$$here"
cscopelist: cscopelist-am

cscopelist-am: $(am__tagged_files)
	list='$(am__tagged_files)'; \
	case "$(srcdir)" in \
	  [\\/]* | ?:[\\/]*) sdir="$(srcdir)" ;; \
	  *) sdir=$(subdir)/$(srcdir) ;; \
	esac; \
	for i in $$list; do \
	  if test -f "$$i"; then \
	    echo "$(subdir)/$$i"; \
	  else \
	    echo "$$sdir/$$i"; \
	  fi; \
	done >> $(top_builddir)/cscope.files

distclean-tags:
	-rm -f TAGS ID GTAGS GRTAGS GSYMS GPATH tags

distdir: $(BUILT_SOURCES)
	$(MAKE) $(AM_MAKEFLAGS) distdir-am

distdir-am: $(DISTFILES)
	@srcdirstrip=`echo "$(srcdir)" | sed 's/[].[^$$\\*]/\\\\&/g'`; \
	topsrcdirstrip=`echo "$(top_srcdir)" | sed 's/[].[^$$\\*]/\\\\&/g'`; \
	list='$(DISTFILES)'; \
	  dist_files=`for file in $$list; do echo $$file; done | \
	  sed -e "s|^$$srcdirstrip/||;t" \
	      -e "s|^$$topsrcdirstrip/|$(top_builddir)/|;t"`; \
	case $$dist_files in \
	  */*) $(MKDIR_P) `echo "$$dist_files" | \
			   sed '/\//!d;s|^|$(distdir)/|;s,/[^/]*$$,,' | \
			   sort -u` ;; \
	esac; \
	for file in $$dist_files; do \
	  if test -f $$file || test -d $$file; then d=.; else d=$(srcdir); fi; \
	  if test -d $$d/$$file; then \
	    dir=`echo "/$$file" | sed -e 's,/[^/]*$$,,'`; \
	    if test -d "$(distdir)/$$file"; then \
	      find "$(distdir)/$$file" -type d ! -perm -700 -exec chmod u+rwx {} \;; \
	    fi; \
	    if test -d $(srcdir)/$$file && test $$d != $(srcdir); then \
	      cp -fpR $(srcdir)/$$file "$(distdir)$$dir" || exit 1; \
	      find "$(distdir)/$$file" -type d ! -perm -700 -exec chmod u+rwx {} \;; \
	    fi; \
	    cp -fpR $$d/$$file "$(distdir)$$dir" || exit 1; \
	  else \
	    test -f "$(distdir)/$$file" \
	    || cp -p $$d/$$file "$(distdir)/$$file" \
	    || exit 1; \
	  fi; \
	done
check-am: all-am
check: check-am
all-am: Makefile $(PROGRAMS)
installdirs:
	for dir in "$(DESTDIR)$(bindir)"; do \
	  test -z "$$dir" || $(MKDIR_P) "$$dir"; \
	done
install: install-am
install-exec: install-exec-am
install-data: install-data-am
uninstall: uninstall-am

install-am: all-am
	@$(MAKE) $(AM_MAKEFLAGS) install-exec-am install-data-am

installcheck: installcheck-am
install-strip:
	if test -z '$(STRIP)'; then \
	  $(MAKE) $(AM_MAKEFLAGS) INSTALL_PROGRAM="$(INSTALL_STRIP_PROGRAM)" \
	    install_sh_PROGRAM="$(INSTALL_STRIP_PROGRAM)" INSTALL_STRIP_FLAG=-s \
	      install; \
	else \
	  $(MAKE) $(AM_MAKEFLAGS) INSTALL_PROGRAM="$(INSTALL_STRIP_PROGRAM)" \
	    install_sh_PROGRAM="$(INSTALL_STRIP_PROGRAM)" INSTALL_STRIP_FLAG=-s \
	    "INSTALL_PROGRAM_ENV=STRIPPROG='$(STRIP)'" install; \
	fi
mostlyclean-generic:

clean-generic:

distclean-generic:
	-test -z "$(CONFIG_CLEAN_FILES)" || rm -f $(CONFIG_CLEAN_FILES)
	-test . = "$(srcdir)" || test -z "$(CONFIG_CLEAN_VPATH_FILES)" || rm -f $(CONFIG_CLEAN_VPATH_FILES)

maintainer-clean-generic:
	@echo "This command is intended for maintainers to use"
	@echo "it deletes files that may require special tools to rebuild."
clean: clean-am

clean-am: clean-binPROGRAMS clean-generic mostlyclean-am

distclean: distclean-am
		-rm -f ./$(DEPDIR)/load.Po
	-rm -f ./$(DEPDIR)/lsacct.Po
	-rm -f ./$(DEPDIR)/lsaddhost.Po
	-rm -f ./$(DEPDIR)/lseligible.Po
	-rm -f ./$(DEPDIR)/lshosts.Po
	-rm -f ./$(DEPDIR)/lsid.Po
	-rm -f ./$(DEPDIR)/lsinfo.Po
	-rm -f ./$(DEPDIR)/lsload.Po
	-rm -f ./$(DEPDIR)/lsloadadj.Po
	-rm -f ./$(DEPDIR)/lsmon.Po
	-rm -f ./$(DEPDIR)/lsplace.Po
	-rm -f ./$(DEPDIR)/lsrcp.Po
	-rm -f ./$(DEPDIR)/lsrmhost.Po
	-rm -f ./$(DEPDIR)/lsrun.Po
	-rm -f Makefile
distclean-am: clean-am distclean-compile distclean-generic \
	distclean-tags

dvi: dvi-am

dvi-am:

html: html-am

html-am:

info: info-am

info-am:

install-data-am:

install-dvi: install-dvi-am

install-dvi-am:

install-exec-am: install-binPROGRAMS

install-html: install-html-am

install-html-am:

install-info: install-info-am

install-info-am:

install-man:

install-pdf: install-pdf-am

install-pdf-am:

install-ps: install-ps-am

install-ps-am:

installcheck-am:

maintainer-clean: maintainer-clean-am
		-rm -f ./$(DEPDIR)/load.Po
	-rm -f ./$(DEPDIR)/lsacct.Po
	-rm -f ./$(DEPDIR)/lsaddhost.Po
	-rm -f ./$(DEPDIR)/lseligible.Po
	-rm -f ./$(DEPDIR)/lshosts.Po
	-rm -f ./$(DEPDIR)/lsid.Po
	-rm -f ./$(DEPDIR)/lsinfo.Po
	-rm -f ./$(DEPDIR)/lsload.Po
	-rm -f ./$(DEPDIR)/lsloadadj.Po
	-rm -f ./$(DEPDIR)/lsmon.Po
	-rm -f ./$(DEPDIR)/lsplace.Po
	-rm -f ./$(DEPDIR)/lsrcp.Po
	-rm -f ./$(DEPDIR)/lsrmhost.Po
	-rm -f ./$(DEPDIR)/lsrun.Po
	-rm -f Makefile
maintainer-clean-am: distclean-am maintainer-clean-generic

mostlyclean: mostlyclean-am

mostlyclean-am: mostlyclean-compile mostlyclean-generic

pdf: pdf-am

pdf-am:

ps: ps-am

ps-am:

uninstall-am: uninstall-binPROGRAMS

.MAKE: install-am install-strip

.PHONY: CTAGS GTAGS TAGS all all-am am--depfiles check check-am clean \
	clean-binPROGRAMS clean-generic cscopelist-am ctags ctags-am \
	distclean distclean-compile distclean-generic distclean-tags \
	distdir dvi dvi-am html html-am info info-am install \
	install-am install-binPROGRAMS install-data install-data-am \
	install-dvi install-dvi-am install-exec install-exec-am \
	install-html install-html-am install-info install-info-am \
	install-man install-pdf install-pdf-am install-ps \
	install-ps-am install-strip installcheck installcheck-am \
	installdirs maintainer-clean maintainer-clean-generic \
	mostlyclean mostlyclean-compile mostlyclean-generic pdf pdf-am \
	ps ps-am tags tags-am uninstall uninstall-am \
	uninstall-binPROGRAMS

.PRECIOUS: Makefile


etags :
	etags *.[hc] ../*.h ../lib/*.[hc] 

# Tell versions [3.59,3.63) of GNU make to not export all variables.
# Otherwise a system limit (for SysV at least) may be exceeded.
.NOEXPORT:
