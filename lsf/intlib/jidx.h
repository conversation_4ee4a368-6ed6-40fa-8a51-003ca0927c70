/* Copyright (C) 2021-2025 Bytedance Ltd. and/or its affiliates
 * $Id: jidx.h 397 2007-11-26 19:04:00Z mblack $
 * Copyright (C) 2007 Platform Computing Inc
 *
 * This program is free software; you can redistribute it and/or modify
 * it under the terms of version 2 of the GNU General Public License as
 * published by the Free Software Foundation.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.

 * You should have received a copy of the GNU General Public License
 * along with this program; if not, write to the Free Software
 * Foundation, Inc., 51 Franklin Street, Fifth Floor, Boston, MA  02110-1301, USA
 *
 */

#ifndef JIDX_H
#define JIDX_H

#define IDX_NOERR       0
#define IDX_BOUND       1
#define IDX_MEM         2

extern int idxerrno;

struct idxList {
    int             start;
    int             end;
    int             step;
    struct idxList *next;
};
extern int 	idxparse (struct idxList **, int *);
#endif
