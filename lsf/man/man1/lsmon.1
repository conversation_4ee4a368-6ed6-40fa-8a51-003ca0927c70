.ds ]W %
.ds ]L
.nh
.TH lsmon 1 "Lava Version 1.0 - Sept 2007"
.br
.SH NAME
\fBlsmon\fR - displays load information for Lava hosts and periodically updates the 
display
.SH SYNOPSIS
.BR
.PP
.PP
\fBlsmon\fR [\fB-N\fR\fI \fR| \fB-E\fR] [\fB-n\fR \fInum_hosts\fR] [\fB-R\fR \fIres_req\fR] [\fB-I\fR \fIindex_list\fR] [\fB-i\fR \fIinterval\fR] 
[\fB-L\fR \fIfile_name\fR] [\fIhost_name\fR ...]
.PP
\fBlsmon\fR [\fB-h\fR | \fB-V\fR]
.SH DESCRIPTION
.BR
.PP
.PP
\fB\fRlsmon is a full-screen Lava monitoring utility that displays and updates 
load information for hosts in a cluster. 
.PP
By default, displays load information for all hosts in the cluster, up to 
the number of lines that will fit on-screen. 
.PP
By default, displays raw load indices.
.PP
By default, load information is sorted according to CPU and paging 
load. 
.PP
By default, load information is updated every 10 seconds.
.SH OPTIONS
.BR
.PP
.TP 
\fB-N
\fR
.IP
Displays normalized CPU run queue length load indices (see 
lsfintro(1)). 


.TP 
\fB-E
\fR
.IP
Displays effective CPU run queue length load indices (see 
lsfintro(1)). Options -N and -E are mutually exclusive. 


.TP 
\fB-n\fR \fInum_hosts
\fR
.IP
Displays only load information for the requested number of hosts. 
Information for up to \fInum_hosts\fR hosts that best satisfy resource 
requirements is displayed. 


.TP 
\fB-R\fR\fI \fR\fIres_req\fR 

.IP
Displays only load information for hosts that satisfy the specified 
resource requirements. See lsinfo(1) for a list of built-in resource 
names. 

.IP
Load information for the hosts is sorted according to load on the 
specified resources. 

.IP
If \fIres_req\fR contains special resource names, only load information for 
hosts that provide these resources is displayed (see lshosts(1) to find 
out what resources are available on each host). 

.IP
If one or more host names are specified, only load information for the 
hosts that satisfy the resource requirements is displayed. 


.TP 
\fB-I\fR \fIindex_list
\fR
.IP
Displays only load information for the specified load indices. Load 
index names must be separated by a colon (for example, r1m:pg:ut). 

.IP
If the index list \fIindex_list\fR is too long to fit in the screen of the user 
who invoked the command, the output is truncated. For example, if the 
invoker's screen is 80 characters wide, then up to 10 load indices are 
displayed.


.TP 
\fB-i\fR \fIinterval
\fR
.IP
Sets how often load information is updated on-screen, in seconds.


.TP 
\fB-L\fR \fIfile_name
\fR
.IP
Saves load information in the specified file while it is displayed on-
screen. 

.IP
If you do not want load information to be displayed on your screen at 
the same time, use \fBlsmon -L\fR \fIfile_name\fR \fB< /dev/null\fR. The format 
of the file is described in lim.acct(5).


.TP 
\fIhost_name\fR ...

.IP
Displays only load information for the specified hosts. 


.TP 
\fB-h
\fR
.IP
Prints command usage to stderr and exits. 


.TP 
\fB-V\fR 

.IP
Prints Lava release version to stderr and exits.


.SH USAGE
.BR
.PP
.PP
You can use the following commands while lsmon is running:
.PP
[\fB^L\fR |\fB i\fR | \fBn\fR | \fBN\fR | \fBE\fR | \fBR\fR | \fBq\fR]
.TP 
\fB^L
\fR
.IP
Refreshes the screen.


.TP 
\fBi
\fR
.IP
Prompts you to input a new update interval.


.TP 
\fBn
\fR
.IP
Prompts you to input a new number of hosts to display.


.TP 
\fBN
\fR
.IP
Toggles between displaying raw CPU run queue length load indices 
and normalized CPU run queue length load indices.


.TP 
\fBE
\fR
.IP
Toggles between displaying raw CPU run queue length load indices 
and effective CPU run queue length load indices.


.TP 
\fBR
\fR
.IP
Prompts you to input new resource requirements.


.TP 
\fBq
\fR
.IP
Quits lsmon.


.SH OUTPUT
.BR
.PP
.PP
The following fields are displayed by default. 

.IP
HOST_NAME
.BR
.RS
.IP
Name of specified hosts for which load information is displayed, or 
if resource requirements were specified, name of hosts that satisfied 
the specified resource requirement and for which load information 
is displayed. 

.RE

.IP
status
.BR
.RS
.IP
Status of the host. A minus sign (-) may precede the status, 
indicating that the Remote Execution Server (RES) on the host is not 
running. 

.IP
Possible statuses are: 


.IP
ok 
.BR
.RS
.IP
The host is in normal load sharing state and can accept remote 
jobs. 

.RE

.IP
busy
.BR
.RS
.IP
The host is overloaded because some load indices exceed 
configured thresholds. Load index values that caused the host 
to be busy are preceded by an asterisk (*). Built-in load indices 
include r15s, r1m, r15m, ut, pg, io, ls, it, swp, mem and tmp 
(see below). External load indices are configured in the file 
lsf.cluster.\fIcluster_name\fR (see lsf.cluster(5)). 

.RE

.IP
lockW 
.BR
.RS
.IP
The host is locked by its run window. Run windows for a host 
are specified in the configuration file (see lsf.conf(5)) and 
can be displayed by lshosts. A locked host will not accept 
load shared jobs from other hosts. 

.RE

.IP
lockU 
.BR
.RS
.IP
The host is locked by the Lava administrator or root. 

.RE

.IP
unavail 
.BR
.RS
.IP
The host is down or the Load Information Manager (LIM) on 
the host is not running. 

.RE

.IP
.RE

.IP
r15s
.BR
.RS
.IP
The 15-second exponentially averaged CPU run queue length.

.RE

.IP
r1m
.BR
.RS
.IP
The 1-minute exponentially averaged CPU run queue length.

.RE

.IP
r15m
.BR
.RS
.IP
The 15-minute exponentially averaged CPU run queue length.

.RE

.IP
ut
.BR
.RS
.IP
The CPU utilization exponentially averaged over the last minute, 
between 0 and 1.

.RE

.IP
pg
.BR
.RS
.IP
The memory paging rate exponentially averaged over the last 
minute, in pages per second.

.RE

.IP
ls
.BR
.RS
.IP
The number of current login users.

.RE

.IP
it
.BR
.RS
.IP
On UNIX, the idle time of the host (keyboard not touched on all 
logged in sessions), in minutes.
.RE

.IP
tmp
.BR
.RS
.IP
The amount of free space in /tmp, in megabytes.

.RE

.IP
swp
.BR
.RS
.IP
The amount of currently available swap space, in megabytes.

.RE

.IP
mem
.BR
.RS
.IP
The amount of currently available memory, in megabytes.

.RE
.SH SEE ALSO
.BR
.PP
.PP
lsfintro(1), lshosts(1), lsinfo(1), lsload(1), 
lslockhost(8), lim.acct(5), ls_load(3)
.SH DIAGNOSTICS
.BR
.PP
.PP
Specifying an invalid resource requirement string while lsmon is 
running (via the R option) causes lsmon to exit with an appropriate 
error message.
.PP
lsmon exits if it does not receive a reply from LIM within the update 
interval.
