.\" $Id: nios.8,v 1.1 2007/07/20 16:17:07 cchen Exp $
.ds ]W %
.ds ]L
.TH NIOS 8 "1 August 1998"
.SH NAME
nios \- network I/O server for the \s-1LSF\s0 (Load Sharing Facility) system
.SH SYNOPSIS
.B LSF_SERVDIR/nios
.SH DESCRIPTION
.B nios
is an application server process that is started by the \s-1LSF\s0 library 
\s-1LSLIB\s0 when
remote execution is first initiated. \fBnios\fR runs concurrently with the
client application, transparently handling all I/O and signals to/from
the remote tasks. The \fBnios\fR is a per-application server process,
that is, if there is more than one remote task belonging to the same
application, the tasks all share the
same \fBnios\fR on the client host.
\fBnios\fR terminates when the application terminates.
\fBnios\fR exits when the application exits.
.PP
Most application programmers do not need to know that there is
a \fBnios\fR process running. Applications call
.BR ls_rtask (3)
to start remote processes and call
.BR ls_rwait (3)
to collect the remote process status. When a remote task finishes,
a SIGUSR1 signal is sent to the client application by the \fBnios\fR. The
application can then call
.BR ls_rwait (3)
to get a remote child's status from the \fBnios\fR.
Remote tasks are identified by Task IDs (TID).
.BR ls_rwait (3)
returns the TID as well as the status of the remote child that has
finished.
.TP 5
.B I/O Shuffling
\fBnios\fR is the I/O agent for all the remote tasks of an application.
By default, \fBnios\fR reads from stdin and forwards the stream to all
remote tasks. \fBnios\fR prints the output from all remote tasks to stdout and
stderr. It is possible to disable or enable the stdin of a particular
remote task by calling
.BR ls_setstdin (3).
This allows stdin to be forwarded to the selected remote tasks.
It is also possible to disable or enable reading of stdin by
remote tasks.
This is necessary for some applications such as a distributed
shell, where the shell reads a command line from the stdin, and
dispatches the task to remote hosts for execution, during which the
remote task must read the stdin.
.TP 5
.B TTY Mode
When a distributed application is running on remote hosts, \fBnios\fR
can switch the tty  back and forth between local mode and remote mode.
In local mode, all control keys are interpreted locally. In remote mode
the input stream is delivered directly to remote tasks.
Users normally do not need to know these details.
.TP 5
.B Job Control
\fBnios\fR transparently supports job control.  It catches signals (such as
SIGTSTP, SIGINT, and SIGTERM) and passes them to remote tasks. Therefore,
when the user presses job control keys such as Ctrl-Z or Ctrl-C, remote
tasks are sent the SIGTSTP or SIGINT signals. With the support of \fBnios\fR,
remote tasks can be suspended or resumed, put to the background or
foreground, and killed, as if they were running locally.
.TP 5
.B nios\-Client Coordination
For applications that need pseudo-terminals on remote hosts (see
.BR ls_rtask (3)),
care must be
taken in synchronizing the tty's local/remote mode setting. When the
client is stopped due to a SIGTSTP signal, it has to make sure that
\fBnios\fR
is stopped first by calling
.BR ls_stoprex (3).
In most cases, this is already
handled by the \s-1LSF\s0 library automatically in the default SIGTSTP handler.
If you define this signal handler explicitly in your program, you must
call
.BR ls_stoprex (3)
in cases where the client wants to be stopped. Failure to do so may
cause some temporary misbehavior in the terminal
environment immediately after stopping a remote interactive job. All
applications that use pseudo-ttys for remote tasks must call
.BR ls_exit (3)
before exiting in order to ensure that the local terminal environment is restored
correctly.
.SH NOTE
\fBnios\fR must be installed in directory \fBLSF_SERVERDIR\fR as defined in
the file
.BR lsf.conf (5)
or as an environment variable.
.SH "SEE ALSO"
.BR lsf.conf (5),
.BR res (8),
.BR ls_rtask (3),
.BR ls_rwait (3),
.BR ls_setstdin (3),
.BR ls_stoprex (3),
.BR ls_exit (3),
.BR lslib (3)
