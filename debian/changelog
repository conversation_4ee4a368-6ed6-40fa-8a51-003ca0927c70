volclava (2.0-0.b.20250616) UNRELEASED; urgency=medium

  * support fairshare scheduling policy for users at queue level;
  * support customize unit by configure LSF_UNIT_FOR_LIMITS in lsf.conf;
  * fix loadstop not effect job scheduling;
  * fix memory leak in bhosts and putEnv();
  * fix pim hang when format of pim.info file is not in volclava format;
  * fix sbd hang due to unnecessary popen;
  * fix lsload -w does not display full hostname;
  * fix mbatchd coredump when only suspend jobs remain;
  * fix mem be reserved repeatedly after bresume ususp/ssusp job;
  * fix running jobs exceed the number of slots due to shared type resource when slotResourceReserve=y;

 -- Mingze Li <<EMAIL>>  Mon, 16 Jun 2025 11:57:00 +0800

volclava (1.0-1.b.20250226) UNRELEASED; urgency=medium

  * lsb.users: support MAX_PEND_JOBS and MAX_PEND_SLOTS;
  * lsb.params: support MAX_PEND_JOBS and MAX_PEND_SLOTS and SUB_TRY_INTERVAL;
  * busers: display MPEND as MAX_PEND_SLOTS defined in lsb.users, display PJOBS
    as statistic of user pend job, display MPJOBS as MAX_PEND_SLOTS defined in lsb.users;
  * bparams: display MAX_PEND_JOBS and MAX_PEND_SLOTS and SUB_TRY_INTERVAL defined in lsb.params;
  * bsub: add retry when submit job is limited by MAX_PEND_JOBS or MAX_PEND_SLOTS;
  * support resource reserve as per-host

 -- Mingze Li <<EMAIL>>  Wed, 26 Feb 2025 17:17:18 +0800

volclava (1.0-1.b.20250106) UNRELEASED; urgency=medium

  * bugfix: revert check for tcl result with function in tcl 8.6;
  * configure: set default limit from cpu to cpu>=0 in lsf.task;
  * update year of Copyright from 2021-2024 to 2021-2025;
  * bsub: enhance parameter check of bsub -R;
  * make source-script active in multiple volclava on the same host;
  * make install script support ubuntu OS;
 -- Mingze Li <<EMAIL>>  Mon, 06 Jan 2025 15:57:18 +0800

volclava (1.0-1.b.20241126) UNRELEASED; urgency=medium

  * Multiple feature support: bjobs -UF; bjobs -o/-json; bsub -pack; bsub -Ep; etc.
  * Multiple bugfix: MXJ not equal with maxCpus when set "!"; lshosts -l segmentation fault;
    sbatchd block by greater than 1000 jobs; prefix not work in rpm install to costomize directory;
    fix job slot limit reached while host is free.
  * Define new project name as volclava in related files.
  * Adapt to Ubuntu 20.04 and Rocky 8.10.
    support ubuntu/debian with tcl 8.6 and glibc 2.31-0;
    bugfix: fix bjobs show nothing with no jobs;
    bugfix: remove ls_syslog in sbd child_handler due to dead lock;
 -- Mingze Li <<EMAIL>>  Mon, 11 Nov 2024 10:57:18 +0800