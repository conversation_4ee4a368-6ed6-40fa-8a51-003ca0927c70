#!/bin/sh

# Set up a compute node.

#
# exit if LSF_ENVDIR is not set
#
if [ -z "$LSF_ENVDIR" ]; then
    echo "LSF_ENVDIR is not set. Exiting..."
    exit 1
fi

# source the lsf.conf file
if [ ! -f "$LSF_ENVDIR/lsf.conf" ]; then
    echo "$LSF_ENVDIR/lsf.conf not found. Exiting..."
    exit 1
fi

. $LSF_ENVDIR/lsf.conf

# install the volclava startup file
cp $LSF_ENVDIR/volclava /etc/init.d

cp $LSF_ENVDIR/volclava.sh /etc/profile.d
cp $LSF_ENVDIR/volclava.csh /etc/profile.d

# configure the lava service to start at boot
/sbin/chkconfig --add volclava
/sbin/chkconfig volclava on
