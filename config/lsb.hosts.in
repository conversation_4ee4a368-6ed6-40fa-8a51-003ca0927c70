#
# The section "host" is optional.  If no hosts are listed here, all hosts 
# known by LSF will be used by <PERSON><PERSON>.  Otherwise only the hosts listed will 
# be used by <PERSON><PERSON>.  The value of keyword HOST_NAME may be an official host
# name (see gethostbyname(3)), a host type/model name (see lsf.shared(5)), or
# the reserved word "default".  The type/model name represents each of the 
# hosts which are of that particular host type/model.  The reserved
# word "default" represents all other hosts in the LSF cluster. 

# MXJ is the maximum number of jobs which can run on the host at one time.
# JL/U is the maximum number of jobs belonging to a user that can run on the 
# host at one time.  The default is no limit.

# DISPATCH_WINDOW is the time windows when the host is available to run
# batch jobs.  The default dispatch window is always open.

# Other columns specify scheduling and stopping thresholds for LIM load 
# indices.  A "()" or "-" is used to specify the default value in a column 
# and cannot be omitted.

# All the host names (except default) in this example are commented out,
# since they are just examples which may not be suitable for some sites.
# Don't use non-default thresholds unless job dispatch needs to be controlled.

Begin Host
HOST_NAME     MXJ JL/U   r1m    pg    ls     tmp  DISPATCH_WINDOW  # Keywords
#host0        1    1   3.5/4.5  15/   12/15  0      ()		   # Example
#host1       ()   2     3.5  15/18   12/    0/  (5:19:00-1:8:30 20:00-8:30)
#host2        ()   ()   3.5/5   18    15     ()     ()		   # Example
default       !   ()     ()    ()    ()     ()     ()		   # Example
End Host

# Host groups can be referenced by the queue file.  Each line defines a host
# group.  The first line contains key words; each subsequent line contains a 
# group name, followed by white space, followed by the list of group members.
# The list of members should be enclosed in parentheses and separated by white 
# space.  This section is optional.

# This example is commented out
#Begin HostGroup
#GROUP_NAME    GROUP_MEMBER	# Key words
#group0        (host0 host1)	# Define a host group
#End HostGroup

