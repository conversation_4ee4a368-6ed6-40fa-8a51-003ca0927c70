m4trace:aclocal.m4:679: -1- AC_SUBST([am__quote])
m4trace:aclocal.m4:679: -1- AC_SUBST_TRACE([am__quote])
m4trace:aclocal.m4:679: -1- m4_pattern_allow([^am__quote$])
m4trace:configure.ac:7: -1- AC_INIT([volclava], [2.0])
m4trace:configure.ac:7: -1- m4_pattern_forbid([^_?A[CHUM]_])
m4trace:configure.ac:7: -1- m4_pattern_forbid([_AC_])
m4trace:configure.ac:7: -1- m4_pattern_forbid([^LIBOBJS$], [do not use LIBOBJS directly, use AC_LIBOBJ (see section `AC_LIBOBJ vs LIBOBJS'])
m4trace:configure.ac:7: -1- m4_pattern_allow([^AS_FLAGS$])
m4trace:configure.ac:7: -1- m4_pattern_forbid([^_?m4_])
m4trace:configure.ac:7: -1- m4_pattern_forbid([^dnl$])
m4trace:configure.ac:7: -1- m4_pattern_forbid([^_?AS_])
m4trace:configure.ac:7: -1- AC_SUBST([SHELL])
m4trace:configure.ac:7: -1- AC_SUBST_TRACE([SHELL])
m4trace:configure.ac:7: -1- m4_pattern_allow([^SHELL$])
m4trace:configure.ac:7: -1- AC_SUBST([PATH_SEPARATOR])
m4trace:configure.ac:7: -1- AC_SUBST_TRACE([PATH_SEPARATOR])
m4trace:configure.ac:7: -1- m4_pattern_allow([^PATH_SEPARATOR$])
m4trace:configure.ac:7: -1- AC_SUBST([PACKAGE_NAME], [m4_ifdef([AC_PACKAGE_NAME],      ['AC_PACKAGE_NAME'])])
m4trace:configure.ac:7: -1- AC_SUBST_TRACE([PACKAGE_NAME])
m4trace:configure.ac:7: -1- m4_pattern_allow([^PACKAGE_NAME$])
m4trace:configure.ac:7: -1- AC_SUBST([PACKAGE_TARNAME], [m4_ifdef([AC_PACKAGE_TARNAME],   ['AC_PACKAGE_TARNAME'])])
m4trace:configure.ac:7: -1- AC_SUBST_TRACE([PACKAGE_TARNAME])
m4trace:configure.ac:7: -1- m4_pattern_allow([^PACKAGE_TARNAME$])
m4trace:configure.ac:7: -1- AC_SUBST([PACKAGE_VERSION], [m4_ifdef([AC_PACKAGE_VERSION],   ['AC_PACKAGE_VERSION'])])
m4trace:configure.ac:7: -1- AC_SUBST_TRACE([PACKAGE_VERSION])
m4trace:configure.ac:7: -1- m4_pattern_allow([^PACKAGE_VERSION$])
m4trace:configure.ac:7: -1- AC_SUBST([PACKAGE_STRING], [m4_ifdef([AC_PACKAGE_STRING],    ['AC_PACKAGE_STRING'])])
m4trace:configure.ac:7: -1- AC_SUBST_TRACE([PACKAGE_STRING])
m4trace:configure.ac:7: -1- m4_pattern_allow([^PACKAGE_STRING$])
m4trace:configure.ac:7: -1- AC_SUBST([PACKAGE_BUGREPORT], [m4_ifdef([AC_PACKAGE_BUGREPORT], ['AC_PACKAGE_BUGREPORT'])])
m4trace:configure.ac:7: -1- AC_SUBST_TRACE([PACKAGE_BUGREPORT])
m4trace:configure.ac:7: -1- m4_pattern_allow([^PACKAGE_BUGREPORT$])
m4trace:configure.ac:7: -1- AC_SUBST([PACKAGE_URL], [m4_ifdef([AC_PACKAGE_URL],       ['AC_PACKAGE_URL'])])
m4trace:configure.ac:7: -1- AC_SUBST_TRACE([PACKAGE_URL])
m4trace:configure.ac:7: -1- m4_pattern_allow([^PACKAGE_URL$])
m4trace:configure.ac:7: -1- AC_SUBST([exec_prefix], [NONE])
m4trace:configure.ac:7: -1- AC_SUBST_TRACE([exec_prefix])
m4trace:configure.ac:7: -1- m4_pattern_allow([^exec_prefix$])
m4trace:configure.ac:7: -1- AC_SUBST([prefix], [NONE])
m4trace:configure.ac:7: -1- AC_SUBST_TRACE([prefix])
m4trace:configure.ac:7: -1- m4_pattern_allow([^prefix$])
m4trace:configure.ac:7: -1- AC_SUBST([program_transform_name], [s,x,x,])
m4trace:configure.ac:7: -1- AC_SUBST_TRACE([program_transform_name])
m4trace:configure.ac:7: -1- m4_pattern_allow([^program_transform_name$])
m4trace:configure.ac:7: -1- AC_SUBST([bindir], ['${exec_prefix}/bin'])
m4trace:configure.ac:7: -1- AC_SUBST_TRACE([bindir])
m4trace:configure.ac:7: -1- m4_pattern_allow([^bindir$])
m4trace:configure.ac:7: -1- AC_SUBST([sbindir], ['${exec_prefix}/sbin'])
m4trace:configure.ac:7: -1- AC_SUBST_TRACE([sbindir])
m4trace:configure.ac:7: -1- m4_pattern_allow([^sbindir$])
m4trace:configure.ac:7: -1- AC_SUBST([libexecdir], ['${exec_prefix}/libexec'])
m4trace:configure.ac:7: -1- AC_SUBST_TRACE([libexecdir])
m4trace:configure.ac:7: -1- m4_pattern_allow([^libexecdir$])
m4trace:configure.ac:7: -1- AC_SUBST([datarootdir], ['${prefix}/share'])
m4trace:configure.ac:7: -1- AC_SUBST_TRACE([datarootdir])
m4trace:configure.ac:7: -1- m4_pattern_allow([^datarootdir$])
m4trace:configure.ac:7: -1- AC_SUBST([datadir], ['${datarootdir}'])
m4trace:configure.ac:7: -1- AC_SUBST_TRACE([datadir])
m4trace:configure.ac:7: -1- m4_pattern_allow([^datadir$])
m4trace:configure.ac:7: -1- AC_SUBST([sysconfdir], ['${prefix}/etc'])
m4trace:configure.ac:7: -1- AC_SUBST_TRACE([sysconfdir])
m4trace:configure.ac:7: -1- m4_pattern_allow([^sysconfdir$])
m4trace:configure.ac:7: -1- AC_SUBST([sharedstatedir], ['${prefix}/com'])
m4trace:configure.ac:7: -1- AC_SUBST_TRACE([sharedstatedir])
m4trace:configure.ac:7: -1- m4_pattern_allow([^sharedstatedir$])
m4trace:configure.ac:7: -1- AC_SUBST([localstatedir], ['${prefix}/var'])
m4trace:configure.ac:7: -1- AC_SUBST_TRACE([localstatedir])
m4trace:configure.ac:7: -1- m4_pattern_allow([^localstatedir$])
m4trace:configure.ac:7: -1- AC_SUBST([runstatedir], ['${localstatedir}/run'])
m4trace:configure.ac:7: -1- AC_SUBST_TRACE([runstatedir])
m4trace:configure.ac:7: -1- m4_pattern_allow([^runstatedir$])
m4trace:configure.ac:7: -1- AC_SUBST([includedir], ['${prefix}/include'])
m4trace:configure.ac:7: -1- AC_SUBST_TRACE([includedir])
m4trace:configure.ac:7: -1- m4_pattern_allow([^includedir$])
m4trace:configure.ac:7: -1- AC_SUBST([oldincludedir], ['/usr/include'])
m4trace:configure.ac:7: -1- AC_SUBST_TRACE([oldincludedir])
m4trace:configure.ac:7: -1- m4_pattern_allow([^oldincludedir$])
m4trace:configure.ac:7: -1- AC_SUBST([docdir], [m4_ifset([AC_PACKAGE_TARNAME],
				     ['${datarootdir}/doc/${PACKAGE_TARNAME}'],
				     ['${datarootdir}/doc/${PACKAGE}'])])
m4trace:configure.ac:7: -1- AC_SUBST_TRACE([docdir])
m4trace:configure.ac:7: -1- m4_pattern_allow([^docdir$])
m4trace:configure.ac:7: -1- AC_SUBST([infodir], ['${datarootdir}/info'])
m4trace:configure.ac:7: -1- AC_SUBST_TRACE([infodir])
m4trace:configure.ac:7: -1- m4_pattern_allow([^infodir$])
m4trace:configure.ac:7: -1- AC_SUBST([htmldir], ['${docdir}'])
m4trace:configure.ac:7: -1- AC_SUBST_TRACE([htmldir])
m4trace:configure.ac:7: -1- m4_pattern_allow([^htmldir$])
m4trace:configure.ac:7: -1- AC_SUBST([dvidir], ['${docdir}'])
m4trace:configure.ac:7: -1- AC_SUBST_TRACE([dvidir])
m4trace:configure.ac:7: -1- m4_pattern_allow([^dvidir$])
m4trace:configure.ac:7: -1- AC_SUBST([pdfdir], ['${docdir}'])
m4trace:configure.ac:7: -1- AC_SUBST_TRACE([pdfdir])
m4trace:configure.ac:7: -1- m4_pattern_allow([^pdfdir$])
m4trace:configure.ac:7: -1- AC_SUBST([psdir], ['${docdir}'])
m4trace:configure.ac:7: -1- AC_SUBST_TRACE([psdir])
m4trace:configure.ac:7: -1- m4_pattern_allow([^psdir$])
m4trace:configure.ac:7: -1- AC_SUBST([libdir], ['${exec_prefix}/lib'])
m4trace:configure.ac:7: -1- AC_SUBST_TRACE([libdir])
m4trace:configure.ac:7: -1- m4_pattern_allow([^libdir$])
m4trace:configure.ac:7: -1- AC_SUBST([localedir], ['${datarootdir}/locale'])
m4trace:configure.ac:7: -1- AC_SUBST_TRACE([localedir])
m4trace:configure.ac:7: -1- m4_pattern_allow([^localedir$])
m4trace:configure.ac:7: -1- AC_SUBST([mandir], ['${datarootdir}/man'])
m4trace:configure.ac:7: -1- AC_SUBST_TRACE([mandir])
m4trace:configure.ac:7: -1- m4_pattern_allow([^mandir$])
m4trace:configure.ac:7: -1- AC_DEFINE_TRACE_LITERAL([PACKAGE_NAME])
m4trace:configure.ac:7: -1- m4_pattern_allow([^PACKAGE_NAME$])
m4trace:configure.ac:7: -1- AH_OUTPUT([PACKAGE_NAME], [/* Define to the full name of this package. */
@%:@undef PACKAGE_NAME])
m4trace:configure.ac:7: -1- AC_DEFINE_TRACE_LITERAL([PACKAGE_TARNAME])
m4trace:configure.ac:7: -1- m4_pattern_allow([^PACKAGE_TARNAME$])
m4trace:configure.ac:7: -1- AH_OUTPUT([PACKAGE_TARNAME], [/* Define to the one symbol short name of this package. */
@%:@undef PACKAGE_TARNAME])
m4trace:configure.ac:7: -1- AC_DEFINE_TRACE_LITERAL([PACKAGE_VERSION])
m4trace:configure.ac:7: -1- m4_pattern_allow([^PACKAGE_VERSION$])
m4trace:configure.ac:7: -1- AH_OUTPUT([PACKAGE_VERSION], [/* Define to the version of this package. */
@%:@undef PACKAGE_VERSION])
m4trace:configure.ac:7: -1- AC_DEFINE_TRACE_LITERAL([PACKAGE_STRING])
m4trace:configure.ac:7: -1- m4_pattern_allow([^PACKAGE_STRING$])
m4trace:configure.ac:7: -1- AH_OUTPUT([PACKAGE_STRING], [/* Define to the full name and version of this package. */
@%:@undef PACKAGE_STRING])
m4trace:configure.ac:7: -1- AC_DEFINE_TRACE_LITERAL([PACKAGE_BUGREPORT])
m4trace:configure.ac:7: -1- m4_pattern_allow([^PACKAGE_BUGREPORT$])
m4trace:configure.ac:7: -1- AH_OUTPUT([PACKAGE_BUGREPORT], [/* Define to the address where bug reports for this package should be sent. */
@%:@undef PACKAGE_BUGREPORT])
m4trace:configure.ac:7: -1- AC_DEFINE_TRACE_LITERAL([PACKAGE_URL])
m4trace:configure.ac:7: -1- m4_pattern_allow([^PACKAGE_URL$])
m4trace:configure.ac:7: -1- AH_OUTPUT([PACKAGE_URL], [/* Define to the home page for this package. */
@%:@undef PACKAGE_URL])
m4trace:configure.ac:7: -1- AC_SUBST([DEFS])
m4trace:configure.ac:7: -1- AC_SUBST_TRACE([DEFS])
m4trace:configure.ac:7: -1- m4_pattern_allow([^DEFS$])
m4trace:configure.ac:7: -1- AC_SUBST([ECHO_C])
m4trace:configure.ac:7: -1- AC_SUBST_TRACE([ECHO_C])
m4trace:configure.ac:7: -1- m4_pattern_allow([^ECHO_C$])
m4trace:configure.ac:7: -1- AC_SUBST([ECHO_N])
m4trace:configure.ac:7: -1- AC_SUBST_TRACE([ECHO_N])
m4trace:configure.ac:7: -1- m4_pattern_allow([^ECHO_N$])
m4trace:configure.ac:7: -1- AC_SUBST([ECHO_T])
m4trace:configure.ac:7: -1- AC_SUBST_TRACE([ECHO_T])
m4trace:configure.ac:7: -1- m4_pattern_allow([^ECHO_T$])
m4trace:configure.ac:7: -1- AC_SUBST([LIBS])
m4trace:configure.ac:7: -1- AC_SUBST_TRACE([LIBS])
m4trace:configure.ac:7: -1- m4_pattern_allow([^LIBS$])
m4trace:configure.ac:7: -1- AC_SUBST([build_alias])
m4trace:configure.ac:7: -1- AC_SUBST_TRACE([build_alias])
m4trace:configure.ac:7: -1- m4_pattern_allow([^build_alias$])
m4trace:configure.ac:7: -1- AC_SUBST([host_alias])
m4trace:configure.ac:7: -1- AC_SUBST_TRACE([host_alias])
m4trace:configure.ac:7: -1- m4_pattern_allow([^host_alias$])
m4trace:configure.ac:7: -1- AC_SUBST([target_alias])
m4trace:configure.ac:7: -1- AC_SUBST_TRACE([target_alias])
m4trace:configure.ac:7: -1- m4_pattern_allow([^target_alias$])
m4trace:configure.ac:8: -1- AC_CONFIG_HEADERS([config.h])
m4trace:configure.ac:13: -1- AC_SUBST([volclavadmin], [volclava])
m4trace:configure.ac:13: -1- AC_SUBST_TRACE([volclavadmin])
m4trace:configure.ac:13: -1- m4_pattern_allow([^volclavadmin$])
m4trace:configure.ac:14: -1- AC_SUBST([volclavaadmin])
m4trace:configure.ac:14: -1- AC_SUBST_TRACE([volclavaadmin])
m4trace:configure.ac:14: -1- m4_pattern_allow([^volclavaadmin$])
m4trace:configure.ac:15: -1- AC_SUBST([volclavacluster], [volclava])
m4trace:configure.ac:15: -1- AC_SUBST_TRACE([volclavacluster])
m4trace:configure.ac:15: -1- m4_pattern_allow([^volclavacluster$])
m4trace:configure.ac:16: -1- AC_SUBST([volclavacluster])
m4trace:configure.ac:16: -1- AC_SUBST_TRACE([volclavacluster])
m4trace:configure.ac:16: -1- m4_pattern_allow([^volclavacluster$])
m4trace:configure.ac:19: -1- AC_CANONICAL_SYSTEM
m4trace:configure.ac:19: -1- _m4_warn([obsolete], [The macro `AC_CANONICAL_SYSTEM' is obsolete.
You should run autoupdate.], [../../lib/autoconf/general.m4:1868: AC_CANONICAL_SYSTEM is expanded from...
configure.ac:19: the top level])
m4trace:configure.ac:19: -1- AC_CANONICAL_TARGET
m4trace:configure.ac:19: -1- AC_CANONICAL_HOST
m4trace:configure.ac:19: -1- AC_CANONICAL_BUILD
m4trace:configure.ac:19: -1- AC_REQUIRE_AUX_FILE([config.sub])
m4trace:configure.ac:19: -1- AC_REQUIRE_AUX_FILE([config.guess])
m4trace:configure.ac:19: -1- AC_SUBST([build], [$ac_cv_build])
m4trace:configure.ac:19: -1- AC_SUBST_TRACE([build])
m4trace:configure.ac:19: -1- m4_pattern_allow([^build$])
m4trace:configure.ac:19: -1- AC_SUBST([build_cpu], [$[1]])
m4trace:configure.ac:19: -1- AC_SUBST_TRACE([build_cpu])
m4trace:configure.ac:19: -1- m4_pattern_allow([^build_cpu$])
m4trace:configure.ac:19: -1- AC_SUBST([build_vendor], [$[2]])
m4trace:configure.ac:19: -1- AC_SUBST_TRACE([build_vendor])
m4trace:configure.ac:19: -1- m4_pattern_allow([^build_vendor$])
m4trace:configure.ac:19: -1- AC_SUBST([build_os])
m4trace:configure.ac:19: -1- AC_SUBST_TRACE([build_os])
m4trace:configure.ac:19: -1- m4_pattern_allow([^build_os$])
m4trace:configure.ac:19: -1- AC_SUBST([host], [$ac_cv_host])
m4trace:configure.ac:19: -1- AC_SUBST_TRACE([host])
m4trace:configure.ac:19: -1- m4_pattern_allow([^host$])
m4trace:configure.ac:19: -1- AC_SUBST([host_cpu], [$[1]])
m4trace:configure.ac:19: -1- AC_SUBST_TRACE([host_cpu])
m4trace:configure.ac:19: -1- m4_pattern_allow([^host_cpu$])
m4trace:configure.ac:19: -1- AC_SUBST([host_vendor], [$[2]])
m4trace:configure.ac:19: -1- AC_SUBST_TRACE([host_vendor])
m4trace:configure.ac:19: -1- m4_pattern_allow([^host_vendor$])
m4trace:configure.ac:19: -1- AC_SUBST([host_os])
m4trace:configure.ac:19: -1- AC_SUBST_TRACE([host_os])
m4trace:configure.ac:19: -1- m4_pattern_allow([^host_os$])
m4trace:configure.ac:19: -1- AC_SUBST([target], [$ac_cv_target])
m4trace:configure.ac:19: -1- AC_SUBST_TRACE([target])
m4trace:configure.ac:19: -1- m4_pattern_allow([^target$])
m4trace:configure.ac:19: -1- AC_SUBST([target_cpu], [$[1]])
m4trace:configure.ac:19: -1- AC_SUBST_TRACE([target_cpu])
m4trace:configure.ac:19: -1- m4_pattern_allow([^target_cpu$])
m4trace:configure.ac:19: -1- AC_SUBST([target_vendor], [$[2]])
m4trace:configure.ac:19: -1- AC_SUBST_TRACE([target_vendor])
m4trace:configure.ac:19: -1- m4_pattern_allow([^target_vendor$])
m4trace:configure.ac:19: -1- AC_SUBST([target_os])
m4trace:configure.ac:19: -1- AC_SUBST_TRACE([target_os])
m4trace:configure.ac:19: -1- m4_pattern_allow([^target_os$])
m4trace:configure.ac:22: -1- AM_CONDITIONAL([CYGWIN], [true])
m4trace:configure.ac:22: -1- AC_SUBST([CYGWIN_TRUE])
m4trace:configure.ac:22: -1- AC_SUBST_TRACE([CYGWIN_TRUE])
m4trace:configure.ac:22: -1- m4_pattern_allow([^CYGWIN_TRUE$])
m4trace:configure.ac:22: -1- AC_SUBST([CYGWIN_FALSE])
m4trace:configure.ac:22: -1- AC_SUBST_TRACE([CYGWIN_FALSE])
m4trace:configure.ac:22: -1- m4_pattern_allow([^CYGWIN_FALSE$])
m4trace:configure.ac:22: -1- _AM_SUBST_NOTMAKE([CYGWIN_TRUE])
m4trace:configure.ac:22: -1- _AM_SUBST_NOTMAKE([CYGWIN_FALSE])
m4trace:configure.ac:24: -1- AM_CONDITIONAL([CYGWIN], [false])
m4trace:configure.ac:24: -1- AC_SUBST([CYGWIN_TRUE])
m4trace:configure.ac:24: -1- AC_SUBST_TRACE([CYGWIN_TRUE])
m4trace:configure.ac:24: -1- m4_pattern_allow([^CYGWIN_TRUE$])
m4trace:configure.ac:24: -1- AC_SUBST([CYGWIN_FALSE])
m4trace:configure.ac:24: -1- AC_SUBST_TRACE([CYGWIN_FALSE])
m4trace:configure.ac:24: -1- m4_pattern_allow([^CYGWIN_FALSE$])
m4trace:configure.ac:24: -1- _AM_SUBST_NOTMAKE([CYGWIN_TRUE])
m4trace:configure.ac:24: -1- _AM_SUBST_NOTMAKE([CYGWIN_FALSE])
m4trace:configure.ac:28: -1- AM_INIT_AUTOMAKE
m4trace:configure.ac:28: -1- m4_pattern_allow([^AM_[A-Z]+FLAGS$])
m4trace:configure.ac:28: -1- AM_AUTOMAKE_VERSION([1.16.1])
m4trace:configure.ac:28: -1- AC_REQUIRE_AUX_FILE([install-sh])
m4trace:configure.ac:28: -1- AC_SUBST([INSTALL_PROGRAM])
m4trace:configure.ac:28: -1- AC_SUBST_TRACE([INSTALL_PROGRAM])
m4trace:configure.ac:28: -1- m4_pattern_allow([^INSTALL_PROGRAM$])
m4trace:configure.ac:28: -1- AC_SUBST([INSTALL_SCRIPT])
m4trace:configure.ac:28: -1- AC_SUBST_TRACE([INSTALL_SCRIPT])
m4trace:configure.ac:28: -1- m4_pattern_allow([^INSTALL_SCRIPT$])
m4trace:configure.ac:28: -1- AC_SUBST([INSTALL_DATA])
m4trace:configure.ac:28: -1- AC_SUBST_TRACE([INSTALL_DATA])
m4trace:configure.ac:28: -1- m4_pattern_allow([^INSTALL_DATA$])
m4trace:configure.ac:28: -1- AC_SUBST([am__isrc], [' -I$(srcdir)'])
m4trace:configure.ac:28: -1- AC_SUBST_TRACE([am__isrc])
m4trace:configure.ac:28: -1- m4_pattern_allow([^am__isrc$])
m4trace:configure.ac:28: -1- _AM_SUBST_NOTMAKE([am__isrc])
m4trace:configure.ac:28: -1- AC_SUBST([CYGPATH_W])
m4trace:configure.ac:28: -1- AC_SUBST_TRACE([CYGPATH_W])
m4trace:configure.ac:28: -1- m4_pattern_allow([^CYGPATH_W$])
m4trace:configure.ac:28: -1- AC_SUBST([PACKAGE], ['AC_PACKAGE_TARNAME'])
m4trace:configure.ac:28: -1- AC_SUBST_TRACE([PACKAGE])
m4trace:configure.ac:28: -1- m4_pattern_allow([^PACKAGE$])
m4trace:configure.ac:28: -1- AC_SUBST([VERSION], ['AC_PACKAGE_VERSION'])
m4trace:configure.ac:28: -1- AC_SUBST_TRACE([VERSION])
m4trace:configure.ac:28: -1- m4_pattern_allow([^VERSION$])
m4trace:configure.ac:28: -1- AC_DEFINE_TRACE_LITERAL([PACKAGE])
m4trace:configure.ac:28: -1- m4_pattern_allow([^PACKAGE$])
m4trace:configure.ac:28: -1- AH_OUTPUT([PACKAGE], [/* Name of package */
@%:@undef PACKAGE])
m4trace:configure.ac:28: -1- AC_DEFINE_TRACE_LITERAL([VERSION])
m4trace:configure.ac:28: -1- m4_pattern_allow([^VERSION$])
m4trace:configure.ac:28: -1- AH_OUTPUT([VERSION], [/* Version number of package */
@%:@undef VERSION])
m4trace:configure.ac:28: -1- AC_REQUIRE_AUX_FILE([missing])
m4trace:configure.ac:28: -1- AC_SUBST([ACLOCAL])
m4trace:configure.ac:28: -1- AC_SUBST_TRACE([ACLOCAL])
m4trace:configure.ac:28: -1- m4_pattern_allow([^ACLOCAL$])
m4trace:configure.ac:28: -1- AC_SUBST([AUTOCONF])
m4trace:configure.ac:28: -1- AC_SUBST_TRACE([AUTOCONF])
m4trace:configure.ac:28: -1- m4_pattern_allow([^AUTOCONF$])
m4trace:configure.ac:28: -1- AC_SUBST([AUTOMAKE])
m4trace:configure.ac:28: -1- AC_SUBST_TRACE([AUTOMAKE])
m4trace:configure.ac:28: -1- m4_pattern_allow([^AUTOMAKE$])
m4trace:configure.ac:28: -1- AC_SUBST([AUTOHEADER])
m4trace:configure.ac:28: -1- AC_SUBST_TRACE([AUTOHEADER])
m4trace:configure.ac:28: -1- m4_pattern_allow([^AUTOHEADER$])
m4trace:configure.ac:28: -1- AC_SUBST([MAKEINFO])
m4trace:configure.ac:28: -1- AC_SUBST_TRACE([MAKEINFO])
m4trace:configure.ac:28: -1- m4_pattern_allow([^MAKEINFO$])
m4trace:configure.ac:28: -1- AC_SUBST([install_sh])
m4trace:configure.ac:28: -1- AC_SUBST_TRACE([install_sh])
m4trace:configure.ac:28: -1- m4_pattern_allow([^install_sh$])
m4trace:configure.ac:28: -1- AC_SUBST([STRIP])
m4trace:configure.ac:28: -1- AC_SUBST_TRACE([STRIP])
m4trace:configure.ac:28: -1- m4_pattern_allow([^STRIP$])
m4trace:configure.ac:28: -1- AC_SUBST([INSTALL_STRIP_PROGRAM])
m4trace:configure.ac:28: -1- AC_SUBST_TRACE([INSTALL_STRIP_PROGRAM])
m4trace:configure.ac:28: -1- m4_pattern_allow([^INSTALL_STRIP_PROGRAM$])
m4trace:configure.ac:28: -1- AC_REQUIRE_AUX_FILE([install-sh])
m4trace:configure.ac:28: -1- AC_SUBST([MKDIR_P])
m4trace:configure.ac:28: -1- AC_SUBST_TRACE([MKDIR_P])
m4trace:configure.ac:28: -1- m4_pattern_allow([^MKDIR_P$])
m4trace:configure.ac:28: -1- AC_SUBST([mkdir_p], ['$(MKDIR_P)'])
m4trace:configure.ac:28: -1- AC_SUBST_TRACE([mkdir_p])
m4trace:configure.ac:28: -1- m4_pattern_allow([^mkdir_p$])
m4trace:configure.ac:28: -1- AC_SUBST([AWK])
m4trace:configure.ac:28: -1- AC_SUBST_TRACE([AWK])
m4trace:configure.ac:28: -1- m4_pattern_allow([^AWK$])
m4trace:configure.ac:28: -1- AC_SUBST([SET_MAKE])
m4trace:configure.ac:28: -1- AC_SUBST_TRACE([SET_MAKE])
m4trace:configure.ac:28: -1- m4_pattern_allow([^SET_MAKE$])
m4trace:configure.ac:28: -1- AC_SUBST([am__leading_dot])
m4trace:configure.ac:28: -1- AC_SUBST_TRACE([am__leading_dot])
m4trace:configure.ac:28: -1- m4_pattern_allow([^am__leading_dot$])
m4trace:configure.ac:28: -1- AC_SUBST([AMTAR], ['$${TAR-tar}'])
m4trace:configure.ac:28: -1- AC_SUBST_TRACE([AMTAR])
m4trace:configure.ac:28: -1- m4_pattern_allow([^AMTAR$])
m4trace:configure.ac:28: -1- AC_SUBST([am__tar])
m4trace:configure.ac:28: -1- AC_SUBST_TRACE([am__tar])
m4trace:configure.ac:28: -1- m4_pattern_allow([^am__tar$])
m4trace:configure.ac:28: -1- AC_SUBST([am__untar])
m4trace:configure.ac:28: -1- AC_SUBST_TRACE([am__untar])
m4trace:configure.ac:28: -1- m4_pattern_allow([^am__untar$])
m4trace:configure.ac:28: -1- AM_SILENT_RULES
m4trace:configure.ac:28: -1- AC_SUBST([AM_V])
m4trace:configure.ac:28: -1- AC_SUBST_TRACE([AM_V])
m4trace:configure.ac:28: -1- m4_pattern_allow([^AM_V$])
m4trace:configure.ac:28: -1- _AM_SUBST_NOTMAKE([AM_V])
m4trace:configure.ac:28: -1- AC_SUBST([AM_DEFAULT_V])
m4trace:configure.ac:28: -1- AC_SUBST_TRACE([AM_DEFAULT_V])
m4trace:configure.ac:28: -1- m4_pattern_allow([^AM_DEFAULT_V$])
m4trace:configure.ac:28: -1- _AM_SUBST_NOTMAKE([AM_DEFAULT_V])
m4trace:configure.ac:28: -1- AC_SUBST([AM_DEFAULT_VERBOSITY])
m4trace:configure.ac:28: -1- AC_SUBST_TRACE([AM_DEFAULT_VERBOSITY])
m4trace:configure.ac:28: -1- m4_pattern_allow([^AM_DEFAULT_VERBOSITY$])
m4trace:configure.ac:28: -1- AC_SUBST([AM_BACKSLASH])
m4trace:configure.ac:28: -1- AC_SUBST_TRACE([AM_BACKSLASH])
m4trace:configure.ac:28: -1- m4_pattern_allow([^AM_BACKSLASH$])
m4trace:configure.ac:28: -1- _AM_SUBST_NOTMAKE([AM_BACKSLASH])
m4trace:configure.ac:31: -1- AM_CONDITIONAL([SCHED_EXPERIMENTAL], [false])
m4trace:configure.ac:31: -1- AC_SUBST([SCHED_EXPERIMENTAL_TRUE])
m4trace:configure.ac:31: -1- AC_SUBST_TRACE([SCHED_EXPERIMENTAL_TRUE])
m4trace:configure.ac:31: -1- m4_pattern_allow([^SCHED_EXPERIMENTAL_TRUE$])
m4trace:configure.ac:31: -1- AC_SUBST([SCHED_EXPERIMENTAL_FALSE])
m4trace:configure.ac:31: -1- AC_SUBST_TRACE([SCHED_EXPERIMENTAL_FALSE])
m4trace:configure.ac:31: -1- m4_pattern_allow([^SCHED_EXPERIMENTAL_FALSE$])
m4trace:configure.ac:31: -1- _AM_SUBST_NOTMAKE([SCHED_EXPERIMENTAL_TRUE])
m4trace:configure.ac:31: -1- _AM_SUBST_NOTMAKE([SCHED_EXPERIMENTAL_FALSE])
m4trace:configure.ac:34: -1- AM_SILENT_RULES([yes])
m4trace:configure.ac:34: -1- AC_SUBST([AM_V])
m4trace:configure.ac:34: -1- AC_SUBST_TRACE([AM_V])
m4trace:configure.ac:34: -1- m4_pattern_allow([^AM_V$])
m4trace:configure.ac:34: -1- _AM_SUBST_NOTMAKE([AM_V])
m4trace:configure.ac:34: -1- AC_SUBST([AM_DEFAULT_V])
m4trace:configure.ac:34: -1- AC_SUBST_TRACE([AM_DEFAULT_V])
m4trace:configure.ac:34: -1- m4_pattern_allow([^AM_DEFAULT_V$])
m4trace:configure.ac:34: -1- _AM_SUBST_NOTMAKE([AM_DEFAULT_V])
m4trace:configure.ac:34: -1- AC_SUBST([AM_DEFAULT_VERBOSITY])
m4trace:configure.ac:34: -1- AC_SUBST_TRACE([AM_DEFAULT_VERBOSITY])
m4trace:configure.ac:34: -1- m4_pattern_allow([^AM_DEFAULT_VERBOSITY$])
m4trace:configure.ac:34: -1- AC_SUBST([AM_BACKSLASH])
m4trace:configure.ac:34: -1- AC_SUBST_TRACE([AM_BACKSLASH])
m4trace:configure.ac:34: -1- m4_pattern_allow([^AM_BACKSLASH$])
m4trace:configure.ac:34: -1- _AM_SUBST_NOTMAKE([AM_BACKSLASH])
m4trace:configure.ac:37: -1- AC_SUBST([CC])
m4trace:configure.ac:37: -1- AC_SUBST_TRACE([CC])
m4trace:configure.ac:37: -1- m4_pattern_allow([^CC$])
m4trace:configure.ac:37: -1- AC_SUBST([CFLAGS])
m4trace:configure.ac:37: -1- AC_SUBST_TRACE([CFLAGS])
m4trace:configure.ac:37: -1- m4_pattern_allow([^CFLAGS$])
m4trace:configure.ac:37: -1- AC_SUBST([LDFLAGS])
m4trace:configure.ac:37: -1- AC_SUBST_TRACE([LDFLAGS])
m4trace:configure.ac:37: -1- m4_pattern_allow([^LDFLAGS$])
m4trace:configure.ac:37: -1- AC_SUBST([LIBS])
m4trace:configure.ac:37: -1- AC_SUBST_TRACE([LIBS])
m4trace:configure.ac:37: -1- m4_pattern_allow([^LIBS$])
m4trace:configure.ac:37: -1- AC_SUBST([CPPFLAGS])
m4trace:configure.ac:37: -1- AC_SUBST_TRACE([CPPFLAGS])
m4trace:configure.ac:37: -1- m4_pattern_allow([^CPPFLAGS$])
m4trace:configure.ac:37: -1- AC_SUBST([CC])
m4trace:configure.ac:37: -1- AC_SUBST_TRACE([CC])
m4trace:configure.ac:37: -1- m4_pattern_allow([^CC$])
m4trace:configure.ac:37: -1- AC_SUBST([CC])
m4trace:configure.ac:37: -1- AC_SUBST_TRACE([CC])
m4trace:configure.ac:37: -1- m4_pattern_allow([^CC$])
m4trace:configure.ac:37: -1- AC_SUBST([CC])
m4trace:configure.ac:37: -1- AC_SUBST_TRACE([CC])
m4trace:configure.ac:37: -1- m4_pattern_allow([^CC$])
m4trace:configure.ac:37: -1- AC_SUBST([CC])
m4trace:configure.ac:37: -1- AC_SUBST_TRACE([CC])
m4trace:configure.ac:37: -1- m4_pattern_allow([^CC$])
m4trace:configure.ac:37: -1- AC_SUBST([ac_ct_CC])
m4trace:configure.ac:37: -1- AC_SUBST_TRACE([ac_ct_CC])
m4trace:configure.ac:37: -1- m4_pattern_allow([^ac_ct_CC$])
m4trace:configure.ac:37: -1- AC_SUBST([EXEEXT], [$ac_cv_exeext])
m4trace:configure.ac:37: -1- AC_SUBST_TRACE([EXEEXT])
m4trace:configure.ac:37: -1- m4_pattern_allow([^EXEEXT$])
m4trace:configure.ac:37: -1- AC_SUBST([OBJEXT], [$ac_cv_objext])
m4trace:configure.ac:37: -1- AC_SUBST_TRACE([OBJEXT])
m4trace:configure.ac:37: -1- m4_pattern_allow([^OBJEXT$])
m4trace:configure.ac:37: -1- AC_REQUIRE_AUX_FILE([compile])
m4trace:configure.ac:37: -1- AC_SUBST([DEPDIR], ["${am__leading_dot}deps"])
m4trace:configure.ac:37: -1- AC_SUBST_TRACE([DEPDIR])
m4trace:configure.ac:37: -1- m4_pattern_allow([^DEPDIR$])
m4trace:configure.ac:37: -1- AC_SUBST([am__include])
m4trace:configure.ac:37: -1- AC_SUBST_TRACE([am__include])
m4trace:configure.ac:37: -1- m4_pattern_allow([^am__include$])
m4trace:configure.ac:37: -1- AM_CONDITIONAL([AMDEP], [test "x$enable_dependency_tracking" != xno])
m4trace:configure.ac:37: -1- AC_SUBST([AMDEP_TRUE])
m4trace:configure.ac:37: -1- AC_SUBST_TRACE([AMDEP_TRUE])
m4trace:configure.ac:37: -1- m4_pattern_allow([^AMDEP_TRUE$])
m4trace:configure.ac:37: -1- AC_SUBST([AMDEP_FALSE])
m4trace:configure.ac:37: -1- AC_SUBST_TRACE([AMDEP_FALSE])
m4trace:configure.ac:37: -1- m4_pattern_allow([^AMDEP_FALSE$])
m4trace:configure.ac:37: -1- _AM_SUBST_NOTMAKE([AMDEP_TRUE])
m4trace:configure.ac:37: -1- _AM_SUBST_NOTMAKE([AMDEP_FALSE])
m4trace:configure.ac:37: -1- AC_SUBST([AMDEPBACKSLASH])
m4trace:configure.ac:37: -1- AC_SUBST_TRACE([AMDEPBACKSLASH])
m4trace:configure.ac:37: -1- m4_pattern_allow([^AMDEPBACKSLASH$])
m4trace:configure.ac:37: -1- _AM_SUBST_NOTMAKE([AMDEPBACKSLASH])
m4trace:configure.ac:37: -1- AC_SUBST([am__nodep])
m4trace:configure.ac:37: -1- AC_SUBST_TRACE([am__nodep])
m4trace:configure.ac:37: -1- m4_pattern_allow([^am__nodep$])
m4trace:configure.ac:37: -1- _AM_SUBST_NOTMAKE([am__nodep])
m4trace:configure.ac:37: -1- AC_SUBST([CCDEPMODE], [depmode=$am_cv_CC_dependencies_compiler_type])
m4trace:configure.ac:37: -1- AC_SUBST_TRACE([CCDEPMODE])
m4trace:configure.ac:37: -1- m4_pattern_allow([^CCDEPMODE$])
m4trace:configure.ac:37: -1- AM_CONDITIONAL([am__fastdepCC], [
  test "x$enable_dependency_tracking" != xno \
  && test "$am_cv_CC_dependencies_compiler_type" = gcc3])
m4trace:configure.ac:37: -1- AC_SUBST([am__fastdepCC_TRUE])
m4trace:configure.ac:37: -1- AC_SUBST_TRACE([am__fastdepCC_TRUE])
m4trace:configure.ac:37: -1- m4_pattern_allow([^am__fastdepCC_TRUE$])
m4trace:configure.ac:37: -1- AC_SUBST([am__fastdepCC_FALSE])
m4trace:configure.ac:37: -1- AC_SUBST_TRACE([am__fastdepCC_FALSE])
m4trace:configure.ac:37: -1- m4_pattern_allow([^am__fastdepCC_FALSE$])
m4trace:configure.ac:37: -1- _AM_SUBST_NOTMAKE([am__fastdepCC_TRUE])
m4trace:configure.ac:37: -1- _AM_SUBST_NOTMAKE([am__fastdepCC_FALSE])
m4trace:configure.ac:39: -1- AC_SUBST([RANLIB])
m4trace:configure.ac:39: -1- AC_SUBST_TRACE([RANLIB])
m4trace:configure.ac:39: -1- m4_pattern_allow([^RANLIB$])
m4trace:configure.ac:40: -1- AC_SUBST([YACC])
m4trace:configure.ac:40: -1- AC_SUBST_TRACE([YACC])
m4trace:configure.ac:40: -1- m4_pattern_allow([^YACC$])
m4trace:configure.ac:40: -1- AC_SUBST([YACC])
m4trace:configure.ac:40: -1- AC_SUBST_TRACE([YACC])
m4trace:configure.ac:40: -1- m4_pattern_allow([^YACC$])
m4trace:configure.ac:40: -1- AC_SUBST([YFLAGS])
m4trace:configure.ac:40: -1- AC_SUBST_TRACE([YFLAGS])
m4trace:configure.ac:40: -1- m4_pattern_allow([^YFLAGS$])
m4trace:configure.ac:41: -1- AC_SUBST([LEX])
m4trace:configure.ac:41: -1- AC_SUBST_TRACE([LEX])
m4trace:configure.ac:41: -1- m4_pattern_allow([^LEX$])
m4trace:configure.ac:41: -1- AC_SUBST([LEX_OUTPUT_ROOT], [$ac_cv_prog_lex_root])
m4trace:configure.ac:41: -1- AC_SUBST_TRACE([LEX_OUTPUT_ROOT])
m4trace:configure.ac:41: -1- m4_pattern_allow([^LEX_OUTPUT_ROOT$])
m4trace:configure.ac:41: -1- AC_SUBST([LEXLIB])
m4trace:configure.ac:41: -1- AC_SUBST_TRACE([LEXLIB])
m4trace:configure.ac:41: -1- m4_pattern_allow([^LEXLIB$])
m4trace:configure.ac:41: -1- AC_DEFINE_TRACE_LITERAL([YYTEXT_POINTER])
m4trace:configure.ac:41: -1- m4_pattern_allow([^YYTEXT_POINTER$])
m4trace:configure.ac:41: -1- AH_OUTPUT([YYTEXT_POINTER], [/* Define to 1 if `lex\' declares `yytext\' as a `char *\' by default, not a
   `char@<:@@:>@\'. */
@%:@undef YYTEXT_POINTER])
m4trace:configure.ac:42: -1- AC_SUBST([LN_S], [$as_ln_s])
m4trace:configure.ac:42: -1- AC_SUBST_TRACE([LN_S])
m4trace:configure.ac:42: -1- m4_pattern_allow([^LN_S$])
m4trace:configure.ac:43: -1- AC_SUBST([SET_MAKE])
m4trace:configure.ac:43: -1- AC_SUBST_TRACE([SET_MAKE])
m4trace:configure.ac:43: -1- m4_pattern_allow([^SET_MAKE$])
m4trace:configure.ac:54: -1- AH_OUTPUT([HAVE_RPC_XDR_H], [/* Define to 1 if you have the <rpc/xdr.h> header file. */
@%:@undef HAVE_RPC_XDR_H])
m4trace:configure.ac:54: -1- AC_SUBST([CPP])
m4trace:configure.ac:54: -1- AC_SUBST_TRACE([CPP])
m4trace:configure.ac:54: -1- m4_pattern_allow([^CPP$])
m4trace:configure.ac:54: -1- AC_SUBST([CPPFLAGS])
m4trace:configure.ac:54: -1- AC_SUBST_TRACE([CPPFLAGS])
m4trace:configure.ac:54: -1- m4_pattern_allow([^CPPFLAGS$])
m4trace:configure.ac:54: -1- AC_SUBST([CPP])
m4trace:configure.ac:54: -1- AC_SUBST_TRACE([CPP])
m4trace:configure.ac:54: -1- m4_pattern_allow([^CPP$])
m4trace:configure.ac:54: -1- AC_SUBST([GREP])
m4trace:configure.ac:54: -1- AC_SUBST_TRACE([GREP])
m4trace:configure.ac:54: -1- m4_pattern_allow([^GREP$])
m4trace:configure.ac:54: -1- AC_SUBST([EGREP])
m4trace:configure.ac:54: -1- AC_SUBST_TRACE([EGREP])
m4trace:configure.ac:54: -1- m4_pattern_allow([^EGREP$])
m4trace:configure.ac:54: -1- AC_DEFINE_TRACE_LITERAL([STDC_HEADERS])
m4trace:configure.ac:54: -1- m4_pattern_allow([^STDC_HEADERS$])
m4trace:configure.ac:54: -1- AH_OUTPUT([STDC_HEADERS], [/* Define to 1 if you have the ANSI C header files. */
@%:@undef STDC_HEADERS])
m4trace:configure.ac:54: -1- AH_OUTPUT([HAVE_SYS_TYPES_H], [/* Define to 1 if you have the <sys/types.h> header file. */
@%:@undef HAVE_SYS_TYPES_H])
m4trace:configure.ac:54: -1- AH_OUTPUT([HAVE_SYS_STAT_H], [/* Define to 1 if you have the <sys/stat.h> header file. */
@%:@undef HAVE_SYS_STAT_H])
m4trace:configure.ac:54: -1- AH_OUTPUT([HAVE_STDLIB_H], [/* Define to 1 if you have the <stdlib.h> header file. */
@%:@undef HAVE_STDLIB_H])
m4trace:configure.ac:54: -1- AH_OUTPUT([HAVE_STRING_H], [/* Define to 1 if you have the <string.h> header file. */
@%:@undef HAVE_STRING_H])
m4trace:configure.ac:54: -1- AH_OUTPUT([HAVE_MEMORY_H], [/* Define to 1 if you have the <memory.h> header file. */
@%:@undef HAVE_MEMORY_H])
m4trace:configure.ac:54: -1- AH_OUTPUT([HAVE_STRINGS_H], [/* Define to 1 if you have the <strings.h> header file. */
@%:@undef HAVE_STRINGS_H])
m4trace:configure.ac:54: -1- AH_OUTPUT([HAVE_INTTYPES_H], [/* Define to 1 if you have the <inttypes.h> header file. */
@%:@undef HAVE_INTTYPES_H])
m4trace:configure.ac:54: -1- AH_OUTPUT([HAVE_STDINT_H], [/* Define to 1 if you have the <stdint.h> header file. */
@%:@undef HAVE_STDINT_H])
m4trace:configure.ac:54: -1- AH_OUTPUT([HAVE_UNISTD_H], [/* Define to 1 if you have the <unistd.h> header file. */
@%:@undef HAVE_UNISTD_H])
m4trace:configure.ac:54: -1- AC_DEFINE_TRACE_LITERAL([HAVE_RPC_XDR_H])
m4trace:configure.ac:54: -1- m4_pattern_allow([^HAVE_RPC_XDR_H$])
m4trace:configure.ac:54: -1- AH_OUTPUT([HAVE_TIRPC_RPC_XDR_H], [/* Define to 1 if you have the <tirpc/rpc/xdr.h> header file. */
@%:@undef HAVE_TIRPC_RPC_XDR_H])
m4trace:configure.ac:54: -1- AC_DEFINE_TRACE_LITERAL([HAVE_TIRPC_RPC_XDR_H])
m4trace:configure.ac:54: -1- m4_pattern_allow([^HAVE_TIRPC_RPC_XDR_H$])
m4trace:configure.ac:59: -1- AH_OUTPUT([HAVE_TCL_H], [/* Define to 1 if you have the <tcl.h> header file. */
@%:@undef HAVE_TCL_H])
m4trace:configure.ac:59: -1- AC_DEFINE_TRACE_LITERAL([HAVE_TCL_H])
m4trace:configure.ac:59: -1- m4_pattern_allow([^HAVE_TCL_H$])
m4trace:configure.ac:59: -1- AH_OUTPUT([HAVE_TCL_TCL_H], [/* Define to 1 if you have the <tcl/tcl.h> header file. */
@%:@undef HAVE_TCL_TCL_H])
m4trace:configure.ac:59: -1- AC_DEFINE_TRACE_LITERAL([HAVE_TCL_TCL_H])
m4trace:configure.ac:59: -1- m4_pattern_allow([^HAVE_TCL_TCL_H$])
m4trace:configure.ac:60: -1- AH_OUTPUT([HAVE_LIBTCL], [/* Define to 1 if you have the `tcl\' library (-ltcl). */
@%:@undef HAVE_LIBTCL])
m4trace:configure.ac:60: -1- AC_DEFINE_TRACE_LITERAL([HAVE_LIBTCL])
m4trace:configure.ac:60: -1- m4_pattern_allow([^HAVE_LIBTCL$])
m4trace:configure.ac:60: -1- AH_OUTPUT([HAVE_LIBTCL8_6], [/* Define to 1 if you have the `tcl8.6\' library (-ltcl8.6). */
@%:@undef HAVE_LIBTCL8_6])
m4trace:configure.ac:60: -1- AC_DEFINE_TRACE_LITERAL([HAVE_LIBTCL8_6])
m4trace:configure.ac:60: -1- m4_pattern_allow([^HAVE_LIBTCL8_6$])
m4trace:configure.ac:60: -1- AH_OUTPUT([HAVE_LIBTCL8_5], [/* Define to 1 if you have the `tcl8.5\' library (-ltcl8.5). */
@%:@undef HAVE_LIBTCL8_5])
m4trace:configure.ac:60: -1- AC_DEFINE_TRACE_LITERAL([HAVE_LIBTCL8_5])
m4trace:configure.ac:60: -1- m4_pattern_allow([^HAVE_LIBTCL8_5$])
m4trace:configure.ac:60: -1- AH_OUTPUT([HAVE_LIBTCL8_4], [/* Define to 1 if you have the `tcl8.4\' library (-ltcl8.4). */
@%:@undef HAVE_LIBTCL8_4])
m4trace:configure.ac:60: -1- AC_DEFINE_TRACE_LITERAL([HAVE_LIBTCL8_4])
m4trace:configure.ac:60: -1- m4_pattern_allow([^HAVE_LIBTCL8_4$])
m4trace:configure.ac:60: -1- AH_OUTPUT([HAVE_LIBTCL8_3], [/* Define to 1 if you have the `tcl8.3\' library (-ltcl8.3). */
@%:@undef HAVE_LIBTCL8_3])
m4trace:configure.ac:60: -1- AC_DEFINE_TRACE_LITERAL([HAVE_LIBTCL8_3])
m4trace:configure.ac:60: -1- m4_pattern_allow([^HAVE_LIBTCL8_3$])
m4trace:configure.ac:60: -1- AH_OUTPUT([HAVE_LIBTCL8_2], [/* Define to 1 if you have the `tcl8.2\' library (-ltcl8.2). */
@%:@undef HAVE_LIBTCL8_2])
m4trace:configure.ac:60: -1- AC_DEFINE_TRACE_LITERAL([HAVE_LIBTCL8_2])
m4trace:configure.ac:60: -1- m4_pattern_allow([^HAVE_LIBTCL8_2$])
m4trace:configure.ac:60: -1- AH_OUTPUT([HAVE_LIBTCL8_1], [/* Define to 1 if you have the `tcl8.1\' library (-ltcl8.1). */
@%:@undef HAVE_LIBTCL8_1])
m4trace:configure.ac:60: -1- AC_DEFINE_TRACE_LITERAL([HAVE_LIBTCL8_1])
m4trace:configure.ac:60: -1- m4_pattern_allow([^HAVE_LIBTCL8_1$])
m4trace:configure.ac:60: -1- AH_OUTPUT([HAVE_LIBTCL8_0], [/* Define to 1 if you have the `tcl8.0\' library (-ltcl8.0). */
@%:@undef HAVE_LIBTCL8_0])
m4trace:configure.ac:60: -1- AC_DEFINE_TRACE_LITERAL([HAVE_LIBTCL8_0])
m4trace:configure.ac:60: -1- m4_pattern_allow([^HAVE_LIBTCL8_0$])
m4trace:configure.ac:71: -1- AC_CONFIG_FILES([
	Makefile                 \
	config/Makefile          \
	lsf/Makefile             \
	lsf/intlib/Makefile      \
	lsf/lib/Makefile         \
	lsf/lim/Makefile         \
	lsf/res/Makefile         \
	lsf/pim/Makefile         \
	lsf/lstools/Makefile     \
	lsf/lsadm/Makefile       \
	lsf/man/Makefile         \
	lsf/man/man1/Makefile    \
	lsf/man/man5/Makefile    \
	lsf/man/man8/Makefile    \
	lsbatch/Makefile         \
	lsbatch/lib/Makefile     \
	lsbatch/cmd/Makefile     \
	lsbatch/bhist/Makefile   \
	lsbatch/daemons/Makefile \
	lsbatch/man1/Makefile    \
	lsbatch/man5/Makefile    \
	lsbatch/man8/Makefile    \
	eauth/Makefile           \
	scripts/Makefile         \
	chkpnt/Makefile          \
	config/lsf.conf          \
	config/lsb.hosts         \
	config/lsb.params        \
	config/lsb.queues        \
	config/lsb.users         \
	config/lsf.cluster.volclava \
	config/lsf.shared        \
	config/lsf.task          \
	config/volclava.csh      \
	config/volclava          \
	config/volclava.setup    \
	config/volclava.sh
])
m4trace:configure.ac:111: -1- AC_SUBST([LIB@&t@OBJS], [$ac_libobjs])
m4trace:configure.ac:111: -1- AC_SUBST_TRACE([LIB@&t@OBJS])
m4trace:configure.ac:111: -1- m4_pattern_allow([^LIB@&t@OBJS$])
m4trace:configure.ac:111: -1- AC_SUBST([LTLIBOBJS], [$ac_ltlibobjs])
m4trace:configure.ac:111: -1- AC_SUBST_TRACE([LTLIBOBJS])
m4trace:configure.ac:111: -1- m4_pattern_allow([^LTLIBOBJS$])
m4trace:configure.ac:111: -1- AM_CONDITIONAL([am__EXEEXT], [test -n "$EXEEXT"])
m4trace:configure.ac:111: -1- AC_SUBST([am__EXEEXT_TRUE])
m4trace:configure.ac:111: -1- AC_SUBST_TRACE([am__EXEEXT_TRUE])
m4trace:configure.ac:111: -1- m4_pattern_allow([^am__EXEEXT_TRUE$])
m4trace:configure.ac:111: -1- AC_SUBST([am__EXEEXT_FALSE])
m4trace:configure.ac:111: -1- AC_SUBST_TRACE([am__EXEEXT_FALSE])
m4trace:configure.ac:111: -1- m4_pattern_allow([^am__EXEEXT_FALSE$])
m4trace:configure.ac:111: -1- _AM_SUBST_NOTMAKE([am__EXEEXT_TRUE])
m4trace:configure.ac:111: -1- _AM_SUBST_NOTMAKE([am__EXEEXT_FALSE])
m4trace:configure.ac:111: -1- AC_SUBST_TRACE([top_builddir])
m4trace:configure.ac:111: -1- AC_SUBST_TRACE([top_build_prefix])
m4trace:configure.ac:111: -1- AC_SUBST_TRACE([srcdir])
m4trace:configure.ac:111: -1- AC_SUBST_TRACE([abs_srcdir])
m4trace:configure.ac:111: -1- AC_SUBST_TRACE([top_srcdir])
m4trace:configure.ac:111: -1- AC_SUBST_TRACE([abs_top_srcdir])
m4trace:configure.ac:111: -1- AC_SUBST_TRACE([builddir])
m4trace:configure.ac:111: -1- AC_SUBST_TRACE([abs_builddir])
m4trace:configure.ac:111: -1- AC_SUBST_TRACE([abs_top_builddir])
m4trace:configure.ac:111: -1- AC_SUBST_TRACE([INSTALL])
m4trace:configure.ac:111: -1- AC_SUBST_TRACE([MKDIR_P])
