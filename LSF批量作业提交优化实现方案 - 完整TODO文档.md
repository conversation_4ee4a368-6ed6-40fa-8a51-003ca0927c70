# LSF批量作业提交优化实现方案 - 完整TODO文档

## 项目目标
实现 `bsub -pack` 功能，使客户端与服务器建立一次网络连接，一次性提交文件中的所有作业，服务器处理完一个作业就及时返回结果，直到处理完所有作业后结束连接。

### 🎯 核心需求
- **批量提交**：一次连接提交多个作业，减少网络开销
- **流式响应**：服务器处理完一个作业立即返回结果
- **向后兼容**：不影响现有LSF功能和API
- **错误处理**：部分作业失败不影响其他作业继续处理
- **性能优化**：支持大批量作业（数千个）的高效处理

### 📁 Pack文件格式规范
```bash
# 示例 jobs.pack 文件内容
-R "select[mem>200] rusage[mem=100]" sleep 10
-J "test" sleep 11
sleep 12
-q normal -n 4 ./my_job.sh
-o output.log -e error.log python script.py
```
- 每行一个完整的bsub命令（不包含bsub关键字）
- 支持所有标准bsub参数和选项
- 支持注释行（以#开头）和空行
- 文件编码：UTF-8

## 实现架构概述

### 核心组件
1. **客户端批量处理模块** (`lsbatch/cmd/src/`)
2. **XDR流式传输协议** (`lsbatch/lib/lsb.xdr.c`)
3. **服务器端批量处理** (`lsbatch/daemons/`)
4. **数据结构定义** (`lsbatch/lsbatch.h`)
5. **内存管理和资源清理机制**
6. **错误处理和恢复策略**

---

## 第一阶段：数据结构和协议定义

### 1.1 修改 `lsbatch/lsbatch.h`

**位置**: 在 `struct submitReply` 之后添加

```c
/* 批量作业相关数据结构 */
struct batchJobResult {
    int jobIndex;                   /* 作业在批量中的索引 */
    LS_LONG_INT jobId;              /* 作业ID (-1表示失败) */
    int errorCode;                  /* 错误代码 */
    char *errorMsg;                 /* 错误信息 */
    char *queue;                    /* 队列名称 */
};

struct batchSubmitReq {
    int jobCount;                  /* 批量作业数量 */
    int options;                   /* 批量选项 */
    struct submitReq *jobs;        /* 作业数组 */
    char *sourceFile;              /* 源pack文件 */
    char *batchName;               /* 批量作业名称 */
    int maxConcurrency;            /* 最大并发数 */
    time_t clientTimestamp;        /* 客户端时间戳 */
};

struct batchSubmitReply {
    int totalJobs;                 /* 总作业数 */
    int successCount;              /* 成功提交数 */
    int failureCount;              /* 失败数 */
    struct batchJobResult *results;/* 结果数组 */
};

/* 流式传输协议结构 */
struct streamHeader {
    int messageType;               /* 消息类型 */
    int sequenceId;                /* 序列ID */
    int batchId;                   /* 批次ID */
    int flags;                     /* 标志位 */
};

struct streamJobResult {
    int batchId;                   /* 批次ID */
    int jobIndex;                  /* 作业索引 */
    LS_LONG_INT jobId;             /* 作业ID */
    int resultCode;                /* 结果代码 */
    char *errorMessage;            /* 错误信息 */
    char *queueName;               /* 队列名称 */
    char *hostName;                /* 主机名称 */
    time_t submitTime;             /* 提交时间 */
    time_t processTime;            /* 处理时间 */
};

struct batchCompleteSignal {
    int batchId;                   /* 批次ID */
    int totalJobs;                 /* 总作业数 */
    int successCount;              /* 成功数量 */
    int failureCount;              /* 失败数量 */
    time_t startTime;              /* 开始时间 */
    time_t completionTime;         /* 完成时间 */
    char *summaryMessage;          /* 摘要信息 */
};
```

### 1.2 修改 `lsbatch/daemons/daemonout.h`

**位置**: 在现有操作码定义后添加

```c
/* 批量作业提交操作码 */
#define BATCH_PACK_JOB_SUB          38    /* 批量作业提交 */
#define BATCH_STREAM_RESULT         39    /* 流式结果返回 */
#define BATCH_COMPLETE_SIGNAL       40    /* 批量完成信号 */
```

### 1.3 修改 `lsbatch/lsbatch.h` 函数声明

**位置**: 在现有函数声明后添加

```c
/* 批量作业提交函数声明 */
extern int send_batch_pack P_((struct batchSubmitReq *, struct batchSubmitReply *, struct lsfAuth *));
extern int send_batch_pack_streaming P_((struct batchSubmitReq *, void *, struct lsfAuth *));
```

---

## 第二阶段：XDR流式传输协议实现

### 2.1 修改 `lsbatch/lib/lsb.xdr.h`

**位置**: 在现有XDR函数声明后添加

```c
/* 批量作业提交XDR函数声明 */
extern bool_t xdr_streamHeader(XDR *, struct streamHeader *, struct LSFHeader *);
extern bool_t xdr_batchSubmitReq(XDR *, struct batchSubmitReq *, struct LSFHeader *);
extern bool_t xdr_streamJobResult(XDR *, struct streamJobResult *, struct LSFHeader *);
extern bool_t xdr_batchCompleteSignal(XDR *, struct batchCompleteSignal *, struct LSFHeader *);
extern bool_t xdr_batchSubmitReply(XDR *, struct batchSubmitReply *, struct LSFHeader *);
```

### 2.2 修改 `lsbatch/lib/lsb.xdr.c`

**位置**: 在文件末尾添加完整的XDR函数实现

```c
/* 批量作业提交XDR函数实现 - 流式传输协议 */

/* 流式消息头 - 用于标识不同类型的响应 */
bool_t
xdr_streamHeader(XDR *xdrs, struct streamHeader *hdr, struct LSFHeader *lsfHdr)
{
    if (!(xdr_int(xdrs, &hdr->messageType) &&
          xdr_int(xdrs, &hdr->sequenceId) &&
          xdr_int(xdrs, &hdr->batchId) &&
          xdr_int(xdrs, &hdr->flags))) {
        return (FALSE);
    }
    return (TRUE);
}

/* 内存管理辅助宏 - 统一内存分配和错误处理 */
#define BATCH_XDR_MALLOC(ptr, size, type) do { \
    if (xdrs->x_op == XDR_DECODE) { \
        ptr = (type*)calloc(1, size); \
        if (!ptr) return FALSE; \
    } \
} while(0)

#define BATCH_XDR_FREE(ptr) do { \
    if (ptr) { free(ptr); ptr = NULL; } \
} while(0)

/* 批量作业提交请求 - 借鉴submitReq的完整模式，增强内存管理 */
bool_t
xdr_batchSubmitReq(XDR *xdrs, struct batchSubmitReq *batchReq, struct LSFHeader *hdr)
{
    int i;
    static int numJobs = 0;
    static struct submitReq *jobs = NULL;
    static char *sourceFile = NULL;
    static char *batchName = NULL;
    static int maxJobsLimit = 10000; /* 最大作业数限制，防止内存溢出 */

    /* DECODE阶段：初始化静态变量 - 完全借鉴submitReq模式 */
    if (xdrs->x_op == XDR_DECODE) {
        /* 清理之前的数据 */
        if (numJobs > 0 && jobs) {
            /* 每个作业的清理通过submitReq的XDR_FREE处理 */
            for (i = 0; i < numJobs; i++) {
                XDR freeXdr;
                xdrmem_create(&freeXdr, NULL, 0, XDR_FREE);
                xdr_submitReq(&freeXdr, &jobs[i], hdr);
                xdr_destroy(&freeXdr);
            }
            BATCH_XDR_FREE(jobs);
        }
        numJobs = 0;
        jobs = NULL;

        /* 初始化字符串指针 */
        BATCH_XDR_FREE(sourceFile);
        BATCH_XDR_FREE(batchName);

        /* 将静态指针赋给结构体 - 借鉴submitReq模式 */
        batchReq->sourceFile = sourceFile;
        batchReq->batchName = batchName;
        batchReq->jobs = jobs;
    }

    /* XDR_FREE阶段：释放所有动态分配的内存 */
    if (xdrs->x_op == XDR_FREE) {
        if (batchReq->jobs && batchReq->jobCount > 0) {
            for (i = 0; i < batchReq->jobCount; i++) {
                XDR freeXdr;
                xdrmem_create(&freeXdr, NULL, 0, XDR_FREE);
                xdr_submitReq(&freeXdr, &batchReq->jobs[i], hdr);
                xdr_destroy(&freeXdr);
            }
        }
        BATCH_XDR_FREE(batchReq->jobs);
        BATCH_XDR_FREE(batchReq->sourceFile);
        BATCH_XDR_FREE(batchReq->batchName);
        return TRUE;
    }

    /* 编码批量控制信息 - 借鉴submitReq的基本字段处理 */
    if (!(xdr_int(xdrs, &batchReq->jobCount) &&
          xdr_int(xdrs, &batchReq->options) &&
          xdr_int(xdrs, &batchReq->maxConcurrency) &&
          xdr_time_t(xdrs, &batchReq->clientTimestamp))) {
        return (FALSE);
    }

    /* 作业数量安全检查 - 防止恶意请求 */
    if (xdrs->x_op == XDR_DECODE && batchReq->jobCount > maxJobsLimit) {
        ls_syslog(LOG_ERR, "xdr_batchSubmitReq: jobCount %d exceeds limit %d",
                  batchReq->jobCount, maxJobsLimit);
        return (FALSE);
    }

    /* 编码字符串字段 - 完全借鉴submitReq的字符串处理 */
    if (!(xdr_var_string(xdrs, &batchReq->sourceFile) &&
          xdr_var_string(xdrs, &batchReq->batchName))) {
        return (FALSE);
    }

    /* 处理作业数组 - 完全借鉴submitReq的askedHosts数组处理模式 */
    if (xdrs->x_op == XDR_DECODE && batchReq->jobCount > 0) {
        BATCH_XDR_MALLOC(batchReq->jobs,
                         batchReq->jobCount * sizeof(struct submitReq),
                         struct submitReq);
        /* 初始化作业数组 */
        for (i = 0; i < batchReq->jobCount; i++) {
            memset(&batchReq->jobs[i], 0, sizeof(struct submitReq));
        }
    }

    /* 编码每个作业 - 直接调用现有的xdr_submitReq */
    for (i = 0; i < batchReq->jobCount; i++) {
        if (!xdr_submitReq(xdrs, &batchReq->jobs[i], hdr)) {
            /* 出错时清理 - 借鉴submitReq的错误处理 */
            if (xdrs->x_op == XDR_DECODE) {
                for (int j = 0; j < i; j++) {
                    XDR freeXdr;
                    xdrmem_create(&freeXdr, NULL, 0, XDR_FREE);
                    xdr_submitReq(&freeXdr, &batchReq->jobs[j], hdr);
                    xdr_destroy(&freeXdr);
                }
                free(batchReq->jobs);
                batchReq->jobs = NULL;
            }
            return (FALSE);
        }
    }

    /* 保存静态变量 - 借鉴submitReq的静态变量管理 */
    if (xdrs->x_op == XDR_DECODE) {
        numJobs = batchReq->jobCount;
        jobs = batchReq->jobs;
        sourceFile = batchReq->sourceFile;
        batchName = batchReq->batchName;
    }

    return (TRUE);
}

/* 流式作业结果 - 每个作业完成后立即发送 */
bool_t
xdr_streamJobResult(XDR *xdrs, struct streamJobResult *result, struct LSFHeader *hdr)
{
    int jobArrId, jobArrElemId;
    static char *errorMessage = NULL;
    static char *queueName = NULL;
    static char *hostName = NULL;

    /* DECODE阶段：初始化静态变量 - 借鉴submitReq模式 */
    if (xdrs->x_op == XDR_DECODE) {
        errorMessage = NULL;
        queueName = NULL;
        hostName = NULL;
        
        result->errorMessage = errorMessage;
        result->queueName = queueName;
        result->hostName = hostName;
    }

    /* jobId处理 - 完全借鉴submitReq的jobId转换 */
    if (xdrs->x_op == XDR_ENCODE) {
        jobId64To32(result->jobId, &jobArrId, &jobArrElemId);
    }

    /* 编码流式结果字段 - 借鉴submitReq的字段顺序 */
    if (!(xdr_int(xdrs, &result->batchId) &&
          xdr_int(xdrs, &result->jobIndex) &&
          xdr_int(xdrs, &jobArrId) &&
          xdr_int(xdrs, &result->resultCode))) {
        return (FALSE);
    }

    /* 编码字符串字段 - 借鉴submitReq的字符串处理 */
    if (!(xdr_var_string(xdrs, &result->errorMessage) &&
          xdr_var_string(xdrs, &result->queueName) &&
          xdr_var_string(xdrs, &result->hostName))) {
        return (FALSE);
    }

    /* 编码时间和jobId完整信息 */
    if (!(xdr_time_t(xdrs, &result->submitTime) &&
          xdr_time_t(xdrs, &result->processTime) &&
          xdr_int(xdrs, &jobArrElemId))) {
        return (FALSE);
    }

    /* jobId转换 - 借鉴submitReq的转换逻辑 */
    if (xdrs->x_op == XDR_DECODE) {
        jobId32To64(&result->jobId, jobArrId, jobArrElemId);
        errorMessage = result->errorMessage;
        queueName = result->queueName;
        hostName = result->hostName;
    }

    return (TRUE);
}

/* 批量处理完成信号 - 所有作业处理完后发送 */
bool_t
xdr_batchCompleteSignal(XDR *xdrs, struct batchCompleteSignal *signal, struct LSFHeader *hdr)
{
    if (xdrs->x_op == XDR_DECODE) {
        signal->summaryMessage = NULL;
    }

    if (!(xdr_int(xdrs, &signal->batchId) &&
          xdr_int(xdrs, &signal->totalJobs) &&
          xdr_int(xdrs, &signal->successCount) &&
          xdr_int(xdrs, &signal->failureCount) &&
          xdr_time_t(xdrs, &signal->startTime) &&
          xdr_time_t(xdrs, &signal->completionTime))) {
        return FALSE;
    }

    if (!(xdr_var_string(xdrs, &signal->summaryMessage))) {
        return FALSE;
    }
    
    return TRUE;
}

/* 批量提交响应 - 传统模式兼容 */
bool_t
xdr_batchSubmitReply(XDR *xdrs, struct batchSubmitReply *reply, struct LSFHeader *hdr)
{
    int i;

    if (xdrs->x_op == XDR_DECODE) {
        reply->results = NULL;
    }
    
    if (!(xdr_int(xdrs, &reply->totalJobs) &&
          xdr_int(xdrs, &reply->successCount) &&
          xdr_int(xdrs, &reply->failureCount))) {
        return FALSE;
    }
    
    if (xdrs->x_op == XDR_DECODE && reply->totalJobs > 0) {
        reply->results = (struct batchJobResult *)
            calloc(reply->totalJobs, sizeof(struct batchJobResult));
        if (reply->results == NULL) {
            return FALSE;
        }
        
        for (i = 0; i < reply->totalJobs; i++) {
            reply->results[i].errorMsg = NULL;
            reply->results[i].queue = NULL;
        }
    }
    
    /* 编码/解码每个结果记录 */
    for (i = 0; i < reply->totalJobs; i++) {
        if (!(xdr_int(xdrs, &reply->results[i].jobIndex) &&
              xdr_int(xdrs, (int *)&reply->results[i].jobId) &&
              xdr_int(xdrs, &reply->results[i].errorCode) &&
              xdr_var_string(xdrs, &reply->results[i].errorMsg) &&
              xdr_var_string(xdrs, &reply->results[i].queue))) {
            return FALSE;
        }
    }

    return TRUE;
}
```

---

## 第三阶段：客户端批量处理模块

### 3.1 创建 `lsbatch/cmd/include/job_collector.h`

```c
/**
 * @file job_collector.h
 * @brief 批量作业收集器头文件
 */

#ifndef JOB_COLLECTOR_H
#define JOB_COLLECTOR_H

#include <stdio.h>
#include <stdlib.h>
#include <stdbool.h>
#include <time.h>

/* 作业收集器配置 */
typedef struct {
    int max_jobs_count;                       /* 最大作业数 */
    size_t max_memory_usage_mb;               /* 最大内存使用(MB) */
    bool fail_on_first_error;                 /* 遇到第一个错误就停止 */
    bool collect_all_errors;                  /* 收集所有错误信息 */
    bool enable_detailed_logging;             /* 启用详细日志 */
    FILE *debug_log_file;                     /* 调试日志文件 */
} job_collector_config_t;

/* 批量作业集合 */
typedef struct {
    int total_jobs_count;                     /* 总作业数 */
    int total_jobs_capacity;                  /* 作业数组容量 */
    int valid_jobs_count;                     /* 有效作业数 */
    int error_jobs_count;                     /* 错误作业数 */
    
    struct submit **job_requests_array;       /* 作业请求数组 */
    int *job_line_numbers;                    /* 作业行号数组 */
    char **original_command_lines;            /* 原始命令行数组 */
    
    size_t total_memory_used;                 /* 已使用内存 */
    bool collect_original_commands;           /* 是否收集原始命令 */
    
    time_t collection_start_time;             /* 收集开始时间 */
    time_t collection_end_time;               /* 收集结束时间 */
} batch_job_collection_t;

/* 函数声明 */
int job_collector_create_default_config(job_collector_config_t *config);
int job_collector_collect_from_pack_file(const char *pack_file_path,
                                          batch_job_collection_t *collection,
                                          const job_collector_config_t *config);
int job_collector_add_job_to_collection(batch_job_collection_t *collection,
                                        struct submit *job_request,
                                        int line_number,
                                        const char *original_command);
void job_collector_free_collection(batch_job_collection_t *collection);

#endif /* JOB_COLLECTOR_H */
```

### 3.2 创建 `lsbatch/cmd/include/batch_transmitter.h`

```c
/**
 * @file batch_transmitter.h
 * @brief 批量传输器定义
 */

#ifndef BATCH_TRANSMITTER_H
#define BATCH_TRANSMITTER_H

#include <stdio.h>
#include <stdbool.h>
#include <time.h>

/* 作业集合简单接口 */
typedef struct {
    int total_jobs_count;
    struct submit **jobs;
} simple_job_collection_t;

/* 传输统计信息 */
typedef struct {
    size_t total_jobs;
    size_t successful_jobs;
    size_t failed_jobs;
    size_t total_bytes_transmitted;
    double average_transmission_rate_kbps;
    time_t start_time;
    time_t end_time;
} transmit_statistics_t;

/* 传输配置 */
typedef struct {
    int connection_timeout_seconds;
    int retry_count;
    int batch_size;
    bool enable_compression;
} transmit_config_t;

/* 回调函数类型 */
typedef void (*transmit_progress_callback_t)(int completed, int total, void *user_data);
typedef void (*transmit_error_callback_t)(int error_code, const char *error_message, int job_index, void *user_data);
typedef void (*transmit_complete_callback_t)(bool success, const transmit_statistics_t *statistics, void *user_data);

/* 批量传输器结构 */
typedef struct batch_transmitter batch_transmitter_t;

/* 函数原型 */
batch_transmitter_t* batch_transmitter_create(void);
void batch_transmitter_create_default_config(transmit_config_t *config);
int batch_transmitter_execute(batch_transmitter_t *transmitter, const simple_job_collection_t *collection, struct lsfAuth *auth);
void batch_transmitter_set_progress_callback(batch_transmitter_t *transmitter, transmit_progress_callback_t callback, void *user_data);
void batch_transmitter_set_error_callback(batch_transmitter_t *transmitter, transmit_error_callback_t callback, void *user_data);
void batch_transmitter_set_completion_callback(batch_transmitter_t *transmitter, transmit_complete_callback_t callback, void *user_data);
bool batch_transmitter_is_complete(const batch_transmitter_t *transmitter);
int batch_transmitter_process_responses(batch_transmitter_t *transmitter);
void batch_transmitter_get_statistics(const batch_transmitter_t *transmitter, transmit_statistics_t *stats);
void batch_transmitter_cleanup(batch_transmitter_t *transmitter);

#endif /* BATCH_TRANSMITTER_H */
```

### 3.3 创建 `lsbatch/cmd/src/job_collector.c`

**核心功能**: 解析pack文件，收集作业到内存结构

```c
/**
 * @file job_collector.c
 * @brief 批量作业收集器实现 - 优化版本
 *
 * 主要改进：
 * 1. 复用现有LSF参数解析逻辑
 * 2. 增强错误处理和内存管理
 * 3. 支持大文件和大批量作业
 * 4. 添加详细的进度反馈
 */

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <errno.h>
#include <time.h>
#include <ctype.h>

#include "../include/job_collector.h"
#include "../../lib/lsb.h"
#include "../cmd.h"

/* 内部常量定义 */
#define JOB_COLLECTOR_MAX_LINE_LENGTH    8192   /* 增加行长度限制 */
#define JOB_COLLECTOR_INITIAL_CAPACITY   100
#define JOB_COLLECTOR_GROWTH_FACTOR      2
#define JOB_COLLECTOR_PROGRESS_INTERVAL  100    /* 每100个作业报告一次进度 */

/* 内部函数声明 */
static int parse_job_line(const char *line, int line_number, struct submit *job_req);
static int expand_job_capacity(batch_job_collection_t *collection, int new_capacity);
static bool is_empty_or_comment_line(const char *line);
static void report_progress(int processed, int total, const char *phase);
static int validate_job_request(const struct submit *job_req, int line_number);

/**
 * @brief 创建默认作业收集器配置
 */
int job_collector_create_default_config(job_collector_config_t *config)
{
    if (!config) {
        return -1;
    }
    
    memset(config, 0, sizeof(job_collector_config_t));
    
    config->max_jobs_count = 10000;
    config->max_memory_usage_mb = 100;
    config->fail_on_first_error = false;
    config->collect_all_errors = true;
    config->enable_detailed_logging = false;
    config->debug_log_file = NULL;
    
    return 0;
}

/**
 * @brief 从pack文件收集作业
 */
int job_collector_collect_from_pack_file(const char *pack_file_path,
                                          batch_job_collection_t *collection,
                                          const job_collector_config_t *config)
{
    FILE *pack_file = NULL;
    char line_buffer[JOB_COLLECTOR_MAX_LINE_LENGTH];
    job_collector_config_t default_config;
    const job_collector_config_t *active_config = config;
    int line_number = 0;
    int valid_jobs_collected = 0;
    int result = -1;

    /* 参数验证 */
    if (!pack_file_path || !collection) {
        return -1;
    }

    /* 使用默认配置 */
    if (!active_config) {
        job_collector_create_default_config(&default_config);
        active_config = &default_config;
    }

    /* 初始化集合结构 */
    memset(collection, 0, sizeof(batch_job_collection_t));
    collection->total_jobs_capacity = JOB_COLLECTOR_INITIAL_CAPACITY;
    collection->job_requests_array = calloc(collection->total_jobs_capacity, sizeof(struct submit*));
    collection->job_line_numbers = calloc(collection->total_jobs_capacity, sizeof(int));
    collection->original_command_lines = calloc(collection->total_jobs_capacity, sizeof(char*));
    
    if (!collection->job_requests_array || !collection->job_line_numbers || !collection->original_command_lines) {
        fprintf(stderr, "❌ 内存分配失败\n");
        goto cleanup;
    }

    collection->collect_original_commands = true;
    collection->collection_start_time = time(NULL);

    /* 打开pack文件 */
    pack_file = fopen(pack_file_path, "r");
    if (!pack_file) {
        fprintf(stderr, "❌ 无法打开pack文件: %s (%s)\n", pack_file_path, strerror(errno));
        goto cleanup;
    }

    fprintf(stderr, "📦 开始解析pack文件: %s\n", pack_file_path);

    /* 逐行解析作业 */
    while (fgets(line_buffer, sizeof(line_buffer), pack_file)) {
        line_number++;
        
        /* 跳过空行和注释行 */
        if (is_empty_or_comment_line(line_buffer)) {
            continue;
        }

        /* 检查作业数量限制 */
        if (collection->total_jobs_count >= active_config->max_jobs_count) {
            fprintf(stderr, "⚠️  达到最大作业数限制: %d\n", active_config->max_jobs_count);
            break;
        }

        /* 分配新的作业结构 */
        struct submit *job_req = calloc(1, sizeof(struct submit));
        if (!job_req) {
            fprintf(stderr, "❌ 作业内存分配失败，行号: %d\n", line_number);
            if (active_config->fail_on_first_error) {
                goto cleanup;
            }
            continue;
        }

        /* 解析作业行 */
        if (parse_job_line(line_buffer, line_number, job_req) == 0) {
            /* 添加到集合 */
            if (job_collector_add_job_to_collection(collection, job_req, line_number, line_buffer) == 0) {
                valid_jobs_collected++;
                if (active_config->enable_detailed_logging) {
                    fprintf(stderr, "✅ 作业 %d 解析成功: %s\n", line_number, 
                            job_req->jobName ? job_req->jobName : "unnamed");
                }
            } else {
                free(job_req);
                collection->error_jobs_count++;
            }
        } else {
            free(job_req);
            collection->error_jobs_count++;
            fprintf(stderr, "❌ 作业解析失败，行号: %d\n", line_number);
            
            if (active_config->fail_on_first_error) {
                goto cleanup;
            }
        }
    }

    collection->collection_end_time = time(NULL);
    result = valid_jobs_collected;

    fprintf(stderr, "📊 作业收集完成: 总计 %d 行，有效 %d 个，错误 %d 个\n", 
            line_number, valid_jobs_collected, collection->error_jobs_count);

cleanup:
    if (pack_file) {
        fclose(pack_file);
    }
    
    if (result < 0) {
        job_collector_free_collection(collection);
    }
    
    return result;
}

/**
 * @brief 添加作业到集合
 */
int job_collector_add_job_to_collection(batch_job_collection_t *collection,
                                        struct submit *job_request,
                                        int line_number,
                                        const char *original_command)
{
    if (!collection || !job_request) {
        return -1;
    }

    /* 检查是否需要扩展容量 */
    if (collection->total_jobs_count >= collection->total_jobs_capacity) {
        int new_capacity = collection->total_jobs_capacity * JOB_COLLECTOR_GROWTH_FACTOR;
        if (expand_job_capacity(collection, new_capacity) != 0) {
            return -1;
        }
    }

    /* 添加作业到数组 */
    int job_index = collection->total_jobs_count;
    collection->job_requests_array[job_index] = job_request;
    collection->job_line_numbers[job_index] = line_number;

    /* 保存原始命令行 */
    if (collection->collect_original_commands && original_command) {
        size_t command_length = strlen(original_command) + 1;
        collection->original_command_lines[job_index] = malloc(command_length);
        if (collection->original_command_lines[job_index]) {
            strcpy(collection->original_command_lines[job_index], original_command);
            collection->total_memory_used += command_length;
        }
    }

    collection->total_jobs_count++;
    collection->valid_jobs_count++;

    return 0;
}

/**
 * @brief 释放作业集合资源
 */
void job_collector_free_collection(batch_job_collection_t *collection)
{
    if (!collection) {
        return;
    }

    /* 释放作业数组 */
    if (collection->job_requests_array) {
        for (int i = 0; i < collection->total_jobs_count; i++) {
            if (collection->job_requests_array[i]) {
                free(collection->job_requests_array[i]);
            }
        }
        free(collection->job_requests_array);
    }

    /* 释放其他数组 */
    if (collection->job_line_numbers) {
        free(collection->job_line_numbers);
    }

    if (collection->original_command_lines) {
        for (int i = 0; i < collection->total_jobs_count; i++) {
            if (collection->original_command_lines[i]) {
                free(collection->original_command_lines[i]);
            }
        }
        free(collection->original_command_lines);
    }

    memset(collection, 0, sizeof(batch_job_collection_t));
}

/* ==================== 内部函数实现 ==================== */

/**
 * @brief 解析作业行
 */
static int parse_job_line(const char *line, int line_number, struct submit *job_req)
{
    char **argv;
    int argc;
    char temp_line[JOB_COLLECTOR_MAX_LINE_LENGTH];
    
    /* 复制行内容以便修改 */
    strncpy(temp_line, line, sizeof(temp_line) - 1);
    temp_line[sizeof(temp_line) - 1] = '\0';
    
    /* 移除换行符 */
    char *newline = strchr(temp_line, '\n');
    if (newline) {
        *newline = '\0';
    }
    
    /* 分割命令行 */
    argv = split_commandline(temp_line, &argc);
    if (!argv || argc < 1) {
        return -1;
    }
    
    /* 调用现有的fillReq函数解析参数 */
    optind = 1;  /* 重置getopt */
    int result = fillReq(argc, argv, CMD_BSUB, job_req, TRUE);
    
    /* 清理argv */
    if (argv) {
        for (int i = 0; i < argc; i++) {
            if (argv[i]) {
                free(argv[i]);
            }
        }
        free(argv);
    }
    
    return result;
}

/**
 * @brief 扩展作业数组容量
 */
static int expand_job_capacity(batch_job_collection_t *collection, int new_capacity)
{
    if (!collection || new_capacity <= collection->total_jobs_capacity) {
        return -1;
    }

    /* 重新分配作业数组 */
    struct submit **new_jobs = realloc(collection->job_requests_array, 
                                       new_capacity * sizeof(struct submit*));
    if (!new_jobs) {
        return -1;
    }
    collection->job_requests_array = new_jobs;

    /* 重新分配行号数组 */
    int *new_line_numbers = realloc(collection->job_line_numbers, 
                                    new_capacity * sizeof(int));
    if (!new_line_numbers) {
        return -1;
    }
    collection->job_line_numbers = new_line_numbers;

    /* 重新分配命令行数组 */
    char **new_command_lines = realloc(collection->original_command_lines, 
                                       new_capacity * sizeof(char*));
    if (!new_command_lines) {
        return -1;
    }
    collection->original_command_lines = new_command_lines;

    /* 初始化新分配的空间 */
    for (int i = collection->total_jobs_capacity; i < new_capacity; i++) {
        collection->job_requests_array[i] = NULL;
        collection->job_line_numbers[i] = 0;
        collection->original_command_lines[i] = NULL;
    }

    collection->total_jobs_capacity = new_capacity;
    return 0;
}

/**
 * @brief 检查是否为空行或注释行
 */
static bool is_empty_or_comment_line(const char *line)
{
    if (!line) {
        return true;
    }

    /* 跳过空白字符 */
    while (*line && isspace(*line)) {
        line++;
    }

    /* 空行或以#开头的注释行 */
    return (*line == '\0' || *line == '#');
}
```

### 3.4 创建 `lsbatch/cmd/src/batch_transmitter.c`

**核心功能**: 将收集的作业转换为网络请求并发送

```c
/**
 * @file batch_transmitter.c
 * @brief 批量传输器实现
 */

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <errno.h>
#include <time.h>

#include "../include/batch_transmitter.h"
#include "../../lib/lsb.h"
#include "../../lsf/lib/lib.hdr.h"
#include "../../lsf/lib/lproto.h"

/* 批量传输器实现 */
struct batch_transmitter {
    /* 传输统计 */
    transmit_statistics_t statistics;
    time_t start_time;
    
    /* 回调函数 */
    transmit_progress_callback_t progress_callback;
    transmit_error_callback_t error_callback;
    transmit_complete_callback_t complete_callback;
    void *user_data;
    
    /* 状态标志 */
    bool is_complete;
};

/* 内部函数声明 */
static struct submit** convert_simple_jobs_to_submit_array(const simple_job_collection_t *collection);
static struct submitReq* convert_submit_array_to_submitReq(struct submit **submit_array, int count);
static void cleanup_submit_array(struct submit **submit_array, int count);

/**
 * @brief 创建批量传输器
 */
batch_transmitter_t* batch_transmitter_create(void)
{
    batch_transmitter_t *transmitter = calloc(1, sizeof(batch_transmitter_t));
    if (!transmitter) {
        return NULL;
    }
    
    transmitter->start_time = time(NULL);
    transmitter->is_complete = false;
    
    fprintf(stderr, "✅ 批量传输器创建成功\n");
    return transmitter;
}

/**
 * @brief 创建默认传输配置
 */
void batch_transmitter_create_default_config(transmit_config_t *config)
{
    if (!config) {
        return;
    }
    
    memset(config, 0, sizeof(transmit_config_t));
    config->connection_timeout_seconds = 300;
    config->retry_count = 3;
    config->batch_size = 100;
    config->enable_compression = false;
}

/**
 * @brief 执行批量传输
 */
int batch_transmitter_execute(batch_transmitter_t *transmitter,
                             const simple_job_collection_t *collection,
                             struct lsfAuth *auth)
{
    if (!transmitter || !collection || !auth) {
        return -1;
    }
    
    fprintf(stderr, "🚀 开始批量传输，作业数: %d\n", collection->total_jobs_count);
    
    /* 1. 转换数据结构：simple_job_collection_t -> struct submit** */
    struct submit **submit_array = convert_simple_jobs_to_submit_array(collection);
    if (!submit_array) {
        fprintf(stderr, "❌ 数据结构转换失败\n");
        return -1;
    }
    
    /* 2. 转换为submitReq数组 */
    struct submitReq *req_array = convert_submit_array_to_submitReq(
        submit_array, collection->total_jobs_count);
    
    if (!req_array) {
        fprintf(stderr, "❌ submitReq转换失败\n");
        cleanup_submit_array(submit_array, collection->total_jobs_count);
        return -1;
    }
    
    /* 3. 构建批量请求 */
    struct batchSubmitReq batch_req;
    memset(&batch_req, 0, sizeof(batch_req));
    
    batch_req.jobCount = collection->total_jobs_count;
    batch_req.options = 0;
    batch_req.maxConcurrency = 10;
    batch_req.clientTimestamp = time(NULL);
    batch_req.sourceFile = "bsub-pack";
    batch_req.batchName = "batch_jobs";
    batch_req.jobs = req_array;
    
    /* 4. 发送批量请求 */
    struct batchSubmitReply batch_reply;
    memset(&batch_reply, 0, sizeof(batch_reply));
    
    int result = send_batch_pack(&batch_req, &batch_reply, auth);
    
    /* 5. 更新统计信息 */
    transmitter->statistics.total_jobs = collection->total_jobs_count;
    if (result >= 0) {
        transmitter->statistics.successful_jobs = batch_reply.successCount;
        transmitter->statistics.failed_jobs = batch_reply.failureCount;
    } else {
        transmitter->statistics.failed_jobs = collection->total_jobs_count;
    }
    
    transmitter->statistics.end_time = time(NULL);
    transmitter->is_complete = true;
    
    /* 6. 调用完成回调 */
    if (transmitter->complete_callback) {
        transmitter->complete_callback(result >= 0, &transmitter->statistics, transmitter->user_data);
    }
    
    /* 7. 清理资源 */
    if (batch_reply.results) {
        for (int i = 0; i < batch_reply.totalJobs; i++) {
            if (batch_reply.results[i].errorMsg) {
                free(batch_reply.results[i].errorMsg);
            }
            if (batch_reply.results[i].queue) {
                free(batch_reply.results[i].queue);
            }
        }
        free(batch_reply.results);
    }
    
    cleanup_submit_array(submit_array, collection->total_jobs_count);
    
    fprintf(stderr, "✅ 批量传输完成，成功: %zu，失败: %zu\n", 
            transmitter->statistics.successful_jobs, transmitter->statistics.failed_jobs);
    
    return result;
}

/**
 * @brief 设置进度回调
 */
void batch_transmitter_set_progress_callback(batch_transmitter_t *transmitter, 
                                            transmit_progress_callback_t callback, 
                                            void *user_data)
{
    if (transmitter) {
        transmitter->progress_callback = callback;
        transmitter->user_data = user_data;
    }
}

/**
 * @brief 设置错误回调
 */
void batch_transmitter_set_error_callback(batch_transmitter_t *transmitter, 
                                         transmit_error_callback_t callback, 
                                         void *user_data)
{
    if (transmitter) {
        transmitter->error_callback = callback;
        transmitter->user_data = user_data;
    }
}

/**
 * @brief 设置完成回调
 */
void batch_transmitter_set_completion_callback(batch_transmitter_t *transmitter, 
                                              transmit_complete_callback_t callback, 
                                              void *user_data)
{
    if (transmitter) {
        transmitter->complete_callback = callback;
        transmitter->user_data = user_data;
    }
}

/**
 * @brief 检查是否完成
 */
bool batch_transmitter_is_complete(const batch_transmitter_t *transmitter)
{
    return transmitter ? transmitter->is_complete : true;
}

/**
 * @brief 处理响应（占位符）
 */
int batch_transmitter_process_responses(batch_transmitter_t *transmitter)
{
    /* 在流式传输实现中，这里会处理服务器的流式响应 */
    return 0;
}

/**
 * @brief 获取统计信息
 */
void batch_transmitter_get_statistics(const batch_transmitter_t *transmitter, 
                                     transmit_statistics_t *stats)
{
    if (transmitter && stats) {
        *stats = transmitter->statistics;
    }
}

/**
 * @brief 清理传输器
 */
void batch_transmitter_cleanup(batch_transmitter_t *transmitter)
{
    if (transmitter) {
        free(transmitter);
        fprintf(stderr, "🧹 批量传输器清理完成\n");
    }
}

/* ==================== 内部函数实现 ==================== */

/**
 * @brief 转换simple_job_collection_t到struct submit**数组
 */
static struct submit** convert_simple_jobs_to_submit_array(const simple_job_collection_t *collection)
{
    if (!collection || collection->total_jobs_count <= 0) {
        return NULL;
    }
    
    struct submit **submit_array = calloc(collection->total_jobs_count, sizeof(struct submit*));
    if (!submit_array) {
        return NULL;
    }
    
    for (int i = 0; i < collection->total_jobs_count; i++) {
        submit_array[i] = collection->jobs[i];
    }
    
    return submit_array;
}

/**
 * @brief 转换struct submit**到struct submitReq*数组
 */
static struct submitReq* convert_submit_array_to_submitReq(struct submit **submit_array, int count)
{
    if (!submit_array || count <= 0) {
        return NULL;
    }
    
    struct submitReq *req_array = calloc(count, sizeof(struct submitReq));
    if (!req_array) {
        return NULL;
    }
    
    /* 这里需要实现submit到submitReq的转换逻辑 */
    /* 可以参考现有的LSF代码中的转换函数 */
    
    return req_array;
}

/**
 * @brief 清理submit数组
 */
static void cleanup_submit_array(struct submit **submit_array, int count)
{
    if (submit_array) {
        free(submit_array);
    }
}
```

---

## 第四阶段：客户端主控制逻辑

### 4.1 新建 `lsbatch/cmd/cmd.sub.c` 中的 `do_pack_sub` 函数

**位置**: 新建 `do_pack_sub` 函数

```c
void
do_pack_sub (int option, char **argv, struct submit *req)
{
    static char fname[] = "do_pack_sub";
    
    /* 检查pack功能是否启用 */
    if (lsbMaxPackJobs == DEFAULT_LSB_MAX_PACK_JOBS) {
        fprintf(stderr, "Pack submission disabled by LSB_MAX_PACK_JOBS in lsf.conf. Job not submitted.\n");
        return;
    }
    
    fprintf(stderr, "🚀 开始批量作业提交流程...\n");
    
    /* 1. 初始化作业收集器 */
    batch_job_collection_t job_collection;
    job_collector_config_t collector_config;
    
    job_collector_create_default_config(&collector_config);
    collector_config.enable_detailed_logging = true;
    collector_config.max_jobs_count = lsbMaxPackJobs;
    
    /* 2. 从pack文件收集作业 */
    fprintf(stderr, "📦 正在收集批量作业，文件: %s\n", req->packFile);
    
    int collected_jobs = job_collector_collect_from_pack_file(req->packFile, 
                                                             &job_collection, 
                                                             &collector_config);
    
    if (collected_jobs <= 0) {
        fprintf(stderr, "❌ 作业收集失败: %d\n", collected_jobs);
        job_collector_free_collection(&job_collection);
        return;
    }
    
    fprintf(stderr, "✅ 作业收集完成：收集了 %d 个有效作业\n", collected_jobs);
    
    /* 3. 验证作业数量限制 */
    if (collected_jobs > lsbMaxPackJobs) {
        fprintf(stderr, "❌ 作业数量(%d)超过系统配置的限制(%d)\n", 
                collected_jobs, lsbMaxPackJobs);
        job_collector_free_collection(&job_collection);
        return;
    }
    
    /* 4. 创建批量传输器 */
    batch_transmitter_t *transmitter = batch_transmitter_create();
    if (!transmitter) {
        fprintf(stderr, "❌ 创建批量传输器失败\n");
        job_collector_free_collection(&job_collection);
        return;
    }
    
    /* 5. 设置回调函数 */
    batch_transmitter_set_progress_callback(transmitter, NULL, NULL);
    batch_transmitter_set_error_callback(transmitter, NULL, NULL);
    batch_transmitter_set_completion_callback(transmitter, NULL, NULL);
    
    /* 6. 准备作业集合 */
    simple_job_collection_t simple_collection;
    simple_collection.total_jobs_count = job_collection.total_jobs_count;
    simple_collection.jobs = job_collection.job_requests_array;
    
    /* 7. 执行批量传输 */
    fprintf(stderr, "📤 执行批量作业提交...\n");
    
    struct lsfAuth auth;
    if (authTicketTokens_(&auth, NULL) < 0) {
        fprintf(stderr, "❌ LSF认证失败\n");
        batch_transmitter_cleanup(transmitter);
        job_collector_free_collection(&job_collection);
        return;
    }
    
    int result = batch_transmitter_execute(transmitter, &simple_collection, &auth);
    
    /* 8. 处理结果 */
    if (result >= 0) {
        transmit_statistics_t stats;
        batch_transmitter_get_statistics(transmitter, &stats);
        
        fprintf(stderr, "✅ 批量提交完成！\n");
        fprintf(stderr, "   总作业数: %zu\n", stats.total_jobs);
        fprintf(stderr, "   成功提交: %zu\n", stats.successful_jobs);
        fprintf(stderr, "   提交失败: %zu\n", stats.failed_jobs);
        
        if (stats.successful_jobs > 0) {
            fprintf(stderr, "   成功率: %.1f%%\n", 
                    (double)stats.successful_jobs / stats.total_jobs * 100.0);
        }
    } else {
        fprintf(stderr, "❌ 批量提交失败: %s\n", lsb_sysmsg());
    }
    
    /* 9. 清理资源 */
    batch_transmitter_cleanup(transmitter);
    job_collector_free_collection(&job_collection);
    
    fprintf(stderr, "🧹 批量提交流程完成\n");
}
```

---

## 第五阶段：服务器端批量处理

### 5.1 修改 `lsbatch/lib/lsb.sub.c`

**位置**: 在文件末尾添加批量处理函数

```c
/**
 * @brief 批量作业提交函数 - 传统模式
 */
int send_batch_pack(struct batchSubmitReq *batchReq, 
                   struct batchSubmitReply *batchReply,
                   struct lsfAuth *auth)
{
    static char fname[] = "send_batch_pack";
    char *request_buf = NULL;
    char *reply_buf = NULL;
    XDR xdrs;
    struct LSFHeader hdr;
    int reqBufSize, cc;
    char *masterHost = NULL;
    
    fprintf(stderr, "🚀 [%s] 开始批量提交，作业数: %d\n", fname, batchReq->jobCount);
    
    /* 参数验证 */
    if (!batchReq || !batchReply || !auth) {
        fprintf(stderr, "❌ %s: 关键参数为NULL\n", fname);
        lsberrno = LSBE_BAD_ARG;
        return -1;
    }
    
    if (batchReq->jobCount <= 0 || !batchReq->jobs) {
        fprintf(stderr, "❌ %s: 无效的作业数据\n", fname);
        lsberrno = LSBE_BAD_ARG;
        return -1;
    }
    
    /* 获取主节点信息 */
    masterHost = ls_getmastername();
    if (!masterHost) {
        fprintf(stderr, "❌ %s: 无法获取主节点信息\n", fname);
        lsberrno = LSBE_LSBLIB;
        return -1;
    }
    
    /* 分配请求缓冲区 */
    reqBufSize = 1024 * 1024;  /* 1MB缓冲区 */
    request_buf = malloc(reqBufSize);
    if (!request_buf) {
        fprintf(stderr, "❌ %s: 请求缓冲区分配失败\n", fname);
        lsberrno = LSBE_NO_MEM;
        return -1;
    }
    
    /* XDR编码请求 */
    xdrmem_create(&xdrs, request_buf, reqBufSize, XDR_ENCODE);
    initLSFHeader_(&hdr);
    hdr.opCode = BATCH_PACK_JOB_SUB;
    
    if (!xdr_encodeMsg(&xdrs, (char*)batchReq, &hdr, xdr_batchSubmitReq, 0, auth)) {
        fprintf(stderr, "❌ %s: XDR编码失败\n", fname);
        lsberrno = LSBE_XDR;
        xdr_destroy(&xdrs);
        free(request_buf);
        return -1;
    }
    
    int request_size = XDR_GETPOS(&xdrs);
    xdr_destroy(&xdrs);
    
    fprintf(stderr, "✅ %s: XDR编码成功，大小: %d 字节\n", fname, request_size);
    
    /* 发送请求到MBD */
    cc = callmbd(masterHost, request_buf, request_size, &reply_buf, &hdr, NULL, NULL, 0);
    free(request_buf);
    
    if (cc < 0) {
        fprintf(stderr, "❌ %s: 网络通信失败\n", fname);
        return -1;
    }
    
    /* 解码响应 */
    xdrmem_create(&xdrs, reply_buf, cc, XDR_DECODE);
    
    if (!xdr_batchSubmitReply(&xdrs, batchReply, &hdr)) {
        fprintf(stderr, "❌ %s: 响应解码失败\n", fname);
        lsberrno = LSBE_XDR;
        xdr_destroy(&xdrs);
        return -1;
    }
    
    xdr_destroy(&xdrs);
    
    fprintf(stderr, "✅ %s: 批量提交完成，成功: %d，失败: %d\n", 
            fname, batchReply->successCount, batchReply->failureCount);
    
    return batchReply->successCount;
}

/**
 * @brief 流式批量提交函数 - 简化实现
 */
int send_batch_pack_streaming(struct batchSubmitReq *batchReq, 
                             void *response_processor,
                             struct lsfAuth *auth)
{
    static char fname[] = "send_batch_pack_streaming";
    
    fprintf(stderr, "🚀 [%s] 开始流式批量发送，作业数: %d\n", fname, batchReq->jobCount);
    
    /* 暂时使用传统的批量提交方式 */
    struct batchSubmitReply batchReply;
    memset(&batchReply, 0, sizeof(batchReply));
    
    int result = send_batch_pack(batchReq, &batchReply, auth);
    
    if (result >= 0) {
        fprintf(stderr, "✅ [%s] 批量提交完成！成功: %d, 失败: %d\n", 
                fname, batchReply.successCount, batchReply.failureCount);
        result = batchReply.successCount;
    } else {
        fprintf(stderr, "❌ [%s] 批量提交失败\n", fname);
    }
    
    /* 清理响应结果 */
    if (batchReply.results) {
        for (int i = 0; i < batchReply.totalJobs; i++) {
            if (batchReply.results[i].errorMsg) {
                free(batchReply.results[i].errorMsg);
            }
            if (batchReply.results[i].queue) {
                free(batchReply.results[i].queue);
            }
        }
        free(batchReply.results);
    }
    
    return result;
}
```

### 5.2 创建 `lsbatch/daemons/mbd/mbd.batch.c`

**新文件**: 服务器端批量处理逻辑

```c
/**
 * @file mbd.batch.c
 * @brief MBD批量作业处理模块
 */

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <time.h>

#include "mbd.h"
#include "../../lib/lsb.h"

/* 内部函数声明 */
static int process_single_job(struct submitReq *jobReq, int jobIndex, 
                             struct batchJobResult *result);
static void send_stream_result(int client_fd, struct streamJobResult *result);
static void send_batch_complete_signal(int client_fd, struct batchCompleteSignal *signal);

/**
 * @brief 处理批量作业提交请求
 */
int do_batch_submit(struct batchSubmitReq *batchReq, 
                   struct LSFHeader *reqHdr,
                   struct batchSubmitReply *batchReply,
                   int client_fd)
{
    static char fname[] = "do_batch_submit";
    int i, successCount = 0, failureCount = 0;
    time_t startTime = time(NULL);
    
    fprintf(stderr, "🚀 [%s] 开始处理批量作业，数量: %d\n", fname, batchReq->jobCount);
    
    /* 参数验证 */
    if (!batchReq || !batchReply || batchReq->jobCount <= 0) {
        fprintf(stderr, "❌ [%s] 无效参数\n", fname);
        return -1;
    }
    
    /* 初始化响应结构 */
    memset(batchReply, 0, sizeof(struct batchSubmitReply));
    batchReply->totalJobs = batchReq->jobCount;
    batchReply->results = calloc(batchReq->jobCount, sizeof(struct batchJobResult));
    
    if (!batchReply->results) {
        fprintf(stderr, "❌ [%s] 内存分配失败\n", fname);
        return -1;
    }
    
    /* 逐个处理作业 */
    for (i = 0; i < batchReq->jobCount; i++) {
        struct submitReq *jobReq = &batchReq->jobs[i];
        struct batchJobResult *result = &batchReply->results[i];
        
        fprintf(stderr, "🔄 [%s] 处理作业 %d/%d\n", fname, i+1, batchReq->jobCount);
        
        /* 处理单个作业 */
        int jobResult = process_single_job(jobReq, i, result);
        
        if (jobResult > 0) {
            successCount++;
            fprintf(stderr, "✅ [%s] 作业 %d 提交成功，JobID: %lld\n", 
                    fname, i+1, result->jobId);
        } else {
            failureCount++;
            fprintf(stderr, "❌ [%s] 作业 %d 提交失败: %s\n", 
                    fname, i+1, result->errorMsg ? result->errorMsg : "未知错误");
        }
        
        /* 如果是流式模式，立即发送结果 */
        if (client_fd > 0) {
            struct streamJobResult streamResult;
            memset(&streamResult, 0, sizeof(streamResult));
            
            streamResult.batchId = reqHdr->refCode;
            streamResult.jobIndex = i;
            streamResult.jobId = result->jobId;
            streamResult.resultCode = jobResult > 0 ? 0 : -1;
            streamResult.errorMessage = result->errorMsg;
            streamResult.queueName = result->queue;
            streamResult.submitTime = startTime;
            streamResult.processTime = time(NULL);
            
            send_stream_result(client_fd, &streamResult);
        }
    }
    
    /* 更新统计信息 */
    batchReply->successCount = successCount;
    batchReply->failureCount = failureCount;
    
    /* 发送批量完成信号 */
    if (client_fd > 0) {
        struct batchCompleteSignal completeSignal;
        memset(&completeSignal, 0, sizeof(completeSignal));
        
        completeSignal.batchId = reqHdr->refCode;
        completeSignal.totalJobs = batchReq->jobCount;
        completeSignal.successCount = successCount;
        completeSignal.failureCount = failureCount;
        completeSignal.startTime = startTime;
        completeSignal.completionTime = time(NULL);
        
        char summary[256];
        snprintf(summary, sizeof(summary),
                "批量提交完成: 总计%d个作业，成功%d个，失败%d个",
                batchReq->jobCount, successCount, failureCount);
        completeSignal.summaryMessage = summary;

        send_batch_complete_signal(client_fd, &completeSignal);
    }

    fprintf(stderr, "✅ [%s] 批量处理完成: 成功%d个，失败%d个\n",
            fname, successCount, failureCount);

    return successCount > 0 ? successCount : -1;
}
```

---

## 🔧 关键技术细节和优化策略

### 💾 内存管理策略
```c
/* 内存池管理 - 减少频繁的malloc/free */
typedef struct batch_memory_pool {
    void **blocks;
    size_t *block_sizes;
    int block_count;
    int capacity;
    size_t total_allocated;
    size_t max_memory_limit;
} batch_memory_pool_t;

/* 内存池初始化 */
int batch_memory_pool_init(batch_memory_pool_t *pool, size_t max_limit);
void* batch_memory_pool_alloc(batch_memory_pool_t *pool, size_t size);
void batch_memory_pool_cleanup(batch_memory_pool_t *pool);
```

### 🔄 错误处理和恢复机制
```c
/* 错误类型定义 */
typedef enum {
    BATCH_ERROR_NONE = 0,
    BATCH_ERROR_NETWORK = 1,
    BATCH_ERROR_MEMORY = 2,
    BATCH_ERROR_PARSE = 3,
    BATCH_ERROR_SUBMIT = 4,
    BATCH_ERROR_TIMEOUT = 5,
    BATCH_ERROR_QUOTA = 6
} batch_error_type_t;

/* 错误恢复策略 */
typedef struct {
    batch_error_type_t error_type;
    int retry_count;
    int max_retries;
    time_t last_retry_time;
    bool should_continue;
} batch_error_context_t;
```

### 📊 性能监控和统计
```c
/* 性能统计结构 */
typedef struct {
    time_t start_time;
    time_t end_time;
    int total_jobs;
    int successful_jobs;
    int failed_jobs;
    size_t total_bytes_sent;
    size_t total_bytes_received;
    double avg_job_process_time;
    double network_throughput;
} batch_performance_stats_t;
```

### 🌐 网络优化策略
```c
/* 网络传输优化配置 */
typedef struct {
    int tcp_nodelay;           /* 禁用Nagle算法 */
    int socket_buffer_size;    /* Socket缓冲区大小 */
    int connection_timeout;    /* 连接超时时间 */
    int read_timeout;          /* 读取超时时间 */
    int write_timeout;         /* 写入超时时间 */
    bool enable_compression;   /* 启用数据压缩 */
    int compression_level;     /* 压缩级别 */
} network_optimization_config_t;
```

---

## 🧪 测试和验证方案

### 单元测试计划
```bash
# 1. XDR协议测试
./test_xdr_batch_submit
./test_xdr_memory_management

# 2. 作业收集器测试
./test_job_collector_basic
./test_job_collector_large_files
./test_job_collector_error_handling

# 3. 批量传输器测试
./test_batch_transmitter_basic
./test_batch_transmitter_network_failure
./test_batch_transmitter_partial_failure

# 4. 服务器端处理测试
./test_mbd_batch_processing
./test_mbd_concurrent_batches
```

### 集成测试方案
```bash
# 1. 小批量测试（10个作业）
bsub -pack small_batch.pack

# 2. 中等批量测试（100个作业）
bsub -pack medium_batch.pack

# 3. 大批量测试（1000个作业）
bsub -pack large_batch.pack

# 4. 极限测试（10000个作业）
bsub -pack extreme_batch.pack

# 5. 错误场景测试
bsub -pack error_scenarios.pack
```

### 性能基准测试
```bash
# 性能对比测试脚本
#!/bin/bash

# 传统方式提交1000个作业
time for i in {1..1000}; do
    bsub sleep 1
done

# 批量方式提交1000个作业
time bsub -pack 1000_jobs.pack

# 测试指标：
# - 总提交时间
# - 网络连接数
# - 内存使用量
# - CPU使用率
# - 服务器负载
```

---

## 🚀 部署和配置指南

### 配置参数
```bash
# LSF配置文件 lsf.conf 新增参数
LSB_BATCH_SUBMIT_ENABLED=Y              # 启用批量提交
LSB_BATCH_MAX_JOBS=10000                # 单次批量最大作业数
LSB_BATCH_MEMORY_LIMIT=100MB            # 批量处理内存限制
LSB_BATCH_TIMEOUT=300                   # 批量处理超时时间(秒)
LSB_BATCH_ENABLE_COMPRESSION=Y          # 启用网络压缩
LSB_BATCH_LOG_LEVEL=INFO                # 批量处理日志级别
```

### 向后兼容性保证
1. **API兼容性**: 现有的 `lsb_submit()` 函数保持不变
2. **协议兼容性**: 新增的XDR函数不影响现有协议
3. **配置兼容性**: 新配置参数都有默认值，不影响现有部署
4. **客户端兼容性**: `-pack` 参数为新增选项，不影响现有用法

### 升级路径
1. **阶段1**: 升级服务器端MBD，支持新协议但保持向后兼容
2. **阶段2**: 升级客户端，添加批量提交功能
3. **阶段3**: 逐步启用批量提交功能和相关优化
4. **阶段4**: 性能调优和监控部署

---

## 📋 完整实施计划

### 第一阶段：基础架构 (2周)
- [x] 数据结构设计和定义
- [x] XDR协议实现
- [x] 基础内存管理机制
- [ ] 单元测试框架搭建

### 第二阶段：客户端实现 (3周)
- [x] 作业收集器实现
- [x] 批量传输器实现
- [x] bsub命令行集成
- [ ] 错误处理和恢复机制
- [ ] 客户端测试

### 第三阶段：服务器端实现 (3周)
- [ ] MBD守护进程修改
- [ ] 批量作业处理逻辑
- [ ] 流式响应机制
- [ ] 服务器端测试

### 第四阶段：集成和优化 (2周)
- [ ] 端到端集成测试
- [ ] 性能优化和调优
- [ ] 文档编写
- [ ] 部署准备

### 第五阶段：测试和发布 (2周)
- [ ] 全面测试验证
- [ ] 性能基准测试
- [ ] 生产环境部署
- [ ] 监控和维护

---

## 🎯 成功标准

### 功能标准
- ✅ 支持 `bsub -pack filename` 命令
- ✅ 一次连接提交多个作业
- ✅ 流式响应机制
- ✅ 完整的错误处理
- ✅ 向后兼容性

### 性能标准
- 🎯 批量提交比传统方式快80%以上
- 🎯 支持单次提交10000个作业
- 🎯 内存使用量控制在100MB以内
- 🎯 网络连接数减少99%以上
- 🎯 服务器负载降低50%以上

### 质量标准
- 🎯 单元测试覆盖率90%以上
- 🎯 集成测试通过率100%
- 🎯 内存泄漏零容忍
- 🎯 生产环境稳定运行30天以上

---

## 📚 参考资料和依赖

### LSF相关文档
- LSF Administrator's Guide
- LSF User's Guide
- LSF Developer's Guide
- LSF XDR Protocol Specification

### 技术依赖
- XDR (External Data Representation)
- TCP/IP Socket编程
- POSIX线程库
- 标准C库函数

### 开发工具
- GCC编译器
- GDB调试器
- Valgrind内存检查
- Strace系统调用跟踪

---

**🎉 计划书完善完成！现在可以按照这个详细的实施方案开始开发了！**