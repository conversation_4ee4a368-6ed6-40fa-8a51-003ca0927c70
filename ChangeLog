2025-06-16  <PERSON><PERSON> <<EMAIL>>
            <PERSON> <<EMAIL>>
            <PERSON><PERSON><PERSON> <<EMAIL>>

	* release volclava 2.0.0.

	* Summary of changes:
	1. support fairshare scheduling policy for users at queue level;
	2. support customize unit by configure LSF_UNIT_FOR_LIMITS in lsf.conf;
	3. support conf MAX_PEND_JOBS and MAX_PEND_SLOTS in lsb.users and lsb.params;
	4. support conf DEFAULT_LIMIT_IGNORE_USER_GROUP and SUB_TRY_INTERVAL in lsb.params;
	5. support resource reserve as per-host;
	6. fix loadstop not effect job scheduling;
	7. fix memory leak in bhosts and putEnv();
	8. fix pim hang when format of pim.info file is not in volclava format;
	9. fix sbd hang due to unnecessary popen;
	10. fix lsload -w does not display full hostname;
	11. fix mbatchd coredump when only suspend jobs remain;
	12. fix mem be reserved repeatedly after bresume ususp/ssusp job;
	13. fix running jobs exceed the number of slots due to shared type resource when slotResourceReserve=y;

2025-01-06  <PERSON><PERSON>  <<EMAIL>>
            <PERSON> <PERSON> <<EMAIL>>

	* release volclava 1.0.1.

	* Summary of changes:
	1. bugfix: revert check for tcl result with function in tcl 8.6;
	2. configure: set default limit from cpu to cpu>=0 in lsf.task;
	3. update year of Copyright from 2021-2024 to 2021-2025;
	4. bsub: enhance parameter check of bsub -R;
	5. make source-script active in multiple volclava on the same host;
	6. make install script support ubuntu OS;

2024-11-11  Mingze Li  <<EMAIL>>

	* re-open as volclava 1.0.

	* Summary of changes:
	1. feature support:
        - bjobs -UF;
        - bjobs -o/-json;
        - bsub -pack;
        - bsub -Ep;
	2. bugfix:
	    - MXJ not equal with maxCpus when set "!";
	    - lshosts -l segmentation fault;
	    - sbatchd block by greater than 1000 jobs;
	    - prefix not work in rpm install to costomize directory;
	    - fix job slot limit reached while host is free;
	3. define new project name as volclava in related files.
	4. adapt to Ubuntu 20.04 and Rocky 8.10.

2012-01-23  David Bigagli  <<EMAIL>>

	* Created 2.0 branch.

2011-11-07  David Bigagli  <<EMAIL>>

	* lib.host.c Rewrote the caching code to read the hosts file only
	once and add the host in the cache. Removed the cache timeout
	and simplified the entire implementation to use only two interface
	functions: Gethostbyname_() and Gethostbyaddr_().
	Change the old C types u_int and casta to char *
	into the current linux types whenever required.
	Modified the daemons code to use the new host library
	implementation.

2011-10-30  David Bigagli  <<EMAIL>>

	* mbd.policy.c Developed a little round robin scheduler absed on the number of jobs a user has running.

	* openlava.spec  modified the spec file so that autoconf creates
	openlava configuration files and use the outptu variables to make
	the necessary subsititution in the them. Change the post install
	to just erase the package without saving anything.
	Removed the symbolic link as that is something sites have to
	do as they may want to run more versions together, also
	in now the lsf.conf has the version in the openlava
	fundamental variables clearly indicating which version is in use.

2011-09-16  David Bigagli  <<EMAIL>>

	* README removed obsolete information about the source code directory structure.
	Added the README and COPYING file to the rmp file.

2011-09-14  David Bigagli  <<EMAIL>>

	* lsf.h Added the openlava foundation copyright.

2011-09-09  David Bigagli  <<EMAIL>>

	* openlava.spec Updated the spec file to reflect the changes in teh build system. The ownership of all files changed, no longer root, the entire installation tree is owned by the openlava user. The daemons to run in multi user mode must still be started at root which is guaranteed at boot by init.

2011-08-27  David Bigagli  <<EMAIL>>

	* openlavaconfig.h Removed it from the top directory and merge its content into lsf.h which is the openlava master header file. In this way the repository is more GNU like as well.

2011-08-23  David Bigagli  <<EMAIL>>

	* GNU build system. Switched openlava from home made makefiles to the GNU build system.

2011-07-20  David Bigagli  <<EMAIL>>

	* scripts/Makefile: added the installation
	of scripts that manage the integration with
	mpi and openmpi.

2011-07-17  David Bigagli  <<EMAIL>>

	* lib.table.c: Added getClosestPrime() function to
	always size and resize tables as primies.

	* lsbatch/daemons/mbd.init.c (addHost):
	Set the number of CPUs on batch host to be its MXJ.
	If MXJ is not confgiured than use the number of physical
	CPUs as provided by openlava base.


2011-07-16  David Bigagli  <<EMAIL>>

	* Added lsb.hosts

2011-06-16  David Bigagli  <<EMAIL>>

	* Makefiles changed all variables to run install from LAVA
	to OPENLAVA.

	* config/Makefile added a command to install COPYING and README files
	to the LAVA_ROOT/conf directory

2011-06-07  David Bigagli  <<EMAIL>>

	* lava startup script was rewritten, simplified and introduce
	the possibility to run daemons in single user mode.

	* Added a doc directory with original Platform pdf for lava
	administrator and user.

	* lava.spec Rober Stober added a new lava.spec to buidl rpm.

	* Make.misc removed the reference to the architecture x86_64
	and changed the make install to also install the lava sturtup script.


2011-05-02  David Bigagli  <<EMAIL>>

	* Makefile(s): Removed PREFIX variable from all make files and
	introduced Lava specific installation variables in Make.misc.

	* Makefile(s): Made static link for all Lava binaries.

	* Makefiles(s): Changed makefiles not to build shared libraries
	by default and modified the shared libraries to use standard .so
	suffix instead of .So.

2011-04-24  David Bigagli  <<EMAIL>>

	* ChangeLog: Created this file.

	* Make.def: Set the location of TCL header files in /usr/local/include
	instead of /usr/include. TCL_INCLUDE = /usr/local/include

	* Make.def: Added a static link for TCL libtcl8.4.a from
	/usr/local/lib. TCLLIB = /usr/local/lib/libtcl8.4.a

	* Make.def: Removed the -O2 optimization flag and the hash-style
	linker option.
